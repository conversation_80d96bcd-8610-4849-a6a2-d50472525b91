/* eslint-disable sonarjs/different-types-comparison */
/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable unicorn/no-nested-ternary */
/* eslint-disable sonarjs/no-nested-conditional */
/* eslint-disable unicorn/no-array-reduce */
/* eslint-disable security/detect-object-injection */

type Reset<T> = T extends Date
  ? Date
  : T extends unknown[]
    ? []
    : T extends object
      ? { [K in keyof T]: Reset<T[K]> }
      : T extends string
        ? ''
        : T extends number
          ? 0
          : T extends boolean
            ? false
            : T extends undefined
              ? undefined
              : null

export const resetObject = <T extends object>(
  fields?: T | null
): { [K in keyof T]: Reset<T[K]> } | null => {
  if (fields == null) return null

  return (Object.keys(fields) as (keyof T)[]).reduce(
    (acc, curr) => {
      const value = fields[curr]

      const resetValue: Reset<typeof value> =
        value == null
          ? (null as Reset<typeof value>)
          : value === undefined
            ? (undefined as Reset<typeof value>)
            : typeof value === 'boolean'
              ? (false as Reset<typeof value>)
              : typeof value === 'string'
                ? ('' as Reset<typeof value>)
                : typeof value === 'number'
                  ? (0 as Reset<typeof value>)
                  : Array.isArray(value)
                    ? ([] as Reset<typeof value>)
                    : typeof value === 'object'
                      ? (resetObject(value) as Reset<typeof value>)
                      : (null as Reset<typeof value>)

      return { ...acc, [curr]: resetValue }
    },
    {} as { [K in keyof T]: Reset<T[K]> }
  )
}

export const getProps = (json: object) => {
  const jsonStructure = json as {
    optional?: { key: string; value: string }[]
    required?: { key: string; value: string }[]
  }

  return [...(jsonStructure.required ?? []), ...(jsonStructure.optional ?? [])]
}
