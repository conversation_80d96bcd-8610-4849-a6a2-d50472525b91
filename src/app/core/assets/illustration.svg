<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="330.653" height="229.984" viewBox="0 0 330.653 229.984">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" y1="1" x2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="gray" stop-opacity="0.251"/>
      <stop offset="0.54" stop-color="gray" stop-opacity="0.122"/>
      <stop offset="1" stop-color="gray" stop-opacity="0.102"/>
    </linearGradient>
  </defs>
  <g id="Group_1" data-name="Group 1" transform="translate(-420 -259)">
    <circle id="Ellipse_37" data-name="Ellipse 37" cx="3.972" cy="3.972" r="3.972" transform="translate(722.377 259)" fill="#f1f5f9" opacity="0.1"/>
    <ellipse id="Ellipse_38" data-name="Ellipse 38" cx="27.882" cy="2.78" rx="27.882" ry="2.78" transform="translate(620.471 483.424)" fill="#6c63ff" opacity="0.1"/>
    <path id="Path_65" data-name="Path 65" d="M136.394,487.31l-.122,6.6-.98,53.523L126.9,567.077l-28.9.331V490.352l30.166-2.39Z" transform="translate(354.436 -100.91)" fill="#5f5d7e"/>
    <path id="Path_66" data-name="Path 66" d="M136.394,487.31l-.122,6.6-.98,53.523L126.9,567.077l-28.9.331V490.352l30.166-2.39Z" transform="translate(354.436 -100.91)" fill="#334155"/>
    <path id="Path_67" data-name="Path 67" d="M194.819,487.31l-.122,6.6-.98,53.523-8.387,19.641,2.208-73.038-.007-.046-.94-6.031Z" transform="translate(296.011 -100.91)" fill="#475569"/>
    <path id="Path_68" data-name="Path 68" d="M128.166,487.962l8.228-.652-.122,6.6-7.166.079L98,494.34v-3.988Z" transform="translate(354.436 -100.91)" opacity="0.1"/>
    <path id="Path_69" data-name="Path 69" d="M930.981,487.562v76.613h-6.729L897.33,544.538V485.4l24.053-2.261,1.476.679,1.393.642Z" transform="translate(-180.328 -98.121)" fill="#475569"/>
    <rect id="Rectangle_55" data-name="Rectangle 55" width="6.729" height="80.356" transform="translate(743.924 385.698)" fill="#334155"/>
    <path id="Path_70" data-name="Path 70" d="M930.981,483.819v4.4l-6.729.076-26.922.3v-3.2l24.053-2.261,1.476.679,1.393.642v-.642Z" transform="translate(-180.328 -98.121)" opacity="0.1"/>
    <path id="Path_71" data-name="Path 71" d="M330.653,464.929v6.242L0,474.858v-7.944l31.113-6.289,270.745-1.986Z" transform="translate(420 -81.73)" fill="#475569"/>
    <path id="Path_72" data-name="Path 72" d="M330.653,477.64v6.242L0,487.57v-7.944Z" transform="translate(420 -94.441)" fill="#334155"/>
    <rect id="Rectangle_56" data-name="Rectangle 56" width="115.818" height="89.925" rx="22.82" transform="translate(540.975 289.64)" fill="#475569"/>
    <rect id="Rectangle_57" data-name="Rectangle 57" width="97.349" height="73.356" rx="7" transform="translate(550.295 297.752)" fill="#f1f5f9"/>
    <circle id="Ellipse_39" data-name="Ellipse 39" cx="1.595" cy="1.595" r="1.595" transform="translate(544.9 332.833)" fill="#e6e8ec"/>
    <circle id="Ellipse_40" data-name="Ellipse 40" cx="2.631" cy="2.631" r="2.631" transform="translate(649.326 331.969)" fill="#e6e8ec"/>
    <path id="Path_73" data-name="Path 73" d="M620.934,349.638l-12.448-7.566a1.7,1.7,0,0,0-2.525,1.483V358.27a1.7,1.7,0,0,0,2.525,1.483l12.448-7.143a1.7,1.7,0,0,0,0-2.972Zm9.36,1.274a17.542,17.542,0,1,0-17.542,17.542,17.542,17.542,0,0,0,17.542-17.542Zm-31.688,0a14.146,14.146,0,1,1,14.146,14.146,14.146,14.146,0,0,1-14.146-14.146Z" transform="translate(-11.469 -14.925)" fill="#475469"/>
    <path id="Path_74" data-name="Path 74" d="M736.127,405.782a4.14,4.14,0,0,0-.374-1.165c.251-.857.414-1.384.414-1.384a1.93,1.93,0,0,0-.04-.331,5.081,5.081,0,0,0-5.147-4.263c-1.986.036-5.753.169-9.93.639.238-1.671.523-3.671.821-5.729l.546.06a2.6,2.6,0,0,1,.391.02h.175a1.8,1.8,0,0,0,1.211-.371.715.715,0,0,0-.222-1.172A2.952,2.952,0,0,0,726.565,391a2.317,2.317,0,0,0,0-2.694c-.7-.993-2.178-1.655-2.257-2.823a3.449,3.449,0,0,1,.748-1.844,7.7,7.7,0,0,0-1.215-9.251,18.1,18.1,0,0,0-1.47-1.307c-.728-2.674-1.489-5.064-1.714-5.14a5.783,5.783,0,0,1-1.268-1.264,6.084,6.084,0,0,0-.023-1.367c-.351-2.317-2.648-3.972-3.912-6.017a10.388,10.388,0,0,1-1.168-6.894,49.319,49.319,0,0,1,1.807-6.9,8.782,8.782,0,0,0,.354-5.187,9.428,9.428,0,0,0-2.135-3.217,24.318,24.318,0,0,0-7.586-5.958,15.076,15.076,0,0,0-6.716-1.436h-.023l-.364.02-.258.017-.258.026-.275.026h0c-.281.036-.559.079-.837.136a28.9,28.9,0,0,0-6.14-.688h-.268a3.144,3.144,0,0,0-1.754.414c-.735.513-.828,1.433-1.049,2.234a6.2,6.2,0,0,1-2.383,3.31c-.791.569-1.721.993-2.476,1.589a2.522,2.522,0,0,0-1.142,2.37,2.586,2.586,0,0,0,1.655,1.622c-.1.119-.2.235-.288.357.156.063.331.119.473.165.993.3,2.062.331,3.068.6a6.725,6.725,0,0,1,.814.275,11.624,11.624,0,0,0-1.36,5.468,11.78,11.78,0,0,0,6.163,10.333v.331c.1,3.455-.159,7.659-1.926,8.371s-3.459,1.516-4.5,2.036l-.331.175c-.275-.126-.549-.258-.818-.4,0,0-1.407-.166-1.162.907,0,0-4.253-.662-6.587,1.155a60.664,60.664,0,0,0-6.914,6.6c-.751.99-6.56,3.154-6.56,3.154a7.448,7.448,0,0,0,1.039-5.693s-.874-3.78-.457-5.3,1.5-7.143,1.5-7.143.152-5.938-3.088-8.662-5.494-4.965-4.243-2.317a36.912,36.912,0,0,0,1.834,3.545s-7.748,1.84-5,4.713,3.5,2.876,3.5,2.876-.917,7.093-1.086,8.414-1.655,11.383-1.655,11.383-.083,4.786.751,5.527a3.765,3.765,0,0,0,4,2.979s6.58-.166,8.414-.907,8.162-4.7,8.831-4.455a.38.38,0,0,1,.129.086.943.943,0,0,1,.1.1c-.662,1.466-2.231,6.444-1.562,8.89,0,0-.583,6.927,2.916,9.155,0,0,5.081,8.579,3.164,13.2-.152.374-.288.718-.414,1.053-2.909-2.413-7.348-4.634-14.126-5.862-.692-.126-1.38-.278-2.062-.444-1.926-.467-7.225-.894-9.747,7.484a36.849,36.849,0,0,1-2.237,5.667,18.709,18.709,0,0,0-2.118,7.745c-.056,4,2.012,7.973,9.519,9.43a28.567,28.567,0,0,0,1.443,9.079c2.082,5.2,5.415,13.61,5.415,13.61s4,8.579,4.25,12.041a55.272,55.272,0,0,1,.235,7.282,3.231,3.231,0,0,1-.033.364,8.769,8.769,0,0,1-1.119,1.016c-.583.331-2.5,2.393-2.5,2.886a8.82,8.82,0,0,0,.126,1.41,3.474,3.474,0,0,0-1.4,2.515c0,4.475,12.7,8.1,28.365,8.1s28.362-3.641,28.362-8.1c0-4.084-10.592-7.46-24.354-8.016,2.87-1.145,5.418-2.347,6.474-3.31a1.549,1.549,0,0,0,.5-.682L705.5,473.78s-.208.046-.516.136l-.185-.278c-1.185-1.8-3.465-5.4-3.465-6.375,0-.361-.6-2.668-1.476-5.829V451.289c8.692,1.324,19.892-.477,19.892-.477l.05-.026h.218v-.119c1.506-.834,9.039-5.246,11.273-11.1.662-1.708-.162-4.154-1.8-6.769,1.145-2.843,3.972-10.188,3.69-13.117,0-.156-.02-.331-.02-.5a8.635,8.635,0,0,0,.033-.9c.331-4.3,2.979-12.164,2.979-12.164S736.16,405.981,736.127,405.782ZM679.86,386.665c.6,1.059,1.248,2.413,1.5,2.962h0s-.88-1.622-1.486-2.962Zm.629,92.709-.063.023v-.331a19.7,19.7,0,0,1,.063-2.588,3.032,3.032,0,0,0-.285-1.344,31.773,31.773,0,0,1-2.029-7.987,40.057,40.057,0,0,1-.434-6.835,121.041,121.041,0,0,0,.149-15.113l5.124,1.152,5.958,12.147s3.833,5.931,5.021,10.611V473.8a11.453,11.453,0,0,1-1.791,3.886c-.791,1.082-1.562,2.178-2.277,3.31-.218.351-.46.712-.7,1.049-.079,0-3.995.228-4.66.963a4.449,4.449,0,0,0-1.245,1.887l-.169.026a39.5,39.5,0,0,0-2.645-5.544Z" transform="translate(-50.055 -12.157)" fill="url(#linear-gradient)"/>
    <path id="Path_75" data-name="Path 75" d="M775.915,767.873l-1.324,4.3-5.627,4.882-8.275,2.376s-3.889-2.128-2.565-2.542a3.266,3.266,0,0,0,1.248-1.086c-.463,1.688,3.22-.487,3.22-.487s9.516-6.785,10.509-7.53a7.484,7.484,0,0,1,2.118-.97C775.634,767.465,775.915,767.873,775.915,767.873Z" transform="translate(-120.277 -304.91)" opacity="0.1"/>
    <path id="Path_76" data-name="Path 76" d="M757.738,692.923l-1.324,4.3-5.627,4.882-8.275,2.376s-3.889-2.128-2.565-2.542a3.264,3.264,0,0,0,1.248-1.086,16.531,16.531,0,0,0,1.1-1.6c.708-1.139,1.476-2.237,2.261-3.31a11.912,11.912,0,0,0,2.089-5.5c.579-4.551-5.3-13.736-5.3-13.736L730.68,654.79l14.894,1.82s8.026,27.555,8.026,28.878c0,.993,2.264,4.584,3.442,6.4C757.457,692.516,757.738,692.923,757.738,692.923Z" transform="translate(-102.1 -229.96)" fill="#f8d4d4"/>
    <ellipse id="Ellipse_41" data-name="Ellipse 41" cx="28.173" cy="8.126" rx="28.173" ry="8.126" transform="translate(619.203 472.187)" fill="#1e293b"/>
    <path id="Path_77" data-name="Path 77" d="M770.57,773.456a1.589,1.589,0,0,1-.5.685c-2.045,1.877-9.7,4.667-14.421,6.183a33.427,33.427,0,0,1-3.783,1.076,11.216,11.216,0,0,1-5.213-1.678c-1.655-1.016-.165-2.459.5-3.2s4.634-.966,4.634-.966c-1.9,3.167,2.81.387,2.81.387s9.516-6.785,10.509-7.53a9.136,9.136,0,0,1,2.813-1.158Z" transform="translate(-112.285 -305.197)" fill="#fff"/>
    <path id="Path_79" data-name="Path 79" d="M789.781,788.09c-2.045,1.877-9.694,4.667-14.421,6.183C779.643,790.645,786.825,788.749,789.781,788.09Z" transform="translate(-131.992 -319.14)" opacity="0.1"/>
    <ellipse id="Ellipse_42" data-name="Ellipse 42" cx="5.637" cy="1.625" rx="5.637" ry="1.625" transform="translate(641.74 476.07)" opacity="0.1"/>
    <rect id="Rectangle_58" data-name="Rectangle 58" width="5.792" height="42.862" transform="translate(644.242 434.833)" fill="#1e293b"/>
    <path id="Path_80" data-name="Path 80" d="M783.792,687.128v4.965A40.74,40.74,0,0,1,778,689.045V685h1.208Z" transform="translate(-133.758 -250.171)" opacity="0.1"/>
    <path id="Path_81" data-name="Path 81" d="M754.27,610.208s19.124-4.922,23.884-8.606,23.45,13.65,20.981,20.19-11.459,11.277-11.459,11.277-24.86,4.045-29.094-4.392S754.27,610.208,754.27,610.208Z" transform="translate(-117.882 -194.037)" fill="#94a3b8"/>
    <path id="Path_82" data-name="Path 82" d="M754.27,610.208s19.124-4.922,23.884-8.606,23.45,13.65,20.981,20.19-11.459,11.277-11.459,11.277-24.86,4.045-29.094-4.392S754.27,610.208,754.27,610.208Z" transform="translate(-117.882 -194.037)" fill="#94a3b8" opacity="0.445"/>
    <path id="Path_83" data-name="Path 83" d="M749.27,604.208s19.124-4.922,23.884-8.606,23.45,13.65,20.981,20.19-11.459,11.277-11.459,11.277-24.86,4.045-29.094-4.392S749.27,604.208,749.27,604.208Z" transform="translate(-114.537 -190.023)" fill="#334155"/>
    <path id="Path_84" data-name="Path 84" d="M673.421,423.313l-2.069-.751a7.524,7.524,0,0,0,1.036-5.709s-.87-3.793-.457-5.329,1.489-7.166,1.489-7.166.152-5.958-3.068-8.688-5.455-4.965-4.213-2.317a36.851,36.851,0,0,0,1.82,3.558s-7.7,1.844-4.965,4.73,3.475,2.883,3.475,2.883-.91,7.116-1.076,8.44-1.655,11.419-1.655,11.419-.083,4.8.745,5.544a3.744,3.744,0,0,0,3.972,2.979C669.046,432.9,676.069,425.134,673.421,423.313Z" transform="translate(-56.426 -54.261)" fill="#f8d4d4"/>
    <path id="Path_85" data-name="Path 85" d="M784.127,413.249v4.634l-4.8,1.566-8.175,6.017-2.168,1.6-9.681-3.889-6.123-6.454s.3-.162.814-.43l.331-.175c1.036-.52,2.668-1.324,4.472-2.042s2.016-4.928,1.913-8.4v-.331c-.083-2.433-.331-4.422-.331-4.422s1.009-.192,2.479-.41c4.074-.606,11.677-1.423,11.009,1.072a5.752,5.752,0,0,0,0,2.578c.02.093.04.189.063.281a21.835,21.835,0,0,0,2.82,6.16l6.62,2.383.185.066Z" transform="translate(-117.153 -59.421)" fill="#f8d4d4"/>
    <path id="Path_86" data-name="Path 86" d="M838.485,441.541v-1.407a4.636,4.636,0,0,1,1.572,0c.579.166,1.678,1.9,2.175,2.069s3.535,11.5,3.2,12.495-3.459,23.411-3.459,23.411l-6.3,6.951-1.241-19.114,4.055-11.336Z" transform="translate(-171.511 -86.307)" fill="#f8d4d4"/>
    <path id="Path_87" data-name="Path 87" d="M737.631,601.77s-5.762,1.241-5.031,3.062,3.955,1.9,3.955,1.9Z" transform="translate(-103.342 -194.489)" fill="#fdc2cc"/>
    <path id="Path_88" data-name="Path 88" d="M710.965,463.909l-4.385,9.45-5.213,3.806-.91-5.882s-1.761-3.889-2.519-4.548a.379.379,0,0,0-.129-.086c-.662-.248-6.951,3.724-8.771,4.468s-8.357.91-8.357.91l2.9-10.343s5.792-2.151,6.537-3.144a60.232,60.232,0,0,1,6.868-6.62c2.317-1.82,6.544-1.158,6.544-1.158l.066.027,1.086.47,3.144,7.7Z" transform="translate(-68.65 -93.384)" fill="#f8d4d4"/>
    <path id="Path_89" data-name="Path 89" d="M701.066,669.314a19.757,19.757,0,0,0-.063,2.595c.023.923.063,1.708.063,1.708l-1.9,3.972s-6.537,2.151-5.461,1.241a3.6,3.6,0,0,0,.662-2.214,56.131,56.131,0,0,0-.235-7.3c-.248-3.475-4.22-12.081-4.22-12.081s-3.31-8.44-5.378-13.653-1.324-14.646-1.324-14.646.331-7.116,1.82-9.268,7.282-2.9,7.282-2.9l2.717,9.013s2.234,8.44,3.062,9.681.579,16.053.248,17.625a42.152,42.152,0,0,0,.434,6.858,32.207,32.207,0,0,0,2.012,8.01,3.059,3.059,0,0,1,.281,1.359Z" transform="translate(-70.252 -204.524)" fill="#f8d4d4"/>
    <path id="Path_90" data-name="Path 90" d="M722.3,785.068l-1.9,3.972s-6.537,2.151-5.461,1.241a3.6,3.6,0,0,0,.662-2.214c.235-.235.487-.493.745-.758a16.712,16.712,0,0,1,5.908-3.949h0C722.263,784.283,722.3,785.068,722.3,785.068Z" transform="translate(-91.489 -315.975)" opacity="0.1"/>
    <path id="Path_91" data-name="Path 91" d="M719.522,791.588a.187.187,0,0,1,0,.046c-.331,1.572-5.792,3.724-5.792,3.724s-3.475.579-5.461,1.076a1.374,1.374,0,0,1-.467.043c-1.532-.116-1.6-3.065-1.6-3.518a9.4,9.4,0,0,1,2.482-2.9,17.738,17.738,0,0,0,1.84-1.764,17.078,17.078,0,0,1,6.031-4.028h0S719.787,789.956,719.522,791.588Z" transform="translate(-85.723 -316.584)" fill="#fff"/>
    <path id="Path_93" data-name="Path 93" d="M722.76,806.268a.2.2,0,0,1,0,.046c-.331,1.572-5.792,3.724-5.792,3.724s-3.475.579-5.461,1.076a1.375,1.375,0,0,1-.467.043c1.8-1.235,6.166-4.2,6.755-4.263S721.287,805.993,722.76,806.268Z" transform="translate(-88.961 -331.264)" opacity="0.1"/>
    <path id="Path_94" data-name="Path 94" d="M788.514,404.442c-2.045,1.685-3.925,1.443-6.782,1.443-2.065,0-4.75.725-6.441-.2-.07-2.585-.331-4.76-.331-4.76s14.4-2.731,13.488.662a6.018,6.018,0,0,0,.066,2.86Z" transform="translate(-131.724 -59.422)" opacity="0.1"/>
    <path id="Path_95" data-name="Path 95" d="M781.1,361.146a11.833,11.833,0,0,1-11.833,11.833,8.247,8.247,0,0,1-.907-.033h0a11.833,11.833,0,1,1,12.743-11.8Z" transform="translate(-120.003 -25.593)" fill="#f8d4d4"/>
    <path id="Path_96" data-name="Path 96" d="M742.7,459.252l3.144,4.965-4.385,9.45-5.213,3.806-.91-5.9s-1.761-3.889-2.519-4.548c.218-.533.367-.778.367-.583,0,.827,2.036,4.551,2.036,4.551,6.289-2.151,4.336-10.509,4.336-10.509s-.781-7.447-1.086-9.407l1.086.47Z" transform="translate(-103.532 -93.675)" opacity="0.1"/>
    <path id="Path_97" data-name="Path 97" d="M784.127,439.869V444.5l-4.8,1.566-8.175,6.017-2.168,1.6-9.681-3.889-6.123-6.454s.3-.162.814-.43l.331-.175a40.414,40.414,0,0,0,16.718,3c12.468-1.986,12.839-5,12.528-6.067Z" transform="translate(-117.153 -86.041)" opacity="0.1"/>
    <path id="Path_98" data-name="Path 98" d="M743.016,514.772a92.777,92.777,0,0,1-9.6,4.055c-5.709,2.069-18.287-.662-19.859-1.076s-13.653-5.213-18.287-7.033-5.792-.331-6.868-.5-1.82-2.234-1.986-2.813,2.979-5.627,4.717-8.192,6.951-2.069,7.7-2.234,5.958,0,5.958,0-.331-1.655,1.572-6.289-3.144-13.239-3.144-13.239c-3.475-2.234-2.9-9.185-2.9-9.185-.827-3.062,1.82-10.1,1.82-9.268s2.036,4.551,2.036,4.551c6.289-2.151,4.336-10.509,4.336-10.509s-.91-8.688-1.152-9.764,1.152-.91,1.152-.91q.4.209.814.4a40.229,40.229,0,0,0,17.059,3.161c15.116-2.439,12.438-6.328,12.339-6.467l.735.261c5.461,3.972,3.806,17.128,3.806,17.128l-2.046,9.82-1.519,7.315s5.378,32.6,5.875,34.257S743.016,514.772,743.016,514.772Z" transform="translate(-72.484 -85.901)" fill="#94a3b8"/>
    <path id="Path_99" data-name="Path 99" d="M730.994,622c-.331.248-4.055,7.944-1.324,10.095S730.994,622,730.994,622Z" transform="translate(-100.759 -208.023)" opacity="0.1"/>
    <path id="Path_100" data-name="Path 100" d="M743.68,625.27c.083.248,7.778,12.495,12,13.488Z" transform="translate(-110.798 -210.21)" opacity="0.1"/>
    <path id="Path_101" data-name="Path 101" d="M797.2,332.386l-1.231.261a8.3,8.3,0,0,1,1.039-.308Z" transform="translate(-145.78 -14.236)" opacity="0.1"/>
    <path id="Path_102" data-name="Path 102" d="M783.931,396.545l-2.317,9.814A11.986,11.986,0,0,1,778.4,404.2a9.8,9.8,0,0,0-2.35-1.966,10.2,10.2,0,0,0-4.081-.563,19.63,19.63,0,0,1-5.1-.874,3.6,3.6,0,0,1-1.324-.642,3.217,3.217,0,0,1-.662-2.8,42.893,42.893,0,0,0,.05-5.723c-.02-.357-.046-.715-.076-1.072-.132-1.6-.563-3.452-2.2-4.137-.841-.331-1.844-.3-2.648-.7a2.185,2.185,0,0,1-.331-.2c-1.192-.834-1.3-2.506-1.254-3.912q.106-2.9.2-5.8a16.864,16.864,0,0,0-.063-2.863h0a6.352,6.352,0,0,0-.516-1.827c-.718-1.536-2.214-2.817-2.482-4.438a1.8,1.8,0,0,1-.03-.228c-.139-1.45.774-2.8,1.1-4.227.642-2.87-1.516-6.014-4.69-6.838-.993-.258-2.059-.308-3.048-.6a4.887,4.887,0,0,1-.467-.165,13.894,13.894,0,0,1,11.167-5.3c7.391,0,13.385,5.3,13.385,11.833a11.356,11.356,0,0,1-4.945,9.185c.02.093.043.189.07.281a20.819,20.819,0,0,0,3.191,6.16l7.493,2.383.834.265C785.8,383.395,783.931,396.545,783.931,396.545Z" transform="translate(-113.969 -25.596)" opacity="0.1"/>
    <path id="Path_103" data-name="Path 103" d="M867.572,520.655a1.124,1.124,0,0,1-.212-.03A.439.439,0,0,1,867.572,520.655Z" transform="translate(-193.541 -140.2)" opacity="0.1"/>
    <path id="Path_104" data-name="Path 104" d="M759.671,330a28.465,28.465,0,0,0-6.315-.738,3.5,3.5,0,0,0-1.986.417c-.732.513-.824,1.44-1.043,2.241a6.216,6.216,0,0,1-2.367,3.31c-.784.573-1.708.993-2.459,1.6a2.566,2.566,0,0,0-1.135,2.376,2.938,2.938,0,0,0,2.171,1.84c.993.281,2.049.331,3.048.6,3.174.824,5.329,3.972,4.687,6.835-.331,1.427-1.235,2.777-1.1,4.227.162,1.731,1.761,3.065,2.512,4.67a10.092,10.092,0,0,1,.586,4.687l-.2,5.8c-.053,1.529.079,3.369,1.589,4.117.808.4,1.814.354,2.648.7,1.635.682,2.065,2.539,2.2,4.134a42.74,42.74,0,0,1,.026,6.8,3.187,3.187,0,0,0,.662,2.8,3.549,3.549,0,0,0,1.324.642,19.634,19.634,0,0,0,5.1.874,10.307,10.307,0,0,1,4.081.563,9.878,9.878,0,0,1,2.35,1.966,12.988,12.988,0,0,0,7.672,3.359,2.285,2.285,0,0,0,1.734-.354c.427-.394.175-1.244-.434-1.195a2.943,2.943,0,0,0,2.787-1.066,2.35,2.35,0,0,0,0-2.7c-.692-.993-2.161-1.655-2.241-2.833a3.5,3.5,0,0,1,.741-1.85,7.781,7.781,0,0,0-1.2-9.268c-1.552-1.605-3.879-2.823-4.422-4.882-.361-1.377.185-2.823-.023-4.223-.331-2.317-2.625-3.972-3.886-6.037a10.5,10.5,0,0,1-1.162-6.914,49.179,49.179,0,0,1,1.8-6.918,8.928,8.928,0,0,0,.351-5.206,9.539,9.539,0,0,0-2.122-3.227,23.9,23.9,0,0,0-7.533-5.958,13.774,13.774,0,0,0-9.837-.907" transform="translate(-111.233 -12.175)" fill="#474157"/>
    <path id="Path_105" data-name="Path 105" d="M759.671,330a28.465,28.465,0,0,0-6.315-.738,3.5,3.5,0,0,0-1.986.417c-.732.513-.824,1.44-1.043,2.241a6.216,6.216,0,0,1-2.367,3.31c-.784.573-1.708.993-2.459,1.6a2.566,2.566,0,0,0-1.135,2.376,2.938,2.938,0,0,0,2.171,1.84c.993.281,2.049.331,3.048.6,3.174.824,5.329,3.972,4.687,6.835-.331,1.427-1.235,2.777-1.1,4.227.162,1.731,1.761,3.065,2.512,4.67a10.092,10.092,0,0,1,.586,4.687l-.2,5.8c-.053,1.529.079,3.369,1.589,4.117.808.4,1.814.354,2.648.7,1.635.682,2.065,2.539,2.2,4.134a42.74,42.74,0,0,1,.026,6.8,3.187,3.187,0,0,0,.662,2.8,3.549,3.549,0,0,0,1.324.642,19.634,19.634,0,0,0,5.1.874,10.307,10.307,0,0,1,4.081.563,9.878,9.878,0,0,1,2.35,1.966,12.988,12.988,0,0,0,7.672,3.359,2.285,2.285,0,0,0,1.734-.354c.427-.394.175-1.244-.434-1.195a2.943,2.943,0,0,0,2.787-1.066,2.35,2.35,0,0,0,0-2.7c-.692-.993-2.161-1.655-2.241-2.833a3.5,3.5,0,0,1,.741-1.85,7.781,7.781,0,0,0-1.2-9.268c-1.552-1.605-3.879-2.823-4.422-4.882-.361-1.377.185-2.823-.023-4.223-.331-2.317-2.625-3.972-3.886-6.037a10.5,10.5,0,0,1-1.162-6.914,49.179,49.179,0,0,1,1.8-6.918,8.928,8.928,0,0,0,.351-5.206,9.539,9.539,0,0,0-2.122-3.227,23.9,23.9,0,0,0-7.533-5.958,13.774,13.774,0,0,0-9.837-.907" transform="translate(-111.233 -12.175)" fill="#1e293b"/>
    <path id="Path_106" data-name="Path 106" d="M737.18,548.476s2.813,1.076,5.792-1.9S737.18,548.476,737.18,548.476Z" transform="translate(-106.449 -156.917)" opacity="0.1"/>
    <g id="Group_116" data-name="Group 116" transform="translate(633.048 317.105)" opacity="0.1">
      <path id="Path_107" data-name="Path 107" d="M793.1,330.871a11.776,11.776,0,0,1,1.655-.209,12.551,12.551,0,0,0-2.317.073Z" transform="translate(-776.467 -330.203)"/>
      <path id="Path_108" data-name="Path 108" d="M777.411,390.5a9.9,9.9,0,0,0-2.353-1.966,10.3,10.3,0,0,0-4.081-.563,19.53,19.53,0,0,1-5.1-.874,3.549,3.549,0,0,1-1.324-.642,3.225,3.225,0,0,1-.662-2.8,42.733,42.733,0,0,0-.026-6.8c-.136-1.6-.563-3.452-2.2-4.134-.837-.351-1.84-.308-2.648-.7-1.509-.748-1.655-2.588-1.592-4.117q.1-2.9.2-5.8a10.059,10.059,0,0,0-.586-4.69c-.751-1.605-2.35-2.936-2.512-4.67-.136-1.446.778-2.8,1.1-4.227.642-2.87-1.516-6.011-4.69-6.835-.993-.258-2.059-.311-3.048-.6a2.905,2.905,0,0,1-2.155-1.82,2.52,2.52,0,0,1,1.132-2.38c.751-.606,1.675-1.023,2.463-1.6a6.193,6.193,0,0,0,2.363-3.31,3.713,3.713,0,0,1,1.043-2.241,2.648,2.648,0,0,1,1.357-.4c-.295,0-.589-.017-.88-.017a3.521,3.521,0,0,0-1.986.414c-.732.516-.824,1.44-1.043,2.241a6.216,6.216,0,0,1-2.367,3.31c-.784.573-1.708.993-2.459,1.6a2.568,2.568,0,0,0-1.135,2.38,2.92,2.92,0,0,0,2.158,1.82c.99.295,2.049.331,3.048.6,3.174.824,5.329,3.972,4.687,6.835-.331,1.427-1.235,2.78-1.1,4.227.162,1.734,1.761,3.065,2.512,4.67a10.057,10.057,0,0,1,.586,4.69q-.1,2.9-.2,5.8c-.053,1.529.079,3.369,1.589,4.117.808.4,1.814.354,2.648.7,1.635.682,2.065,2.539,2.2,4.134a42.74,42.74,0,0,1,.026,6.8,3.187,3.187,0,0,0,.662,2.8,3.549,3.549,0,0,0,1.324.642,19.5,19.5,0,0,0,5.1.874,10.308,10.308,0,0,1,4.081.563A9.959,9.959,0,0,1,775.9,390.5a12.968,12.968,0,0,0,7.669,3.36,3.544,3.544,0,0,0,1.109-.043,12.945,12.945,0,0,1-7.267-3.317Z" transform="translate(-744.18 -329.32)"/>
    </g>
    <path id="Path_109" data-name="Path 109" d="M684.782,484s-2.648,2.429-2.261,3.422S684.782,484,684.782,484Z" transform="translate(-69.856 -115.699)" opacity="0.1"/>
    <path id="Path_110" data-name="Path 110" d="M668.535,577.927c-1.91-.467-7.176-.9-9.681,7.507a37.186,37.186,0,0,1-2.221,5.683c-2.4,4.935-5.508,15.083,8.146,17.317l32.767,7.447s17.542,2.151,18.7,1.324a3.207,3.207,0,0,0,.993-2.648s-19.363,0-23.169-5.958-3.972-14.067-3.972-14.067.477-12.521-19.528-16.159C669.908,578.248,669.213,578.092,668.535,577.927Z" transform="translate(-51.152 -178.397)" fill="#474157"/>
    <path id="Path_111" data-name="Path 111" d="M668.535,578.1c-1.91-.467-7.176-.9-9.681,7.51a37.188,37.188,0,0,1-2.221,5.683c-2.4,4.932-5.508,15.08,8.146,17.314l32.767,7.447s17.542,2.151,18.7,1.324a3.207,3.207,0,0,0,.993-2.648s-19.363,0-23.169-5.958S690.1,594.7,690.1,594.7s.477-12.521-19.528-16.159C669.908,578.418,669.213,578.256,668.535,578.1Z" transform="translate(-51.152 -178.511)" fill="#334155"/>
    <path id="Path_112" data-name="Path 112" d="M811.958,543.834a5.061,5.061,0,0,0-5.114-4.276c-4.713.086-19.22.715-25.959,5.117,0,0-5.13,3.31-6.62,3.31s-21.68,17.211,8.275,32.6c0,0,16.053,4.568,22.341-6.156,0,0,4.468-11.518,4.137-15.507S812,544.178,812,544.178,811.991,544.049,811.958,543.834Z" transform="translate(-125.886 -152.867)" fill="#475569"/>
    <path id="Path_113" data-name="Path 113" d="M811.958,553.257a4.963,4.963,0,0,0-5.114-3.949c-4.713.079-19.22.662-25.959,4.723,0,0-5.13,3.055-6.62,3.055s-21.68,15.887,8.275,30.1c0,0,16.053,4.217,22.341-5.683,0,0,4.468-10.635,4.137-14.315S812,553.575,812,553.575,811.991,553.456,811.958,553.257Z" transform="translate(-125.886 -159.39)" fill="#475569" opacity="0.1"/>
    <rect id="Rectangle_59" data-name="Rectangle 59" width="5.296" height="11.277" transform="translate(664.763 427.716)" fill="#1e293b"/>
    <ellipse id="Ellipse_43" data-name="Ellipse 43" cx="14.908" cy="0.957" rx="14.908" ry="0.957" transform="translate(461.439 379.949)" opacity="0.1"/>
    <path id="Path_114" data-name="Path 114" d="M266.794,405.634a5.684,5.684,0,0,0-.755,1.42c-.046.126-.093.265-.139.4a1.22,1.22,0,0,0-.262-.089,3.753,3.753,0,0,0-1.182-.1c0-.152.02-.331.023-.483.05-1.744-.156-4.177-1.82-3.148-1.6.993-.457,2.648.573,3.71l.265.258c-.94.662-1.119,2.228-1.274,3.853s-1.536,3.406-2.453,4.415c-.093.1-.175.2-.258.281a3.346,3.346,0,0,0-2.664-.685c-.036-.142-.079-.291-.122-.45-.741-2.674-2.28-7.311-4.243-8.328l.258-.331c.834-1.092,1.774-2.711.387-3.128s-1.513,1.407-1.436,2.77c0,.172.026.331.04.483a2.042,2.042,0,0,0-1.45.708l-.215-.252c-1.125-1.324-3.187-3.432-3.813-1.84-.583,1.483,1.235,3.406,2.512,4.521l.079.07c-.857,3.485,2.876,6.332,4.9,7.563l.261.159c-2.78,3.277-4.5,9.417-4.5,9.417l7.785,1.39s5.163-4.452,4.5-8.635h.053c5.16-1.033,6.332-6.534,5.527-9.8l.093-.036a5.871,5.871,0,0,0,2.39-1.807C271.3,405.928,268.271,403.664,266.794,405.634Z" transform="translate(222.063 -61.62)" fill="#475569"/>
    <path id="Path_115" data-name="Path 115" d="M266.794,405.634a5.684,5.684,0,0,0-.755,1.42c-.046.126-.093.265-.139.4a1.22,1.22,0,0,0-.262-.089,3.753,3.753,0,0,0-1.182-.1c0-.152.02-.331.023-.483.05-1.744-.156-4.177-1.82-3.148-1.6.993-.457,2.648.573,3.71l.265.258c-.94.662-1.119,2.228-1.274,3.853s-1.536,3.406-2.453,4.415c-.093.1-.175.2-.258.281a3.346,3.346,0,0,0-2.664-.685c-.036-.142-.079-.291-.122-.45-.741-2.674-2.28-7.311-4.243-8.328l.258-.331c.834-1.092,1.774-2.711.387-3.128s-1.513,1.407-1.436,2.77c0,.172.026.331.04.483a2.042,2.042,0,0,0-1.45.708l-.215-.252c-1.125-1.324-3.187-3.432-3.813-1.84-.583,1.483,1.235,3.406,2.512,4.521l.079.07c-.857,3.485,2.876,6.332,4.9,7.563l.261.159c-2.78,3.277-4.5,9.417-4.5,9.417l7.785,1.39s5.163-4.452,4.5-8.635h.053c5.16-1.033,6.332-6.534,5.527-9.8l.093-.036a5.871,5.871,0,0,0,2.39-1.807C271.3,405.928,268.271,403.664,266.794,405.634Z" transform="translate(222.063 -61.62)" fill="none" opacity="0.1"/>
    <path id="Path_116" data-name="Path 116" d="M252.281,410.632l-.9,2.664s-.506-.357-1.145-.92c-1.278-1.115-3.1-3.038-2.512-4.521.626-1.592,2.688.51,3.813,1.84C251.98,410.222,252.281,410.632,252.281,410.632Z" transform="translate(221.08 -64.374)" fill="#f1f5f9"/>
    <path id="Path_117" data-name="Path 117" d="M265.366,406.372a12.878,12.878,0,0,1-.887,1.043,13.984,13.984,0,0,1-.162-1.4c-.076-1.364.069-3.174,1.436-2.77S266.187,405.279,265.366,406.372Z" transform="translate(209.915 -61.633)" fill="#f1f5f9"/>
    <path id="Path_118" data-name="Path 118" d="M297.718,407.222c-.026.877-.113,1.576-.113,1.576a11.254,11.254,0,0,1-1.135-1.013c-1.029-1.069-2.168-2.717-.573-3.71C297.559,403.045,297.765,405.477,297.718,407.222Z" transform="translate(189.306 -62.063)" fill="#f1f5f9"/>
    <path id="Path_119" data-name="Path 119" d="M310.61,411.379a5.871,5.871,0,0,1-2.4,1.8,6.681,6.681,0,0,1-.8.288l-1.072-1.506a10.934,10.934,0,0,1,.444-1.463,5.688,5.688,0,0,1,.755-1.42,1.919,1.919,0,0,1,3.073,2.3Z" transform="translate(181.79 -65.072)" fill="#f1f5f9"/>
    <path id="Path_120" data-name="Path 120" d="M256.831,414.57c.453.526.755.937.755.937l-.9,2.664s-.506-.357-1.145-.92a5.213,5.213,0,0,1,1.026-2.34A3.1,3.1,0,0,1,256.831,414.57Z" transform="translate(215.776 -69.249)" fill="#1e293b" opacity="0.1"/>
    <path id="Path_121" data-name="Path 121" d="M265.4,412.088a12.9,12.9,0,0,1-.887,1.043,13.967,13.967,0,0,1-.162-1.4,1.807,1.807,0,0,1,1.049.357Z" transform="translate(209.882 -67.349)" fill="#1e293b" opacity="0.1"/>
    <path id="Path_122" data-name="Path 122" d="M263.826,423.624l-2.264.993a13.76,13.76,0,0,1-1.2-.682c-2.38-1.466-7.162-5.15-3.955-9.493s5.882,3.449,6.934,7.255C263.671,422.84,263.826,423.624,263.826,423.624Z" transform="translate(215.916 -68.308)" fill="#f1f5f9"/>
    <path id="Path_123" data-name="Path 123" d="M299.22,414.643a1.844,1.844,0,0,1,1.248-.563c-.026.877-.113,1.575-.113,1.575a11.266,11.266,0,0,1-1.135-1.012Z" transform="translate(186.553 -68.921)" fill="#1e293b" opacity="0.1"/>
    <path id="Path_124" data-name="Path 124" d="M308.213,417.581a6.692,6.692,0,0,1-.8.288l-1.072-1.506a10.935,10.935,0,0,1,.444-1.463,3.83,3.83,0,0,1,1.428,2.681Z" transform="translate(181.79 -69.47)" fill="#1e293b" opacity="0.1"/>
    <path id="Path_125" data-name="Path 125" d="M285.8,427.814l-.381.066c-7.589,1.155-2.5-2.939-2.5-2.939s.331-.314.8-.831c.917-1.009,2.294-2.78,2.453-4.415.238-2.459.513-4.766,3.416-4.088C292.452,416.272,293,426.374,285.8,427.814Z" transform="translate(198.577 -69.862)" fill="#f1f5f9"/>
    <rect id="Rectangle_60" data-name="Rectangle 60" width="14.5" height="3.813" transform="translate(469.747 376.844)" fill="#474157"/>
    <rect id="Rectangle_61" data-name="Rectangle 61" width="18.946" height="13.306" transform="translate(467.443 364.452)" fill="#474157"/>
    <rect id="Rectangle_62" data-name="Rectangle 62" width="14.5" height="3.813" transform="translate(469.608 376.844)" fill="#334155" opacity="0.1"/>
    <rect id="Rectangle_63" data-name="Rectangle 63" width="18.946" height="13.306" transform="translate(467.384 364.455)" fill="#334155" opacity="0.1"/>
    <rect id="Rectangle_64" data-name="Rectangle 64" width="14.5" height="3.813" transform="translate(470.7 376.844)" fill="#1e293b"/>
    <path id="Path_126" data-name="Path 126" d="M273.539,439c.331,1.139.49,1.923.49,1.923l-2.264.993a13.763,13.763,0,0,1-1.2-.682A5.677,5.677,0,0,1,273.539,439Z" transform="translate(205.727 -85.593)" fill="#1e293b" opacity="0.1"/>
    <path id="Path_127" data-name="Path 127" d="M285.8,445.244l-.381.066c-7.589,1.155-2.5-2.939-2.5-2.939s.331-.314.8-.831a5,5,0,0,1,2.081,3.7Z" transform="translate(198.577 -87.292)" fill="#1e293b" opacity="0.1"/>
    <path id="Path_128" data-name="Path 128" d="M257.76,451.555s4.25-15.225,10.211-10.525-2.426,11.915-2.426,11.915Z" transform="translate(214.291 -86.348)" fill="#f1f5f9"/>
    <circle id="Ellipse_44" data-name="Ellipse 44" cx="0.477" cy="0.477" r="0.477" transform="translate(473.719 348.045)" fill="#1e293b" opacity="0.1"/>
    <circle id="Ellipse_45" data-name="Ellipse 45" cx="0.477" cy="0.477" r="0.477" transform="translate(473.679 350.273)" fill="#1e293b" opacity="0.1"/>
    <circle id="Ellipse_46" data-name="Ellipse 46" cx="0.477" cy="0.477" r="0.477" transform="translate(487.504 348.482)" fill="#1e293b" opacity="0.1"/>
    <circle id="Ellipse_47" data-name="Ellipse 47" cx="0.477" cy="0.477" r="0.477" transform="translate(476.38 359.884)" fill="#1e293b" opacity="0.1"/>
    <circle id="Ellipse_48" data-name="Ellipse 48" cx="0.477" cy="0.477" r="0.477" transform="translate(486.511 352.179)" fill="#1e293b" opacity="0.1"/>
    <circle id="Ellipse_49" data-name="Ellipse 49" cx="0.477" cy="0.477" r="0.477" transform="translate(481.586 357.104)" fill="#1e293b" opacity="0.1"/>
    <circle id="Ellipse_50" data-name="Ellipse 50" cx="0.477" cy="0.477" r="0.477" transform="translate(475.347 348.998)" fill="#1e293b" opacity="0.1"/>
    <circle id="Ellipse_51" data-name="Ellipse 51" cx="0.477" cy="0.477" r="0.477" transform="translate(486.194 349.915)" fill="#1e293b" opacity="0.1"/>
    <circle id="Ellipse_52" data-name="Ellipse 52" cx="0.477" cy="0.477" r="0.477" transform="translate(484.565 353.609)" fill="#1e293b" opacity="0.1"/>
    <circle id="Ellipse_53" data-name="Ellipse 53" cx="0.477" cy="0.477" r="0.477" transform="translate(479.557 359.527)" fill="#1e293b" opacity="0.1"/>
    <circle id="Ellipse_54" data-name="Ellipse 54" cx="0.477" cy="0.477" r="0.477" transform="translate(478.525 357.184)" fill="#1e293b" opacity="0.1"/>
    <circle id="Ellipse_55" data-name="Ellipse 55" cx="0.477" cy="0.477" r="0.477" transform="translate(476.062 351.901)" fill="#1e293b" opacity="0.1"/>
    <rect id="Rectangle_65" data-name="Rectangle 65" width="14.5" height="1.393" transform="translate(470.7 376.844)" opacity="0.1"/>
    <rect id="Rectangle_66" data-name="Rectangle 66" width="18.946" height="13.306" transform="translate(468.397 364.452)" fill="#1e293b"/>
    <circle id="Ellipse_56" data-name="Ellipse 56" cx="0.477" cy="0.477" r="0.477" transform="translate(480.193 361.672)" fill="#1e293b" opacity="0.1"/>
    <circle id="Ellipse_57" data-name="Ellipse 57" cx="0.477" cy="0.477" r="0.477" transform="translate(477.333 362.268)" fill="#1e293b" opacity="0.1"/>
    <path id="Path_129" data-name="Path 129" d="M399.263,626s2.36,3.088-1.089,7.748-6.289,8.606-5.15,11.505c0,0,5.21-8.659,9.45-8.778S403.927,631.21,399.263,626Z" transform="translate(123.968 -210.699)" fill="#f1f5f9"/>
    <path id="Path_130" data-name="Path 130" d="M399.525,626a3.8,3.8,0,0,1,.483.967c4.134,4.859,6.338,9.393,2.363,9.509-3.7.116-8.146,6.736-9.211,8.39.036.129.079.258.126.381,0,0,5.21-8.659,9.45-8.778S404.188,631.21,399.525,626Z" transform="translate(123.706 -210.699)" fill="#1e293b" opacity="0.1"/>
    <path id="Path_131" data-name="Path 131" d="M424.553,636.492c0,1.086-.122,1.966-.271,1.966s-.271-.88-.271-1.966.152-.576.3-.576S424.553,635.4,424.553,636.492Z" transform="translate(103.067 -217.252)" fill="#475569"/>
    <path id="Path_132" data-name="Path 132" d="M426.437,640.312c-.953.52-1.784.834-1.857.7s.642-.662,1.6-1.182.576-.142.662,0S427.393,639.793,426.437,640.312Z" transform="translate(102.689 -219.775)" fill="#475569"/>
    <path id="Path_133" data-name="Path 133" d="M363.985,626s-2.36,3.088,1.089,7.748,6.289,8.606,5.147,11.505c0,0-5.206-8.659-9.446-8.778S359.321,631.21,363.985,626Z" transform="translate(146.771 -210.699)" fill="#f1f5f9"/>
    <path id="Path_134" data-name="Path 134" d="M363.985,626a3.8,3.8,0,0,0-.483.967c-4.134,4.859-6.338,9.393-2.363,9.509,3.7.116,8.146,6.736,9.211,8.39a3.669,3.669,0,0,1-.129.381s-5.206-8.655-9.446-8.768S359.321,631.21,363.985,626Z" transform="translate(146.771 -210.699)" fill="#1e293b" opacity="0.1"/>
    <path id="Path_135" data-name="Path 135" d="M361.44,636.492c0,1.086.122,1.966.271,1.966s.271-.88.271-1.966-.152-.576-.3-.576S361.44,635.4,361.44,636.492Z" transform="translate(144.927 -217.252)" fill="#475569"/>
    <path id="Path_136" data-name="Path 136" d="M355.691,640.312c.953.52,1.784.834,1.857.7s-.642-.662-1.6-1.182-.576-.142-.662,0S354.734,639.793,355.691,640.312Z" transform="translate(149.17 -219.775)" fill="#475569"/>
    <path id="Ellipse_58" data-name="Ellipse 58" d="M24.691,0C38.327,0,49.382,1.7,49.382,3.79S38.327,7.58,24.691,7.58,0,5.883,0,3.79,11.055,0,24.691,0Z" transform="translate(492.3 450.604)" fill="#f1f5f9" opacity="0.077"/>
    <path id="Path_137" data-name="Path 137" d="M380.556,668.693l-.119.963-.166,1.364-.066.566-.169,1.364-.07.569-.165,1.36-1.873,15.49c-.169,1.383-2.429,2.459-5.153,2.459h-9.837c-2.727,0-4.965-1.076-5.15-2.459l-1.893-15.49-.165-1.36-.069-.569-.169-1.364-.066-.566-.165-1.364-.119-.963c-.1-.781,1.125-1.443,2.671-1.443H377.89C379.427,667.25,380.652,667.912,380.556,668.693Z" transform="translate(149.146 -238.296)" fill="#1e293b"/>
    <path id="Path_138" data-name="Path 138" d="M280.188,623.15l-.165,1.36H255.176l-.165-1.36Z" transform="translate(249.394 -191.79)" fill="#334155"/>
    <path id="Path_139" data-name="Path 139" d="M280.425,628.98l-.165,1.364H255.88l-.169-1.364Z" transform="translate(248.926 -195.69)" fill="#334155"/>
    <path id="Path_140" data-name="Path 140" d="M280.668,634.81l-.165,1.364H256.6l-.165-1.364Z" transform="translate(248.444 -199.59)" fill="#334155"/>
  </g>
</svg>
