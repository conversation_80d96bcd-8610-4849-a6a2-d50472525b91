<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<script setup lang="ts">
import Button from '@/common/components/button/button.vue'
import { useLoginStore } from '@/common/stores/login-store'
import type { NuxtError } from '#app'
import errorIllustration from '~/assets/error-illustration.svg'

defineProps({ error: Object as () => NuxtError })

const loginStore = useLoginStore()
</script>

<template>
  <div>
    <div class="bg-primary">
      <div class="container">
        <img alt="" class="w-3/12 absolute top-6 left-6" src="/tm-large.png" />
        <div
          class="flex flex-col items-center justify-center h-screen text-center error-page lg:flex-row lg:text-left"
        >
          <div class="-intro-x lg:mr-20">
            <errorIllustration
              class="w-[450px] h-48 lg:h-auto"
              :fontControlled="false"
              filled
            />
          </div>
          <div class="mt-10 text-white lg:mt-0">
            <div class="font-medium intro-x text-8xl">
              {{ error?.statusCode }}
            </div>
            <div class="mt-5 text-xl font-medium intro-x lg:text-3xl">
              {{
                error?.statusCode === 404
                  ? 'Perdón, pero te has equivocado de ruta.'
                  : error?.message
              }}
            </div>
            <div class="mt-3 text-lg intro-x">
              {{
                error?.statusCode === 404
                  ? 'Revisa bien la dirección en tu navegador.'
                  : 'Este es un error del servidor, contactar con el administrador.'
              }}
            </div>
            <Button
              class="px-4 py-3 mt-10 text-white border-white intro-x dark:border-darkmode-400 dark:text-slate-200"
              @click="() => navigateTo(loginStore.isNullToken ? '/' : '/dash')"
            >
              Ir a Inicio
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
