<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<script setup lang="ts">
import Alert from '@c/components/alert/Alert.vue'
import ThemeSwitcher from '@c/components/theme-switcher.vue'
import { useAlertStore } from '@c/stores/alert-store'
import { useLayoutStore } from '@c/stores/layout-store'
import { useLoginStore } from '@c/stores/login-store'
import { storeToRefs } from 'pinia'

const layoutStore = useLayoutStore()
const alertStore = useAlertStore()
const loginStore = useLoginStore()
const { layoutComponent } = storeToRefs(layoutStore)

onMounted(() => {
  if (loginStore.isNullToken) navigateTo('/')
})
</script>

<template>
  <div v-if="!loginStore.isNullToken">
    <ThemeSwitcher />
    <Alert
      :show="alertStore.displayAlert.open"
      :variant="alertStore.displayAlert.variant"
    >
      {{ alertStore.displayAlert.message }}
    </Alert>
    <layoutComponent>
      <slot />
    </layoutComponent>
  </div>
</template>
