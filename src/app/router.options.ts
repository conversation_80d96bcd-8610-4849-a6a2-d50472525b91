import type { RouterConfig } from '@nuxt/schema'

//import { accesosRoutes } from '@d/accesos/routes'
import { homeRoutes } from '@d/home/<USER>'
//import { informeRoutes } from '@d/informes/routes'
//import { consultasRoutes } from '@d/consultas/routes'
//import { adminRoutes } from '@d/admin/routes'

export default {
  routes: _routes => [
    //...accesosRoutes,
    //...informeRoutes,
    ...homeRoutes,
    //...consultasRoutes,
    //...adminRoutes,
    {
      path: '/',
      component: () => import('@/login/pages/login.vue')
    }
  ]
} satisfies RouterConfig
