<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import FormCheck from '@c/components/form/FormCheck/Input.vue'
import FormInput from '@c/components/form/FormInput.vue'
import ThemeSwitcher from '@c/components/theme-switcher.vue'
import type { Consultores } from '@d/admin/models/consultores'
import { useMutation } from '@tanstack/vue-query'
import { useLocalStorage } from '@vueuse/core'

import Loading from '@/common/components/loading.vue'
import { useLoginStore } from '@/common/stores/login-store'
import illustration from '~/assets/illustration.svg'

definePageMeta({ layout: false })

const rememberMe = useLocalStorage('user', '')
const loginStore = useLoginStore()
const loginModel = reactive({ password: '', username: '' })
const isChecked = ref(false)
const isSuccess = ref(false)
const errorMessage = ref('')

preloadRouteComponents('/dash')
prerenderRoutes('/dash')

const { isPending: isLoginPending, mutate: doLogin } = useMutation({
  mutationFn: () =>
    $fetch('/auth/login', { body: loginModel, method: 'POST' }).then(data => {
      errorMessage.value = ''
      return data
    }),
  onError: error => (errorMessage.value = error.message),
  onSuccess: data => {
    if (data.statusCode !== 200) {
      errorMessage.value = data.body.message ?? 'Error desconocido'
      return
    }
    isSuccess.value = true
    loginStore.login(
      data.body.user as unknown as Consultores,
      data.body.token ?? ''
    )
    navigateTo('/dash', { replace: true })
  }
})

const disabledButton = computed(
  () => !loginModel.username || !loginModel.password
)

const doRememberMe = (input: boolean) => {
  console.log(input)
  rememberMe.value = input ? loginModel.username : ''
}

onMounted(() => {
  if (!loginStore.isNullToken) navigateTo('/dash')
  isChecked.value = rememberMe.value !== ''
  loginModel.username = rememberMe.value
})
</script>

<template>
  <div
    v-if="loginStore.isNullToken"
    :class="[
      'p-3 sm:px-8 relative h-screen lg:overflow-hidden bg-primary xl:bg-white dark:bg-darkmode-800 xl:dark:bg-darkmode-600',
      'before:hidden before:xl:block before:content-[\'\'] before:w-[57%] before:-mt-[28%] before:-mb-[16%] before:-ml-[13%] before:absolute before:inset-y-0 before:left-0 before:transform before:rotate-[-4.5deg] before:bg-primary/20 before:rounded-[100%] before:dark:bg-darkmode-400',
      'after:hidden after:xl:block after:content-[\'\'] after:w-[57%] after:-mt-[20%] after:-mb-[13%] after:-ml-[13%] after:absolute after:inset-y-0 after:left-0 after:transform after:rotate-[-4.5deg] after:bg-primary after:rounded-[100%] after:dark:bg-darkmode-700'
    ]"
  >
    <ThemeSwitcher />
    <div class="container relative z-10 sm:px-10">
      <div class="block grid-cols-2 gap-4 xl:grid">
        <div class="flex-col hidden min-h-screen xl:flex">
          <a class="flex items-center pt-5 -intro-x" href="#top">
            <img
              alt=""
              class="w-8/12 absolute top-4 -left-10"
              src="/tm-large.png"
            />
          </a>
          <div class="my-auto">
            <illustration
              class="w-[450px] h-48 lg:h-auto"
              :fontControlled="false"
              filled
            />
            <div
              class="mt-10 text-4xl font-medium leading-tight text-white -intro-x"
            >
              ¡Qué gusto verte de nuevo!
            </div>
            <div
              class="mt-5 text-lg text-white -intro-x text-opacity-70 dark:text-slate-400"
            >
              Portal de administración de consultores y registro de horas
            </div>
          </div>
        </div>
        <div class="flex h-screen py-5 my-10 xl:h-auto xl:py-0 xl:my-0">
          <div
            class="w-full px-5 py-8 mx-auto my-auto bg-white rounded-md shadow-md xl:ml-20 dark:bg-darkmode-600 xl:bg-transparent sm:px-8 xl:p-0 xl:shadow-none sm:w-3/4 lg:w-2/4 xl:w-auto"
          >
            <h2
              class="text-2xl font-bold text-center intro-x xl:text-3xl xl:text-left"
            >
              Inicio de Sesión
            </h2>
            <div class="mt-2 text-center intro-x text-slate-400 xl:hidden">
              A few more clicks to sign in to your account. Manage all your
              e-commerce accounts in one place
            </div>
            <div class="mt-8 intro-x">
              <FormInput
                v-model="loginModel.username"
                class="block px-4 py-3 intro-x min-w-full xl:min-w-[350px]"
                placeholder="Usuario"
                type="text"
                @input="() => (errorMessage = '')"
              />
              <FormInput
                v-model="loginModel.password"
                class="block px-4 py-3 mt-4 intro-x min-w-full xl:min-w-[350px]"
                placeholder="Contraseña"
                type="password"
                @input="() => (errorMessage = '')"
              />
            </div>
            <div
              class="flex mt-4 text-xs intro-x text-slate-600 dark:text-slate-500 sm:text-sm"
            >
              <div class="flex items-center mr-auto">
                <FormCheck
                  id="remember-me"
                  v-model:boolean="isChecked"
                  class="mr-2 border"
                  type="checkbox"
                  :disabled="loginModel.username === ''"
                  @input="
                    e =>
                      doRememberMe(
                        (e.target as HTMLInputElement | null)?.checked ?? false
                      )
                  "
                />
                <label class="cursor-pointer select-none" for="remember-me">
                  Recuérdame
                </label>
              </div>
            </div>
            <p v-if="errorMessage !== ''" class="text-danger mt-5">
              {{ errorMessage }}
            </p>
            <div class="mt-5 text-center items-ce intro-x xl:mt-8 xl:text-left">
              <Button
                class="w-full px-1 py-2"
                variant="primary"
                :disabled="disabledButton || isLoginPending || isSuccess"
                @click="doLogin"
              >
                <Loading v-if="isLoginPending" icon="three-dots" class="mr-3" />
                Ingresar
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
