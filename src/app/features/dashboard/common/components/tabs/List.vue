<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { TabList as HeadlessTabList } from '@headlessui/vue'
import { twMerge } from 'tailwind-merge'
import type { ExtractPropTypes } from 'vue'

defineOptions({ inheritAttrs: false })

const props = withDefaults(
  defineProps<
    /* @vue-ignore */ ExtractPropTypes<typeof HeadlessTabList> & {
      class?: string
      variant?: 'boxed-tabs' | 'link-tabs' | 'pills' | 'tabs'
    }
  >(),
  { class: '', variant: 'tabs' }
)

const attrs = useAttrs()
const computedClass = computed(() =>
  twMerge([
    props.variant === 'tabs' &&
      'border-b border-slate-200 dark:border-darkmode-400',
    props.variant === 'boxed-tabs' &&
      'p-0.5 border bg-slate-50/70 border-slate-200/70 rounded-lg dark:border-darkmode-400',
    'w-full flex',
    props.class
  ])
)

provide<{
  variant?: 'boxed-tabs' | 'link-tabs' | 'pills' | 'tabs'
}>('list', { variant: props.variant })
</script>

<template>
  <HeadlessTabList as="ul" :class="computedClass" v-bind="attrs">
    <slot />
  </HeadlessTabList>
</template>
