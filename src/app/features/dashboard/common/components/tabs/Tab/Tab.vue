<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { Tab as HeadlessTab } from '@headlessui/vue'
import type { ExtractPropTypes } from 'vue'

import Provider from './Provider.vue'

const props = withDefaults(
  defineProps<
    /* @vue-ignore */
    ExtractPropTypes<typeof HeadlessTab> & {
      fullWidth?: boolean
    }
  >(),
  { fullWidth: true }
)

const list = inject<{
  variant?: 'boxed-tabs' | 'link-tabs' | 'pills' | 'tabs'
}>('list')
</script>

<template>
  <HeadlessTab v-slot="{ selected }" as="template">
    <li
      :class="[
        'focus-visible:outline-none',
        { 'flex-1': props.fullWidth },
        { '-mb-px': list && list.variant == 'tabs' }
      ]"
    >
      <Provider :selected="selected">
        <slot :selected="selected" />
      </Provider>
    </li>
  </HeadlessTab>
</template>
