<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import type { ComputedRef } from 'vue'

const props = withDefaults(defineProps<{ selected?: boolean }>(), {
  selected: false
})

provide<{ selected: ComputedRef<boolean> }>('tab', {
  selected: computed(() => props.selected)
})
</script>

<template>
  <slot />
</template>
