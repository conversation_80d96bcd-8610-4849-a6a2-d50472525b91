<script setup lang="ts">
import { TabGroup as HeadlessTabGroup } from '@headlessui/vue'
import type { ExtractPropTypes } from 'vue'

const props = withDefaults(
  defineProps<
    /* @vue-ignore */ ExtractPropTypes<typeof HeadlessTabGroup> & {
      class?: string
    }
  >(),
  { class: '' }
)
</script>

<template>
  <HeadlessTabGroup as="div" :class="props.class">
    <slot />
  </HeadlessTabGroup>
</template>
