<script setup lang="ts">
import { TabPanel as HeadlessTabPanel, TransitionRoot } from '@headlessui/vue'
import type { ExtractPropTypes } from 'vue'

defineProps</* @vue-ignore */ ExtractPropTypes<typeof HeadlessTabPanel>>()
</script>

<template>
  <HeadlessTabPanel v-slot="{ selected }" as="template">
    <TransitionRoot
      appear
      as="div"
      :show="selected"
      enter="transition-opacity duration-300"
      enterFrom="opacity-0"
      enterTo="opacity-100"
      leave="transition-opacity duration-300"
      leaveFrom="opacity-100"
      leaveTo="opacity-0"
    >
      <slot :selected="selected" />
    </TransitionRoot>
  </HeadlessTabPanel>
</template>
