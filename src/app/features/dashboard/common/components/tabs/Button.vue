<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { twMerge } from 'tailwind-merge'
import type { ButtonHTMLAttributes, ComputedRef } from 'vue'

defineOptions({ inheritAttrs: false })

const props = withDefaults(
  defineProps<
    /* @vue-ignore */ ButtonHTMLAttributes & {
      as?: object | string
      class?: string
    }
  >(),
  { as: 'button', class: '' }
)

const attrs = useAttrs()

const tab = inject<{ selected: ComputedRef<boolean> }>('tab')
const list = inject<{
  variant?: 'boxed-tabs' | 'link-tabs' | 'pills' | 'tabs'
}>('list')

const computedClass = computed(() =>
  twMerge([
    'cursor-pointer block appearance-none px-3 py-2 border border-transparent text-slate-600 transition-colors dark:text-slate-400',
    (tab?.selected.value ?? false) && 'text-slate-700 dark:text-white',

    list?.variant === 'tabs' &&
      'block border-transparent rounded-t-md dark:border-transparent',
    list?.variant === 'tabs' &&
      (tab?.selected.value ?? false) &&
      'bg-white border-slate-200 border-b-transparent font-medium dark:bg-transparent dark:border-t-darkmode-400 dark:border-b-darkmode-600 dark:border-x-darkmode-400',
    list?.variant === 'tabs' &&
      !(tab?.selected.value ?? false) &&
      'hover:bg-slate-100 dark:hover:bg-darkmode-400 dark:hover:border-transparent',

    list?.variant === 'pills' && 'rounded-md border-0',
    list?.variant === 'pills' &&
      (tab?.selected.value ?? false) &&
      'bg-primary text-white font-medium',

    list?.variant === 'boxed-tabs' &&
      'rounded-md py-1.5 dark:border-transparent',
    list?.variant === 'boxed-tabs' &&
      (tab?.selected.value ?? false) &&
      'text-slate-700 border shadow-sm font-medium border-slate-200 bg-white dark:text-slate-300 dark:bg-darkmode-400 dark:border-darkmode-400',

    list?.variant === 'link-tabs' &&
      'border-b-2 border-transparent dark:border-transparent',
    list?.variant === 'link-tabs' &&
      (tab?.selected.value ?? false) &&
      'border-b-primary font-medium dark:border-b-primary',
    props.class
  ])
)
</script>

<template>
  <button :class="computedClass" v-bind="attrs">
    <slot />
  </button>
</template>
