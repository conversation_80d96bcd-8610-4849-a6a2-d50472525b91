<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-redundant-type-constituents -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<script setup lang="ts">
import '~/resources/vendors/simplebar.css'
import '~/resources/mobile-menu.css'

import { useMenuStore } from '@d/common/stores/menu-store'
import SimpleBar from 'simplebar'
import { twMerge } from 'tailwind-merge'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import {
  enter,
  type FormattedMenu,
  leave,
  linkTo,
  nestedMenu
} from './mobile-menu'

const route = useRoute()
const router = useRouter()
const formattedMenu = reactive<(FormattedMenu | 'divider')[]>([])
const menuStore = useMenuStore()

const setFormattedMenu = (
  computedFormattedMenu: (FormattedMenu | 'divider')[]
) => Object.assign(formattedMenu, computedFormattedMenu)

const menu = computed(() =>
  // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-member-access
  nestedMenu(menuStore.menu, route)
)

const activeMobileMenu = ref(false)
const setActiveMobileMenu = (active: boolean) =>
  (activeMobileMenu.value = active)

const scrollableRef = ref<HTMLDivElement>()

watch(menu, () => {
  setFormattedMenu(menu.value)
})

onMounted(() => {
  // eslint-disable-next-line sonarjs/constructor-for-side-effects
  if (scrollableRef.value) new SimpleBar(scrollableRef.value)
  setFormattedMenu(menu.value)
})
</script>

<template>
  <div
    :class="[
      'mobile-menu group top-0 inset-x-0 fixed bg-theme-1/90 z-[60] border-b border-white/[0.08] dark:bg-darkmode-800/90 md:hidden',
      'before:content-[\'\'] before:w-full before:h-screen before:z-10 before:fixed before:inset-x-0 before:bg-black/90 before:transition-opacity before:duration-200 before:ease-in-out',
      'before:invisible before:opacity-0',
      '[&.mobile-menu--active]:before:visible [&.mobile-menu--active]:before:opacity-100',
      activeMobileMenu && 'mobile-menu--active'
    ]"
  >
    <div class="h-[70px] px-3 sm:px-8 flex items-center">
      <a href="" class="flex mr-auto">
        <img alt="Timesheets" class="w-36" src="/logo-large.png" />
      </a>
      <a href="#" @click="e => e.preventDefault()">
        <LucideBarChart2
          class="w-8 h-8 text-white transform -rotate-90"
          @click="() => setActiveMobileMenu(!activeMobileMenu)"
        />
      </a>
    </div>
    <div
      ref="scrollableRef"
      :class="
        twMerge([
          'h-screen z-20 top-0 left-0 w-[270px] -ml-[100%] bg-primary transition-all duration-300 ease-in-out dark:bg-darkmode-800',
          '[&[data-simplebar]]:fixed [&_.simplebar-scrollbar]:before:bg-black/50',
          'group-[.mobile-menu--active]:ml-0'
        ])
      "
    >
      <a
        href="#"
        :class="[
          'fixed top-0 right-0 mt-4 mr-4 transition-opacity duration-200 ease-in-out',
          'invisible opacity-0',
          'group-[.mobile-menu--active]:visible group-[.mobile-menu--active]:opacity-100'
        ]"
        @click="e => e.preventDefault()"
      >
        <LucideXCircle
          class="w-8 h-8 text-white transform -rotate-90"
          @click="() => setActiveMobileMenu(!activeMobileMenu)"
        />
      </a>
      <ul class="py-2">
        <!-- BEGIN: First Child -->
        <template v-for="(menuState, menuKey) in formattedMenu">
          <li
            v-if="menuState == 'divider'"
            :key="menuKey"
            class="my-6 menu__divider"
          ></li>
          <li v-else :key="menuKey + 3">
            <a
              :href="
                menuState.subMenu
                  ? '#'
                  : ((page: string | undefined) => {
                      try {
                        return router.resolve({
                          name: page
                        }).fullPath
                      } catch (err) {
                        return ''
                      }
                    })(menuState.page)
              "
              :class="[menuState.active ? 'menu menu--active' : 'menu']"
              @click="
                event => {
                  event.preventDefault()
                  linkTo(menuState, router, setActiveMobileMenu)
                  setFormattedMenu([...formattedMenu])
                }
              "
            >
              <div class="menu__icon">
                <component :is="menuState.icon" />
              </div>
              <div class="menu__title">
                {{ menuState.title }}
                <div
                  v-if="menuState.subMenu"
                  :class="[
                    'menu__sub-icon',
                    menuState.activeDropdown && 'transform rotate-180'
                  ]"
                >
                  <LucideChevronDown />
                </div>
              </div>
            </a>
            <Transition
              :show="menuState.subMenu && menuState.activeDropdown"
              @enter="enter"
              @leave="leave"
            >
              <ul
                v-if="menuState.subMenu && menuState.activeDropdown"
                :class="{ 'menu__sub-open': menuState.activeDropdown }"
              >
                <li
                  v-for="(subMenu, subMenuKey) in menuState.subMenu"
                  :key="subMenuKey"
                >
                  <a
                    :href="
                      subMenu.subMenu
                        ? '#'
                        : ((page: string | undefined) => {
                            try {
                              return router.resolve({
                                name: page
                              }).fullPath
                            } catch (err) {
                              return ''
                            }
                          })(subMenu.page)
                    "
                    :class="[subMenu.active ? 'menu menu--active' : 'menu']"
                    @click="
                      event => {
                        event.preventDefault()
                        linkTo(subMenu, router, setActiveMobileMenu)
                        setFormattedMenu([...formattedMenu])
                      }
                    "
                  >
                    <div class="menu__icon">
                      <component :is="subMenu.icon" />
                    </div>
                    <div class="menu__title">
                      {{ subMenu.title }}
                      <div
                        v-if="subMenu.subMenu"
                        :class="[
                          'menu__sub-icon',
                          subMenu.activeDropdown && 'transform rotate-180'
                        ]"
                      >
                        <LucideChevronDown />
                      </div>
                    </div>
                  </a>
                  <Transition @enter="enter" @leave="leave">
                    <ul
                      v-if="subMenu.subMenu && subMenu.activeDropdown"
                      :class="{ 'menu__sub-open': subMenu.activeDropdown }"
                    >
                      <li
                        v-for="(lastSubMenu, lastSubMenuKey) in subMenu.subMenu"
                        :key="lastSubMenuKey"
                      >
                        <a
                          :href="
                            lastSubMenu.subMenu
                              ? '#'
                              : ((page: string | undefined) => {
                                  try {
                                    return router.resolve({
                                      name: page
                                    }).fullPath
                                  } catch (err) {
                                    return ''
                                  }
                                })(lastSubMenu.page)
                          "
                          :class="[
                            lastSubMenu.active ? 'menu menu--active' : 'menu'
                          ]"
                          @click="
                            event => {
                              event.preventDefault()
                              linkTo(lastSubMenu, router, setActiveMobileMenu)
                              setFormattedMenu([...formattedMenu])
                            }
                          "
                        >
                          <div class="menu__icon">
                            <component :is="lastSubMenu.icon" />
                          </div>
                          <div class="menu__title">{{ lastSubMenu.title }}</div>
                        </a>
                      </li>
                    </ul>
                  </Transition>
                </li>
              </ul>
            </Transition>
          </li>
        </template>
      </ul>
    </div>
  </div>
</template>
