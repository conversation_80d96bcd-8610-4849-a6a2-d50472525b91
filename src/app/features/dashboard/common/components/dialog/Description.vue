<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { DialogDescription as HeadlessDialogDescription } from '@headlessui/vue'
import { twMerge } from 'tailwind-merge'
import type { ExtractPropTypes } from 'vue'

const props = withDefaults(
  defineProps<
    /* @vue-ignore */ ExtractPropTypes<typeof HeadlessDialogDescription> & {
      as?: object | string
      class?: string
    }
  >(),
  { as: 'div' }
)

const attrs = useAttrs()
const computedClass = computed(() => twMerge(['p-5', props.class ?? '']))
</script>

<template>
  <HeadlessDialogDescription as="template">
    <component :is="props.as" :class="computedClass" v-bind="attrs">
      <slot />
    </component>
  </HeadlessDialogDescription>
</template>
