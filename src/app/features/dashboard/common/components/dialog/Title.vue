<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { DialogTitle as HeadlessDialogTitle } from '@headlessui/vue'
import { twMerge } from 'tailwind-merge'
import type { ExtractPropTypes } from 'vue'

const props = withDefaults(
  defineProps<
    /* @vue-ignore */ ExtractPropTypes<typeof HeadlessDialogTitle> & {
      as?: object | string
      class?: string
    }
  >(),
  { as: 'div' }
)

const attrs = useAttrs()
const computedClass = computed(() =>
  twMerge([
    'flex items-center px-5 py-3 border-b border-slate-200/60 dark:border-darkmode-400',
    props.class ?? ''
  ])
)
</script>

<template>
  <HeadlessDialogTitle as="template">
    <component :is="props.as" :class="computedClass" v-bind="attrs">
      <slot />
    </component>
  </HeadlessDialogTitle>
</template>
