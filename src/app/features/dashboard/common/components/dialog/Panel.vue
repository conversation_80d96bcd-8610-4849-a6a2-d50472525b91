<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<script setup lang="ts">
import {
  DialogPanel as HeadlessDialogPanel,
  TransitionChild
} from '@headlessui/vue'
import { twMerge } from 'tailwind-merge'
import type { ExtractPropTypes } from 'vue'

const props = withDefaults(
  defineProps<
    /* @vue-ignore */ ExtractPropTypes<typeof HeadlessDialogPanel> & {
      as?: object | string
      class?: string
    }
  >(),
  { as: 'div' }
)

const dialog = inject<{
  open: boolean
  size?: 'lg' | 'md' | 'sm' | 'xl'
  zoom: Ref<boolean>
}>('dialog')

const attrs = useAttrs()
const computedClass = computed(() =>
  twMerge([
    'w-[90%] mx-auto bg-white relative rounded-lg shadow-md transition-transform dark:bg-darkmode-600',
    dialog?.size === 'md' && 'sm:w-[460px]',
    dialog?.size === 'sm' && 'sm:w-[300px]',
    dialog?.size === 'lg' && 'sm:w-[600px]',
    dialog?.size === 'xl' && 'sm:w-[600px] lg:w-[900px]',
    Boolean(dialog?.zoom.value) && 'scale-105',
    props.class ?? ''
  ])
)
</script>

<template>
  <TransitionChild
    as="div"
    enter="ease-in-out duration-500"
    enterFrom="opacity-0"
    enterTo="opacity-100"
    leave="ease-in-out duration-[400ms]"
    leaveFrom="opacity-100"
    leaveTo="opacity-0"
    class="fixed inset-0 bg-gradient-to-b from-theme-1/50 via-theme-2/50 to-black/50 backdrop-blur-sm"
    aria-hidden="true"
  />
  <TransitionChild
    as="div"
    enter="ease-in-out duration-500"
    enterFrom="opacity-0 -mt-16"
    enterTo="opacity-100 mt-16"
    entered="opacity-100 mt-16"
    leave="ease-in-out duration-[400ms]"
    leaveFrom="opacity-100 mt-16"
    leaveTo="opacity-0 -mt-16"
    class="fixed inset-0"
  >
    <HeadlessDialogPanel :is="props.as" :class="computedClass" v-bind="attrs">
      <slot />
    </HeadlessDialogPanel>
  </TransitionChild>
</template>
