<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { Dialog as HeadlessDialog, TransitionRoot } from '@headlessui/vue'
import { twMerge } from 'tailwind-merge'
import type { Ref } from 'vue'

const props = withDefaults(
  defineProps<{
    as?: object | string
    class?: string
    open?: boolean
    size?: 'lg' | 'md' | 'sm' | 'xl'
    staticBackdrop?: boolean
  }>(),
  {
    as: 'div',
    class: '',
    open: false,
    size: 'md',
    staticBackdrop: false
  }
)

const attrs = useAttrs()
const computedClass = computed(() => twMerge(['relative z-[60]', props.class]))
const zoom = ref(false)
const emit = defineEmits<(e: 'close', value: boolean) => void>()

const handleClose = (value: boolean) => {
  if (props.staticBackdrop) {
    zoom.value = true
    setTimeout(() => (zoom.value = false), 300)
  } else {
    emit('close', value)
  }
}

provide<{
  open: boolean
  size?: 'lg' | 'md' | 'sm' | 'xl'
  zoom: Ref<boolean>
}>('dialog', {
  open: props.open,
  size: props.size,
  zoom
})
</script>

<template>
  <TransitionRoot appear as="template" :show="props.open">
    <HeadlessDialog
      :as="props.as"
      :class="computedClass"
      v-bind="attrs"
      @close="handleClose"
    >
      <slot />
    </HeadlessDialog>
  </TransitionRoot>
</template>
