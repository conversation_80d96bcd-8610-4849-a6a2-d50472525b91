<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { twMerge } from 'tailwind-merge'

const props = defineProps<{ class?: string }>()

const table = inject<{
  bordered: boolean
  dark: boolean
  hover: boolean
  sm: boolean
  striped: boolean
}>('table', {
  bordered: false,
  dark: false,
  hover: false,
  sm: false,
  striped: false
})

const thead = inject<{ variant?: 'dark' | 'default' | 'light' }>('thead', {
  variant: 'default'
})
const attrs = useAttrs()
const computedClass = computed(() =>
  twMerge([
    'font-medium px-5 py-3 border-b-2 dark:border-darkmode-300',
    thead.variant === 'light' && 'border-b-0 text-slate-700',
    thead.variant === 'dark' && 'border-b-0',
    table.dark && 'border-slate-600 dark:border-darkmode-300',
    table.bordered && 'border-l border-r border-t',
    table.sm && 'px-4 py-2',
    props.class ?? ''
  ])
)
</script>

<template>
  <th :class="computedClass" v-bind="attrs">
    <slot />
  </th>
</template>
