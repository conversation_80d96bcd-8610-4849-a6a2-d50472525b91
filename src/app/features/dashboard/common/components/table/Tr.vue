<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { twMerge } from 'tailwind-merge'

const props = defineProps<{ class?: string }>()

const table = inject<{
  bordered: boolean
  dark: boolean
  hover: boolean
  sm: boolean
  striped: boolean
}>('table', {
  bordered: false,
  dark: false,
  hover: false,
  sm: false,
  striped: false
})

const attrs = useAttrs()

const computedClass = computed(() =>
  twMerge([
    table.hover &&
      '[&:hover_td]:bg-slate-100 [&:hover_td]:dark:bg-darkmode-300 [&:hover_td]:dark:bg-opacity-50',
    table.striped &&
      '[&:nth-of-type(odd)_td]:bg-slate-100 [&:nth-of-type(odd)_td]:dark:bg-darkmode-300 [&:nth-of-type(odd)_td]:dark:bg-opacity-50',
    props.class ?? ''
  ])
)
</script>

<template>
  <tr :class="computedClass" v-bind="attrs">
    <slot />
  </tr>
</template>
