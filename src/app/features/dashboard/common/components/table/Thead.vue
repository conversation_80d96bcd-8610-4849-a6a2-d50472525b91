<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { twMerge } from 'tailwind-merge'
import type { HTMLAttributes } from 'vue'

const props = withDefaults(
  defineProps<
    /* @vue-ignore */ HTMLAttributes & {
      class?: string
      variant?: 'dark' | 'default' | 'light'
    }
  >(),
  { variant: 'default' }
)

const attrs = useAttrs()
const computedClass = computed(() =>
  twMerge([
    props.variant === 'light' && 'bg-slate-200/60 dark:bg-slate-200',
    props.variant === 'dark' && 'bg-dark text-white dark:bg-black/30',
    props.class ?? ''
  ])
)

provide<{ variant?: 'dark' | 'default' | 'light' }>('thead', {
  variant: props.variant
})
</script>

<template>
  <thead :class="computedClass" v-bind="attrs">
    <slot />
  </thead>
</template>
