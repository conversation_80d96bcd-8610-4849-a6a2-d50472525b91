<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { twMerge } from 'tailwind-merge'

const props = defineProps<{ class?: string }>()

const table = inject<{
  bordered: boolean
  dark: boolean
  hover: boolean
  sm: boolean
  striped: boolean
}>('table', {
  bordered: false,
  dark: false,
  hover: false,
  sm: false,
  striped: false
})

const attrs = useAttrs()
const computedClass = computed(() =>
  twMerge([
    'px-5 py-3 border-b dark:border-darkmode-300',
    table.dark && 'border-slate-600 dark:border-darkmode-300',
    table.bordered && 'border-l border-r border-t',
    table.sm && 'px-4 py-2',
    props.class ?? ''
  ])
)
</script>

<template>
  <td :class="computedClass" v-bind="attrs">
    <slot />
  </td>
</template>
