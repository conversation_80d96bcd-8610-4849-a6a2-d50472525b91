<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { twMerge } from 'tailwind-merge'
import type { TableHTMLAttributes } from 'vue'

const {
  bordered = false,
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  class: className,
  dark = false,
  hover = false,
  sm = false,
  striped = false
} = defineProps<
  /* @vue-ignore */ TableHTMLAttributes & {
    bordered?: boolean
    class?: string
    dark?: boolean
    hover?: boolean
    sm?: boolean
    striped?: boolean
  }
>()

const attrs = useAttrs()

const computedClass = computed(() =>
  twMerge([
    'w-full text-left',
    dark && 'bg-dark text-white dark:bg-black/30',
    className ?? ''
  ])
)

provide<{
  bordered: boolean
  dark: boolean
  hover: boolean
  sm: boolean
  striped: boolean
}>('table', {
  bordered,
  dark,
  hover,
  sm,
  striped
})
</script>

<template>
  <table :class="computedClass" v-bind="attrs">
    <slot />
  </table>
</template>
