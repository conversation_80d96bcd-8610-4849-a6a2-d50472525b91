<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import '~/resources/vendors/tippy.css'

import tippy, {
  animateFill as animateFillPlugin,
  type PopperElement,
  type Props,
  roundArrow
} from 'tippy.js'

type ProvideTippy = (el: PopperElement) => void

type TippyContentProps = {
  options?: Partial<Props>
  refKey?: string
  to: string
}

const props = defineProps<TippyContentProps>()

const tippyRef = ref<PopperElement>()

const init = (el: PopperElement, props: TippyContentProps) => {
  tippy(`[data-tooltip="${props.to}"]`, {
    allowHTML: true,
    animateFill: false,
    animation: 'shift-away',
    arrow: roundArrow,
    content: el,
    plugins: [animateFillPlugin],
    popperOptions: {
      modifiers: [
        {
          name: 'preventOverflow',
          options: {
            rootBoundary: 'viewport'
          }
        }
      ]
    },
    theme: 'light',
    trigger: 'click',
    ...props.options
  })
}

const bindInstance = (el: PopperElement) => {
  if (props.refKey) {
    const bind = inject<ProvideTippy>(`bind[${props.refKey}]`)
    if (bind) {
      bind(el)
    }
  }
}

onMounted(() => {
  if (tippyRef.value) {
    init(tippyRef.value, props)
    bindInstance(tippyRef.value)
  }
})
</script>

<template>
  <div ref="tippyRef">
    <slot />
  </div>
</template>
