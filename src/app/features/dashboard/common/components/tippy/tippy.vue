<!-- eslint-disable @typescript-eslint/no-empty-function -->
<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import '~/resources/vendors/tippy.css'

import tippy, {
  animateFill as animateFillPlugin,
  type PopperElement,
  type Props,
  roundArrow
} from 'tippy.js'

type ProvideTippy = (el: PopperElement) => void

type TippyProps = {
  as?: object | string
  class?: string
  content: string
  disable?: boolean
  options?: Partial<Props>
  refKey?: string
}

const props = withDefaults(defineProps<TippyProps>(), {
  as: 'span',
  class: '',
  disable: false
})

const tippyRef = ref<PopperElement>()

const init = (el: PopperElement, props: TippyProps) => {
  tippy(el, {
    animateFill: false,
    animation: 'shift-away',
    arrow: roundArrow,
    content: props.content,
    plugins: [animateFillPlugin],
    popperOptions: {
      modifiers: [
        {
          name: 'preventOverflow',
          options: { rootBoundary: 'viewport' }
        }
      ]
    },
    ...props.options
  })
}

const bindInstance = (el: PopperElement) => {
  if (props.refKey !== undefined) {
    const bind = inject<ProvideTippy>(`bind[${props.refKey}]`, () => {})
    if (bind) {
      bind(el)
    }
  }
}

const vTippyDirective = {
  mounted(el: PopperElement) {
    tippyRef.value = el
  }
}

const isDisabled = () => {
  if (tippyRef.value?._tippy !== undefined) {
    if (props.disable) {
      tippyRef.value._tippy.disable()
    } else {
      tippyRef.value._tippy.enable()
    }
  }
}

watch(props, () => {
  isDisabled()
})

onMounted(() => {
  if (tippyRef.value) {
    init(tippyRef.value, props)
    bindInstance(tippyRef.value)
    isDisabled()
  }
})
</script>

<template>
  <component
    :is="as"
    v-tippy-directive
    :class="`cursor-pointer ${props.class}`"
  >
    <slot />
  </component>
</template>
