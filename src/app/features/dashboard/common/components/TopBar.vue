<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup>
import MenuButton from '@d/common/components/menu/Button.vue'
import MenuDivider from '@d/common/components/menu/Divider.vue'
import MenuHeader from '@d/common/components/menu/Header.vue'
import MenuItem from '@d/common/components/menu/Item.vue'
import MenuItems from '@d/common/components/menu/Items.vue'
import Menu from '@d/common/components/menu/Menu.vue'

import { useDarkModeStore } from '@/common/stores/dark-mode-store'
import { useLoginStore } from '@/common/stores/login-store'

const loginStore = useLoginStore()

const logout = async () => {
  loginStore.logout()
  await $fetch('/auth/logout', { method: 'POST' })
  navigateTo('/')
}

const darkModeStore = useDarkModeStore()
</script>

<template>
  <div
    class="relative z-[51] flex h-[67px] justify-between items-center border-b border-slate-200"
  >
    <img
      alt="Timesheets"
      class="w-36 ml-1"
      :src="darkModeStore.darkModeValue ? '/tm.png' : '/tm-color.png'"
    />
    <Menu>
      <MenuButton
        class="block w-8 h-8 overflow-hidden rounded-full shadow-lg image-fit zoom-in intro-x"
      >
        <LucideUser />
      </MenuButton>
      <MenuItems class="w-56 mt-px text-white bg-primary" placement="top">
        <MenuHeader class="font-normal">
          <div
            :class="`font-medium ${darkModeStore.darkModeValue ? 'text-white' : 'text-[#212121]'}`"
          >
            {{ loginStore.user?.nombre ?? 'Usuario' }}
            {{ loginStore.user?.apellido ?? '' }}
          </div>
          <div
            :class="`text-xs mt-0.5 ${darkModeStore.darkModeValue ? 'text-white/70 ' : 'text-slate-500'}`"
          >
            Consultor
          </div>
        </MenuHeader>
        <MenuDivider
          :class="
            darkModeStore.darkModeValue ? 'bg-white/[0.08]' : 'bg-slate-200'
          "
        />
        <MenuItem class="hover:bg-white/5">
          <NuxtLink
            :class="`flex flex-row ${darkModeStore.darkModeValue ? 'text-white' : 'text-[#212121]'}`"
            to="/dash/cambio-contrasena"
          >
            <LucideRotateCcwKey class="w-4 h-4 mr-2" /> Cambio de Clave
          </NuxtLink>
        </MenuItem>
        <MenuItem class="hover:bg-white/5" @click="logout">
          <NuxtLink
            :class="`flex flex-row ${darkModeStore.darkModeValue ? 'text-white' : 'text-[#212121]'}`"
            @click="logout"
          >
            <LucideDoorClosed class="w-4 h-4 mr-2" /> Cerrar sesión
          </NuxtLink>
        </MenuItem>
      </MenuItems>
    </Menu>
  </div>
</template>
