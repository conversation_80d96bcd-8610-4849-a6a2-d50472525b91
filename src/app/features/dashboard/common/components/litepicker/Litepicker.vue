<!-- eslint-disable @typescript-eslint/unified-signatures -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/sort-type-constituents -->
<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import '@vuepic/vue-datepicker/dist/main.css'

import VueDatePicker from '@vuepic/vue-datepicker'
import dayjs from 'dayjs'

import { useDarkModeStore } from '@/common/stores/dark-mode-store'

const darkModeStore = useDarkModeStore()

const props = defineProps<{
  class?: string
  isBeginWeekMonday?: boolean
  modelValue?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}>()

const localValue = computed({
  get: () => {
    if (props.modelValue === undefined) return dayjs().format('YYYY-MM-DD')
    if (props.modelValue === '') return dayjs().format('YYYY-MM-DD')
    if (props.isBeginWeekMonday)
      return dayjs(props.modelValue).startOf('isoWeek').format('YYYY-MM-DD')
    return dayjs(props.modelValue).format('YYYY-MM-DD')
  },
  set: (newValue: Date) => {
    emit('update:modelValue', dayjs(newValue).format('YYYY-MM-DD'))
  }
})
</script>

<template>
  <div :class="`${props.class} flex flex-row w-48`">
    <vue-date-picker
      v-model="localValue"
      :clearable="false"
      auto-apply
      format="yyyy-MM-dd"
      :enable-time-picker="false"
      :dark="darkModeStore.darkModeValue"
      locale="es"
      @update:model-value="() => emit('change', localValue)"
    />
  </div>
</template>

<style scoped>
.dp__theme_dark {
  --dp-cell-border-radius: 10px;
  --dp-border-radius: 10px;
  --dp-background-color: #1b253b;
  --dp-highlight-color: #1b253b;
  --dp-secondary-color: #1b253b;
  --dp-tooltip-color: #1b253b;
  --dp-highlight-color: rgb(0 92 178 / 20%);
}
</style>
