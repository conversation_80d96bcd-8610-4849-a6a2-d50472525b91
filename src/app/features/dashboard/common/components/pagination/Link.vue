<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import { twMerge } from 'tailwind-merge'
import type { LiHTMLAttributes } from 'vue'

const {
  active = false,
  as = 'a',
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  class: className
} = defineProps<
  LiHTMLAttributes & {
    active?: boolean
    as?: object | string
    class?: string
  }
>()

const attrs = useAttrs()
const computedClass = computed(() =>
  twMerge([
    'min-w-0 sm:min-w-[40px] shadow-none font-normal flex items-center justify-center border-transparent text-slate-800 sm:mr-2 dark:text-slate-300 px-1 sm:px-3',
    active && '!box font-medium dark:bg-darkmode-400',
    className ?? ''
  ])
)
</script>

<template>
  <li class="flex-1 sm:flex-initial">
    <Button :as="as" :class="computedClass" v-bind="attrs">
      <slot />
    </Button>
  </li>
</template>
