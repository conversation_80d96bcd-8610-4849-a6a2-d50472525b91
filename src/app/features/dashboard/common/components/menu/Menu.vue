<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { Menu as HeadlessMenu } from '@headlessui/vue'
import { twMerge } from 'tailwind-merge'
import type { ExtractPropTypes } from 'vue'

const props = withDefaults(
  defineProps<
    /* @vue-ignore */ ExtractPropTypes<typeof HeadlessMenu> & {
      class?: string
    }
  >(),
  {
    class: ''
  }
)

const attrs = useAttrs()
const computedClass = computed(() => twMerge(['relative', props.class]))
</script>

<template>
  <HeadlessMenu as="template">
    <component :is="'div'" :class="computedClass" v-bind="attrs">
      <slot />
    </component>
  </HeadlessMenu>
</template>
