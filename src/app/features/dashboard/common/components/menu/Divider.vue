<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { twMerge } from 'tailwind-merge'

const props = withDefaults(
  defineProps<{
    class?: string
  }>(),
  {
    class: ''
  }
)

const attrs = useAttrs()
const computedClass = computed(() =>
  twMerge(['h-px my-2 -mx-2 bg-slate-200/60 dark:bg-darkmode-400', props.class])
)
</script>

<template>
  <component :is="'div'" :class="computedClass" v-bind="attrs">
    <slot />
  </component>
</template>
