<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { MenuButton as HeadlessMenuButton } from '@headlessui/vue'
import { twMerge } from 'tailwind-merge'
import type { ExtractPropTypes } from 'vue'

const props = withDefaults(
  defineProps<
    /* @vue-ignore */ ExtractPropTypes<typeof HeadlessMenuButton> & {
      class?: string
    }
  >(),
  {
    class: ''
  }
)

const attrs = useAttrs()
const computedClass = computed(() => twMerge(['cursor-pointer', props.class]))
</script>

<template>
  <HeadlessMenuButton as="template">
    <component :is="'button'" :class="computedClass" v-bind="attrs">
      <slot />
    </component>
  </HeadlessMenuButton>
</template>
