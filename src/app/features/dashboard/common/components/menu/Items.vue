<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { MenuItems as HeadlessMenuItems, TransitionRoot } from '@headlessui/vue'
import { twMerge } from 'tailwind-merge'
import type { ExtractPropTypes } from 'vue'

const props = withDefaults(
  defineProps<
    /* @vue-ignore */ ExtractPropTypes<typeof HeadlessMenuItems> & {
      class?: string
      placement?:
        | 'bottom-end'
        | 'bottom-start'
        | 'bottom'
        | 'custom'
        | 'left-end'
        | 'left-start'
        | 'left'
        | 'right-end'
        | 'right-start'
        | 'right'
        | 'top-end'
        | 'top-start'
        | 'top'
    }
  >(),
  {
    class: '',
    placement: 'bottom-end'
  }
)

const attrs = useAttrs()

const computedClass = computed(() =>
  twMerge([
    'p-2 shadow-[0px_3px_10px_#00000017] bg-white border-transparent rounded-md dark:bg-darkmode-600 dark:border-transparent',
    props.class
  ])
)
</script>

<template>
  <TransitionRoot
    as="template"
    enter="transition-all ease-linear duration-150"
    enterFrom="mt-5 invisible opacity-0 translate-y-1"
    enterTo="mt-1 visible opacity-100 translate-y-0"
    entered="mt-1"
    leave="transition-all ease-linear duration-150"
    leaveFrom="mt-1 visible opacity-100 translate-y-0"
    leaveTo="mt-5 invisible opacity-0 translate-y-1"
  >
    <div
      :class="[
        'absolute z-30 right-0',
        { 'left-0 bottom-[100%]': props.placement == 'top-start' },
        {
          'left-[50%] translate-x-[-50%] bottom-[100%]':
            props.placement == 'top'
        },
        { 'right-0 bottom-[100%]': props.placement == 'top-end' },
        { 'left-[100%] translate-y-[-50%]': props.placement == 'right-start' },
        {
          'left-[100%] top-[50%] translate-y-[-50%]': props.placement == 'right'
        },
        { 'left-[100%] bottom-0': props.placement == 'right-end' },
        { 'top-[100%] right-0': props.placement == 'bottom-end' },
        {
          'top-[100%] left-[50%] translate-x-[-50%]':
            props.placement == 'bottom'
        },
        { 'top-[100%] left-0': props.placement == 'bottom-start' },
        { 'right-[100%] translate-y-[-50%]': props.placement == 'left-start' },
        {
          'right-[100%] top-[50%] translate-y-[-50%]': props.placement == 'left'
        },
        { 'right-[100%] bottom-0': props.placement == 'left-end' }
      ]"
    >
      <HeadlessMenuItems as="template">
        <component :is="'div'" :class="computedClass" v-bind="attrs">
          <slot />
        </component>
      </HeadlessMenuItems>
    </div>
  </TransitionRoot>
</template>
