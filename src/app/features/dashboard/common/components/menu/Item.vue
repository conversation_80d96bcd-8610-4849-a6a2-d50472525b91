<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { MenuItem as HeadlessMenuItem } from '@headlessui/vue'
import { twMerge } from 'tailwind-merge'
import type { ExtractPropTypes } from 'vue'

const props = withDefaults(
  defineProps<
    /* @vue-ignore */ ExtractPropTypes<typeof HeadlessMenuItem> & {
      class?: string
    }
  >(),
  {
    class: ''
  }
)

const attrs = useAttrs()

const computedClass = computed(() =>
  twMerge([
    'cursor-pointer flex items-center p-2 transition duration-300 ease-in-out rounded-md hover:bg-slate-200/60 dark:bg-darkmode-600 dark:hover:bg-darkmode-400',
    props.class
  ])
)
</script>

<template>
  <HeadlessMenuItem as="template">
    <component :is="'a'" :class="computedClass" v-bind="attrs">
      <slot />
    </component>
  </HeadlessMenuItem>
</template>
