<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { twMerge } from 'tailwind-merge'

const props = withDefaults(
  defineProps<{
    as?: object | string
    class?: string
  }>(),
  {
    as: 'div',
    class: ''
  }
)

const attrs = useAttrs()
const computedClass = computed(() => twMerge(['p-2 font-medium', props.class]))
</script>

<template>
  <component :is="props.as" :class="computedClass" v-bind="attrs">
    <slot />
  </component>
</template>
