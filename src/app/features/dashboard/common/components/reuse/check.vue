<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<script setup lang="ts">
import FormCheck from '@c/components/form/FormCheck/Input.vue'

const props = defineProps<{
  label: string
  modelValue?: boolean
  value?: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'input', value: Event): void
}>()

const localValue = computed({
  get() {
    return props.modelValue
  },
  set(newValue: boolean) {
    emit('update:modelValue', newValue)
  }
})
</script>

<template>
  <div class="flex flex-row justify-start gap-4">
    <label class="select-none">{{ props.label }} </label>
    <form-check
      v-if="props.value === undefined"
      v-model:boolean="localValue"
      class="mr-2 border"
      type="checkbox"
    />
    <form-check
      v-else
      :value="localValue"
      class="mr-2 border"
      type="checkbox"
      @input="e => emit('input', e)"
    />
  </div>
</template>
