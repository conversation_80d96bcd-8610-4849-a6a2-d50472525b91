<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<script
  setup
  lang="ts"
  generic="T extends Record<string, string | number | boolean | null>"
>
import FormSelect from '@c/components/form/FormSelect.vue'

const selectRef = ref<HTMLSelectElement>()

const props = defineProps<{
  class?: string
  data: T[]
  defaultText: string
  displayText: (row: T) => number | string | null | undefined
  identityKey: keyof T
  label?: string
  modelValue?: string
  valueKey: keyof T
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: Event): void
}>()

const localValue = computed({
  get() {
    if (props.modelValue === undefined) {
      const firstOption = selectRef.value?.querySelector('option')
      if (firstOption !== undefined && firstOption !== null) {
        return firstOption.getAttribute('value') ?? firstOption.text
      }
      return ''
    }

    return props.modelValue
  },
  set(newValue: string) {
    emit('update:modelValue', newValue)
  }
})
</script>

<template>
  <div class="flex flex-row justify-between gap-3">
    <label v-if="props.label" class="cursor-pointer select-none mt-1">
      {{ props.label }}
    </label>
    <form-select
      v-model="localValue"
      :class="props.class ?? 'w-8/12'"
      @change="e => emit('change', e)"
    >
      <option value="">{{ props.defaultText }}</option>
      <option
        v-for="row in props.data"
        :key="row[props.identityKey]?.toString() ?? ''"
        :value="row[props.valueKey]"
      >
        {{ props.displayText(row) ?? '' }}
      </option>
    </form-select>
  </div>
</template>
