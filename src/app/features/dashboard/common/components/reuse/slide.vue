<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable unicorn/explicit-length-check -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/unified-signatures -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-redundant-type-constituents -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts" generic="T extends object">
import VButton from '@c/components/button/button.vue'
import SlideoverDescription from '@c/components/slideover/Description.vue'
import SlideoverPanel from '@c/components/slideover/Panel.vue'
import Slideover from '@c/components/slideover/Slideover.vue'

const props = defineProps<{
  disabled?: boolean
  isNotBox?: boolean
  item: T | null
  open: boolean
  title: string
}>()

const slots = useSlots()

const activeSlotCount = computed(
  () =>
    Object.entries(slots).filter(
      ([_, fn]) => typeof fn === 'function' && fn().length > 0
    ).length
)

defineEmits<(e: 'submit', value: boolean) => void>()
</script>

<template>
  <slideover size="lg" :open="props.open" @close="$emit('submit', false)">
    <slideover-panel>
      <slideover-description
        class="px-8 pt-10 pb-8 flex flex-col justify-between"
      >
        <div class="flex flex-col">
          <div class="flex flex-row w-full justify-between">
            <div class="text-xl font-medium">
              {{
                props.item == null ? `Insertar ${title}:` : `Editar ${title}:`
              }}
            </div>
            <div v-if="activeSlotCount > 1" class="flex flex-row gap-2">
              <slot name="header" />
            </div>
          </div>
          <div :class="`intro-y ${props.isNotBox ? '' : 'box'} w-full`">
            <slot />
          </div>
        </div>
        <div class="flex flex-row gap-3 justify-end">
          <v-button
            :variant="(props.disabled ?? false) ? 'danger' : 'primary'"
            class="w-24 mr-1"
            :disabled="props.disabled ?? false"
            @click="() => $emit('submit', true)"
          >
            {{ (props.disabled ?? false) ? 'Horas Excedidas' : 'Guardar' }}
          </v-button>
          <v-button
            variant="danger"
            class="w-24 mr-1"
            @click="() => $emit('submit', false)"
          >
            Cancelar
          </v-button>
        </div>
      </slideover-description>
    </slideover-panel>
  </slideover>
</template>
