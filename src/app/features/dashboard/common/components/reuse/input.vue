<!-- eslint-disable sonarjs/concise-regex -->
<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<script setup lang="ts">
import FormInput from '@c/components/form/FormInput.vue'

const props = withDefaults(
  defineProps<{
    class?: string
    label?: string
    max?: string
    modelValue?: number | string
    placeholder?: string
    readonly?: boolean
    type?: 'number' | 'text'
    value?: string
  }>(),
  { readonly: false, type: 'text' }
)

const emit = defineEmits<(e: 'update:modelValue', value: string) => void>()
const local = ref(String(props.modelValue ?? ''))

const handleInput = (e: InputEvent) => {
  if (props.type === 'number') {
    let val = (e.target as HTMLInputElement).value
    if (val === '') {
      local.value = '0'
      return
    }
    val = val.replaceAll(/[^0-9.,]/g, '')
    if (val !== '' && Number(val) > Number(props.max ?? 0))
      val = String(props.max)
    local.value = val
  } else {
    local.value = (e.target as HTMLInputElement).value
  }
}

onMounted(() => {
  if (props.value !== undefined) local.value = props.value
})

watch(
  () => props.modelValue,
  val => {
    const stringVal = String(val ?? '')
    if (stringVal !== local.value) local.value = stringVal
  },
  { immediate: true }
)

watch(local, val => {
  emit('update:modelValue', val)
})
</script>

<template>
  <div v-if="props.label" class="flex flex-row justify-between gap-4">
    <label class="select-none mt-3">{{ props.label }}</label>
    <form-input
      v-model="local"
      :max="max"
      :class="`block py-3 ${props.class ?? 'w-8/12 px-4'}`"
      :placeholder="props.placeholder ?? ''"
      :type="props.type"
      :readonly="props.readonly"
      @input="handleInput"
    />
  </div>
  <form-input
    v-else
    v-model="local"
    :max="max"
    :class="`block py-3 ${props.class ?? 'w-8/12 px-4'}`"
    :placeholder="props.placeholder ?? ''"
    :type="props.type"
    :readonly="props.readonly"
    @input="handleInput"
  />
</template>
