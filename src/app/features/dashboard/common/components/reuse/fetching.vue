<script setup lang="ts" generic="T extends object">
import Loading from '@/common/components/loading.vue'

const props = withDefaults(
  defineProps<{
    class?: string
    data: T[] | undefined
    displayData: T[]
    emptyTitle?: string
    isContained?: boolean
    isFetching: boolean
  }>(),
  {
    emptyTitle: 'No hay datos disponibles para mostrar',
    isContained: false
  }
)
</script>

<template>
  <div
    v-if="props.isFetching"
    class="flex flex-col items-center m-5 justify-end col-span-6 sm:col-span-3 xl:col-span-2"
  >
    <loading icon="three-dots" />
  </div>
  <div
    v-else-if="!props.isFetching && props.data && displayData.length > 0"
    :class="props.class ?? 'p-5'"
  >
    <slot />
  </div>
  <div
    v-else-if="!props.isFetching && props.data && displayData.length === 0"
    :class="props.isContained ? 'mt-5 p-5' : 'p-5'"
  >
    <h3 class="text-center font-bold text-xl">
      {{ props.emptyTitle }}
    </h3>
  </div>
</template>
