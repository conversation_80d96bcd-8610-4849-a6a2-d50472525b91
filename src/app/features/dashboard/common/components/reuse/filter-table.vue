<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts" generic="T extends object">
import FormInput from '@c/components/form/FormInput.vue'

import FormSelect from '@/common/components/form/FormSelect.vue'

const props = defineProps<{
  data: T[]
  metadata: { key: string; label: string }[]
}>()

const { data } = toRefs(props)

const emit = defineEmits<(e: 'filteredData', value: T[]) => void>()

const columnQuery = ref('')
const searchQuery = ref('')
const debouncedSearchQuery = useDebounce(searchQuery, 150)

const filteredData = computed(() => {
  if (!debouncedSearchQuery.value.trim()) return data.value ?? []

  return (data.value ?? []).filter(item => {
    const value = item[columnQuery.value as keyof T]
    if (typeof value === 'string') {
      return value
        .toLowerCase()
        .includes(debouncedSearchQuery.value.toLowerCase())
    } else if (typeof value === 'number') {
      return value.toString().includes(debouncedSearchQuery.value)
    }
    return false
  })
})

watch(filteredData, value => emit('filteredData', value))
</script>

<template>
  <div class="flex flex-col sm:flex-row gap-3">
    <form-select formSelectSize="lg" class="w-22">
      <option value="" @click="columnQuery = ''">Seleccionar Columna</option>
      <option
        v-for="row in props.metadata"
        :key="row.key"
        :value="row.key"
        @click="columnQuery = row.key"
      >
        {{ row.label }}
      </option>
    </form-select>
    <div class="relative text-slate-500 w-11/12">
      <form-input
        v-model="searchQuery"
        placeholder="Buscar..."
        class="pr-10"
        :disabled="columnQuery === ''"
        type="text"
      />
      <lucide-search class="absolute inset-y-0 right-0 w-4 h-4 my-auto mr-3" />
    </div>
  </div>
</template>
