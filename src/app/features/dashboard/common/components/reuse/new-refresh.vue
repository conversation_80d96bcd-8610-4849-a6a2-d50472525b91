<script setup lang="ts">
import VButton from '@c/components/button/button.vue'

const emit = defineEmits(['refresh', 'new'])

const props = withDefaults(
  defineProps<{
    disabled?: boolean
    disabledFresh?: boolean
    disabledText?: string
    isNew?: boolean
  }>(),
  {
    disabled: false,
    disabledFresh: false,
    disabledText: '<PERSON><PERSON>',
    isNew: false
  }
)
</script>

<template>
  <div class="flex flex-col sm:flex-row gap-3 my-auto">
    <v-button
      v-if="props.isNew"
      class="w-28 h-10"
      :disabled="props.disabled"
      :variant="props.disabled ? 'success' : 'primary'"
      @click="emit('new')"
    >
      {{ props.disabled ? props.disabledText : 'Nuevo' }}
    </v-button>
    <v-button
      class="h-10"
      :disabled="props.disabledFresh"
      variant="dark"
      @click="() => emit('refresh')"
    >
      <LucideRefreshCcw class="h-4 w-4" />
    </v-button>
  </div>
</template>
