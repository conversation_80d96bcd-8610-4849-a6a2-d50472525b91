<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<script setup lang="ts">
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'

const props = defineProps<{
  label: string
  modelValue?: string
}>()

const emit = defineEmits<(e: 'update:modelValue', value: string) => void>()

const local = ref(props.modelValue ?? '')

watch(
  () => props.modelValue,
  val => {
    const stringVal = String(val ?? '')
    if (stringVal !== local.value) local.value = stringVal
  },
  { immediate: true }
)

watch(local, val => {
  emit('update:modelValue', val)
})
</script>

<template>
  <div class="flex flex-row justify-between gap-3">
    <label class="select-none mt-2">{{ props.label }} </label>
    <div class="relative w-80">
      <div
        class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
      >
        <LucideCalendar class="w-4 h-4" />
      </div>
      <Litepicker
        v-model="local"
        class="pl-12"
        :options="{
          autoApply: true,
          dropdowns: {
            minYear: 2020,
            maxYear: 2035,
            months: true,
            years: true
          }
        }"
      />
    </div>
  </div>
</template>
