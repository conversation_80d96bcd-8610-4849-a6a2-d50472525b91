<!-- eslint-disable @typescript-eslint/unified-signatures -->
<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable unicorn/explicit-length-check -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script
  setup
  lang="ts"
  generic="T extends Record<string, string | number | boolean | null>"
>
import VTable from '@d/common/components/table/Table.vue'
import VTbody from '@d/common/components/table/Tbody.vue'
import VTd from '@d/common/components/table/Td.vue'
import VTh from '@d/common/components/table/Th.vue'
import VThead from '@d/common/components/table/Thead.vue'
import VTr from '@d/common/components/table/Tr.vue'
import type { Type } from 'arktype'

import VButton from '@/common/components/button/button.vue'
import FormCheck from '@/common/components/form/FormCheck/FormCheck.vue'
import FormCheckInput from '@/common/components/form/FormCheck/Input.vue'

import { useGenericTable } from './use-generic-table'

const slots = useSlots()

const props = withDefaults(
  defineProps<{
    data: T[]
    entries: Type<object>
    identityKey: keyof T
    isDeleteAction?: boolean
    isEditAction?: boolean
    isMultiCheckbox?: boolean
    isSelected?: boolean
    metadata: { key: string; label: string }[]
    paginationSize?: number
    slotClass?: string
  }>(),
  {
    isDeleteAction: false,
    isEditAction: false,
    isMultiCheckbox: false,
    isSelected: false,
    paginationSize: 50,
    slotClass: 'justify-end'
  }
)

const { slicedData, tableMetadata } = useGenericTable(
  props.data,
  props.entries,
  props.metadata,
  props.paginationSize,
  props.isDeleteAction,
  props.isEditAction
)

defineEmits<{
  (e: 'checkboxChange', value: { isChecked: boolean; row: T }): void
  (e: 'rowClick', value: T): void
  (e: 'deleteClick', value: T): void
  (e: 'editClick', value: T): void
}>()

const selectedRow = ref<T | null>(null) as Ref<T | null>

const hasSlot = !!slots.default && !!slots.default().length
</script>

<template>
  <div class="overflow-auto max-h-[360px]">
    <v-table hover sm>
      <v-thead variant="dark">
        <v-tr>
          <v-th
            v-if="props.isMultiCheckbox"
            class="whitespace-nowrap font-bold"
          >
            <lucide-check class="h-3 w-3" />
          </v-th>
          <v-th
            v-for="row in props.metadata"
            :key="row.key"
            class="whitespace-nowrap font-bold"
          >
            {{ row.label }}
          </v-th>
          <v-th
            v-if="props.isDeleteAction || props.isEditAction"
            class="whitespace-nowrap font-bold text-center"
          >
            Acciones
          </v-th>
        </v-tr>
      </v-thead>
      <v-tbody>
        <v-tr
          v-for="(el, rowIndex) in slicedData"
          :key="el[props.identityKey]?.toString() ?? rowIndex"
          :class="`dark:hover:bg-[rgb(48,55,97)] hover:bg-[#dadef7] ${
            props.isSelected &&
            selectedRow?.[props.identityKey] === el[props.identityKey]
              ? 'dark:bg-[#303761] bg-[#dadef7]'
              : ''
          }`"
          @click="
            () => {
              if (props.isSelected) selectedRow = el
              $emit('rowClick', el)
            }
          "
        >
          <v-td v-if="props.isMultiCheckbox">
            <form-check>
              <form-check-input
                type="checkbox"
                :value="el[props.identityKey]"
                @change="
                  e =>
                    $emit('checkboxChange', {
                      isChecked: (e.target as HTMLInputElement).checked,
                      row: el
                    })
                "
              />
            </form-check>
          </v-td>
          <v-td
            v-for="metaRow in tableMetadata"
            :key="metaRow.key"
            :class="
              el?.[metaRow.key] === '' ||
              el?.[metaRow.key] === null ||
              el?.[metaRow.key] === undefined
                ? 'text-slate-500 italic'
                : ''
            "
          >
            <div
              v-if="metaRow.key === 'actions'"
              class="flex flex-row gap-3 w-full justify-center"
            >
              <v-button
                v-if="props.isEditAction"
                variant="dark"
                @click="() => $emit('editClick', el)"
              >
                <lucide-pencil class="h-4 w-4" />
              </v-button>
              <v-button
                v-if="props.isDeleteAction"
                variant="dark"
                @click="() => $emit('deleteClick', el)"
              >
                <lucide-eraser class="h-4 w-4" />
              </v-button>
            </div>
            <template v-else>
              {{
                el?.[metaRow.key] === '' ||
                el?.[metaRow.key] === null ||
                el?.[metaRow.key] === undefined
                  ? 'Vacío'
                  : el?.[metaRow.key]
              }}
            </template>
          </v-td>
        </v-tr>
      </v-tbody>
    </v-table>
  </div>
  <div v-if="hasSlot" :class="`flex flex-row w-full ${props.slotClass}`">
    <div class="mt-6 flex flex-row gap-3">
      <slot />
    </div>
  </div>
</template>
