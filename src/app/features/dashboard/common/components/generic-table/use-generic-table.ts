import type { Type } from 'arktype'
import { getProps } from '~/utils/models'

export const useGenericTable = <
  T extends Record<string, string | number | boolean | null>
>(
  data: T[],
  entries: Type<object>,
  metadata: { label: string; key: string }[],
  paginationSize: number,
  isDeleteAction: boolean,
  isEditAction: boolean
) => {
  const paginationActual = ref(1)

  const tableMetadata = computed(() => {
    const realMetadata = [...metadata].flatMap(data => {
      const entry = getProps(entries.toJSON()).find(el => el.key === data.key)
      return entry === undefined
        ? []
        : [{ key: data.key, title: data.label, type: entry.value }]
    })
    if (isEditAction || isDeleteAction)
      realMetadata.push({ key: 'actions', title: 'Acciones', type: 'string' })

    return realMetadata
  })

  const totalRows = computed(() => data.length)

  const paginationCount = computed(() =>
    Math.ceil(totalRows.value / paginationSize)
  )

  const slicedData = computed(() =>
    data.slice(
      (paginationActual.value - 1) * paginationSize,
      paginationSize * paginationActual.value
    )
  )

  return {
    paginationActual,
    paginationCount,
    slicedData,
    tableMetadata,
    totalRows
  }
}
