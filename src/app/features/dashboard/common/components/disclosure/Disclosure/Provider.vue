<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import type { ComputedRef } from 'vue'

const props = withDefaults(
  defineProps<{
    close?: (ref?: HTMLElement) => void
    index?: number
    open?: boolean
  }>(),
  {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    close: () => {},
    index: 0,
    open: true
  }
)

provide<
  ComputedRef<{
    close: () => void
    index: number
    open: boolean
  }>
>(
  'disclosure',
  computed(() => ({
    close: props.close,
    index: props.index,
    open: props.open
  }))
)
</script>

<template>
  <slot />
</template>
