<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { Disclosure as HeadlessDisclosure } from '@headlessui/vue'
import { twMerge } from 'tailwind-merge'
import type { ComputedRef, ExtractPropTypes } from 'vue'

import Provider from './Provider.vue'

const props = withDefaults(
  defineProps<
    /* @vue-ignore */ ExtractPropTypes<typeof HeadlessDisclosure> & {
      class?: string
      index?: number
      openDisc?: boolean
    }
  >(),
  {
    class: '',
    index: 0
  }
)

const group = inject<
  ComputedRef<{
    selectedIndex?: number
    setSelectedIndex: (value: number) => void
    variant?: 'boxed' | 'default'
  }>
>('group')

const attrs = useAttrs()
const computedClass = computed(() =>
  twMerge([
    'py-4 first:-mt-4 last:-mb-4',
    '[&:not(:last-child)]:border-b [&:not(:last-child)]:border-slate-200/60 [&:not(:last-child)]:dark:border-darkmode-400',
    group?.value.variant === 'boxed' &&
      'p-4 first:mt-0 last:mb-0 border border-slate-200/60 mt-3 dark:border-darkmode-400',
    props.class
  ])
)
</script>

<template>
  <HeadlessDisclosure
    v-slot="{ open, close }"
    as="div"
    :defaultOpen="group?.selectedIndex === props.index || props.openDisc"
    :class="computedClass"
    v-bind="attrs"
  >
    <Provider :open="open" :close="close" :index="props.index">
      <slot :open="open" :close="close" />
    </Provider>
  </HeadlessDisclosure>
</template>
