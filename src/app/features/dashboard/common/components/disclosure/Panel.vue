<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import {
  DisclosurePanel as HeadlessDisclosurePanel,
  TransitionRoot
} from '@headlessui/vue'
import { twMerge } from 'tailwind-merge'
import type { ExtractPropTypes } from 'vue'

const props = withDefaults(
  defineProps<
    /* @vue-ignore */ ExtractPropTypes<typeof HeadlessDisclosurePanel> & {
      as?: object | string
      class?: string
    }
  >(),
  {
    as: 'div',
    class: ''
  }
)

const attrs = useAttrs()
const computedClass = computed(() =>
  twMerge([
    'mt-3 text-slate-700 leading-relaxed dark:text-slate-400',
    props.class
  ])
)
</script>

<template>
  <TransitionRoot
    as="template"
    enter="overflow-hidden transition-all linear duration-[400ms]"
    enterFrom="mt-0 max-h-0 invisible opacity-0"
    enterTo="mt-3 max-h-[2000px] visible opacity-100"
    entered="mt-3"
    leave="overflow-hidden transition-all linear duration-500"
    leaveFrom="mt-3 max-h-[2000px] visible opacity-100"
    leaveTo="mt-0 max-h-0 invisible opacity-0"
  >
    <HeadlessDisclosurePanel
      :as="props.as"
      :class="computedClass"
      v-bind="attrs"
    >
      <slot />
    </HeadlessDisclosurePanel>
  </TransitionRoot>
</template>
