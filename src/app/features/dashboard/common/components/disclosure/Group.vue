<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import type { ComputedRef } from 'vue'

const props = withDefaults(
  defineProps<{
    as?: object | string
    selectedIndex?: number
    variant?: 'boxed' | 'default'
  }>(),
  {
    as: 'div',
    selectedIndex: 0,
    variant: 'default'
  }
)

const active = ref(props.selectedIndex)

const slots = useSlots()
const setActive = (value: number) => (active.value = value)

provide<
  ComputedRef<{
    selectedIndex?: number
    setSelectedIndex: (value: number) => void
    variant?: 'boxed' | 'default'
  }>
>(
  'group',
  computed(() => ({
    selectedIndex: active.value,
    setSelectedIndex: setActive,
    variant: props.variant
  }))
)
</script>

<template>
  <component :is="props.as">
    <component
      :is="item"
      v-for="(item, key) in slots.default && slots.default()"
      :key="key"
      :index="key"
    />
  </component>
</template>
