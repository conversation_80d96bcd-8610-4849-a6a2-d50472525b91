<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<script setup lang="ts">
import { DisclosureButton as HeadlessDisclosureButton } from '@headlessui/vue'
import { twMerge } from 'tailwind-merge'
import type { ComputedRef, ExtractPropTypes } from 'vue'

const props = withDefaults(
  defineProps<
    /* @vue-ignore */ ExtractPropTypes<typeof HeadlessDisclosureButton> & {
      as?: object | string
      class?: string
      openDisc?: boolean
    }
  >(),
  {
    as: 'button',
    class: '',
    openDisc: false
  }
)

const disclosure = inject<
  ComputedRef<{
    close: () => void
    index: number
    open: boolean
  }>
>('disclosure')

const group = inject<
  ComputedRef<{
    selectedIndex?: number
    setSelectedIndex: (value: number) => void
    variant?: 'boxed' | 'default'
  }>
>('group')

watch(
  () => group?.value,
  () => {
    // eslint-disable-next-line sonarjs/no-unused-expressions, @typescript-eslint/no-unused-expressions
    group?.value.selectedIndex !== disclosure?.value.index &&
      disclosure?.value.close()
  }
)

const attrs = useAttrs()
const computedClass = computed(() =>
  twMerge([
    'outline-none py-4 -my-4 font-medium w-full text-left dark:text-slate-400',
    (disclosure?.value.open ?? false) && 'text-primary dark:text-slate-300',
    props.class
  ])
)
</script>

<template>
  <HeadlessDisclosureButton
    :as="props.as"
    :class="computedClass"
    v-bind="attrs"
    @click="() => disclosure && group?.setSelectedIndex(disclosure.index)"
  >
    <slot />
  </HeadlessDisclosureButton>
</template>
