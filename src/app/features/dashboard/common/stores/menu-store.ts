import { useLoginStore } from '@/common/stores/login-store'
import type { Consultores } from '@d/admin/models/consultores'
import { menuContent, type Menu } from '@d/common/data/menu-data'

export type MenuState = { menu: (Menu | 'divider')[] }

export const useMenuStore = defineStore('menu', () => {
  const loginStore = useLoginStore()

  const menu = ref<(Menu | 'divider')[]>(
    menuContent(loginStore.user as Consultores)
  )

  return { menu }
})
