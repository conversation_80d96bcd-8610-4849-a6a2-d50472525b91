<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-redundant-type-constituents -->
<!-- eslint-disable no-undef -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import '~/resources/themes/side-nav.css'

import MobileMenu from '@d/common/components/mobile/MobileMenu.vue'
import Tippy from '@d/common/components/tippy/tippy.vue'
import TopBar from '@d/common/components/TopBar.vue'
import { useMenuStore } from '@d/common/stores/menu-store'
import { useRoute } from 'vue-router'

import {
  enter,
  forceActiveMenu,
  type FormattedMenu,
  leave,
  linkTo,
  nestedMenu,
  type ProvideForceActiveMenu,
  type Route
} from './side-menu'

const route: Route = useRoute()
const formattedMenu = reactive<(FormattedMenu | 'divider')[]>([])
const menuStore = useMenuStore()

const setFormattedMenu = (
  computedFormattedMenu: (FormattedMenu | 'divider')[]
) => Object.assign(formattedMenu, computedFormattedMenu)

const menu = computed(() =>
  // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-member-access
  nestedMenu(menuStore.menu, route)
)

provide<ProvideForceActiveMenu>('forceActiveMenu', (page: string) => {
  forceActiveMenu(route, page)
  setFormattedMenu(menu.value)
})

watch(menu, () => setFormattedMenu(menu.value))

watch(
  computed(() => route.path),
  () => {
    delete route.forceActiveMenu
  }
)

const windowWidth = ref(window.innerWidth)

onMounted(() => {
  if (import.meta.client) {
    setFormattedMenu(menu.value)
    window.addEventListener('resize', () => {
      windowWidth.value = window.innerWidth
    })
  }
})
</script>

<template>
  <div
    :class="[
      'rubick px-5 sm:px-8 py-5',
      'before:content-[\'\'] before:bg-gradient-to-b before:from-theme-1 before:to-theme-2 dark:before:from-darkmode-800 dark:before:to-darkmode-800 before:fixed before:inset-0 before:z-[-1]'
    ]"
  >
    <MobileMenu />
    <div class="mt-[4.7rem] flex md:mt-0">
      <nav
        class="side-nav hidden w-[80px] overflow-x-hidden pb-16 pr-5 md:block xl:w-[230px]"
      >
        <NuxtLink to="/dash" class="flex items-center pt-4 pl-6 intro-x">
          <img alt="Timesheets" class="w-40" src="/logo-large.png" />
        </NuxtLink>
        <div class="my-6 side-nav__divider"></div>
        <ul>
          <template v-for="(menuItem, menuKey) in formattedMenu">
            <li
              v-if="menuItem == 'divider'"
              :key="'divider-' + menuKey"
              type="li"
              class="my-6 side-nav__divider"
            ></li>
            <li v-else :key="menuKey">
              <Tippy
                as="a"
                :content="menuItem.title"
                :options="{
                  placement: 'right'
                }"
                :disable="windowWidth > 1260"
                :href="menuItem.subMenu ? '#' : menuItem.page || '#'"
                :class="[
                  menuItem.active ? 'side-menu side-menu--active' : 'side-menu'
                ]"
                @click="
                  (event: MouseEvent) => {
                    event.preventDefault()
                    linkTo(menuItem)
                  }
                "
              >
                <div class="side-menu__icon">
                  <component :is="menuItem.icon" />
                </div>
                <div class="side-menu__title">
                  {{ menuItem.title }}
                  <div
                    v-if="menuItem.subMenu"
                    :class="[
                      'side-menu__sub-icon',
                      { 'transform rotate-180': menuItem.activeDropdown }
                    ]"
                  >
                    <LucideChevronDown />
                  </div>
                </div>
              </Tippy>
              <Transition
                :show="menuItem.subMenu && menuItem.activeDropdown"
                @enter="enter"
                @leave="leave"
              >
                <ul
                  v-if="menuItem.subMenu && menuItem.activeDropdown"
                  :class="{ 'side-menu__sub-open': menuItem.activeDropdown }"
                >
                  <li
                    v-for="(subMenu, subMenuKey) in menuItem.subMenu"
                    :key="subMenuKey"
                  >
                    <Tippy
                      as="a"
                      :content="subMenu.title"
                      :options="{
                        placement: 'right'
                      }"
                      :disable="windowWidth > 1260"
                      :href="subMenu.subMenu ? '#' : subMenu.page || '#'"
                      :class="[
                        subMenu.active
                          ? 'side-menu side-menu--active'
                          : 'side-menu'
                      ]"
                      @click="
                        (event: MouseEvent) => {
                          event.preventDefault()
                          linkTo(subMenu, menuItem)
                        }
                      "
                    >
                      <div class="side-menu__icon">
                        <component :is="subMenu.icon" />
                      </div>
                      <div class="side-menu__title">
                        {{ subMenu.title }}
                        <div
                          v-if="subMenu.subMenu"
                          :class="[
                            'side-menu__sub-icon',
                            { 'transform rotate-180': subMenu.activeDropdown }
                          ]"
                        >
                          <LucideChevronDown />
                        </div>
                      </div>
                    </Tippy>
                    <Transition
                      v-if="subMenu.subMenu"
                      :show="subMenu.activeDropdown"
                      @enter="enter"
                      @leave="leave"
                    >
                      <ul
                        v-if="subMenu.subMenu && subMenu.activeDropdown"
                        :class="{
                          'side-menu__sub-open': subMenu.activeDropdown
                        }"
                      >
                        <li
                          v-for="(
                            lastSubMenu, lastSubMenuKey
                          ) in subMenu.subMenu"
                          :key="lastSubMenuKey"
                        >
                          <Tippy
                            as="a"
                            :content="lastSubMenu.title"
                            :options="{
                              placement: 'right'
                            }"
                            :disable="windowWidth > 1260"
                            :href="lastSubMenu.page || '#'"
                            :class="[
                              lastSubMenu.active
                                ? 'side-menu side-menu--active'
                                : 'side-menu'
                            ]"
                            @click="
                              (event: MouseEvent) => {
                                event.preventDefault()
                                linkTo(lastSubMenu)
                              }
                            "
                          >
                            <div class="side-menu__icon">
                              <component :is="lastSubMenu.icon" />
                            </div>
                            <div class="side-menu__title">
                              {{ lastSubMenu.title }}
                            </div>
                          </Tippy>
                        </li>
                      </ul>
                    </Transition>
                  </li>
                </ul>
              </Transition>
            </li>
          </template>
        </ul>
      </nav>
      <div
        class="md:max-w-auto min-h-screen min-w-0 max-w-full flex-1 rounded-[30px] bg-slate-100 px-4 pb-10 before:block before:h-px before:w-full before:content-[''] dark:bg-darkmode-700 md:px-[22px]"
      >
        <TopBar />
        <slot />
      </div>
    </div>
  </div>
</template>
