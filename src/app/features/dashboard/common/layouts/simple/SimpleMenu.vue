<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-redundant-type-constituents -->
<!-- eslint-disable no-undef -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import '~/resources/themes/side-nav.css'

import MobileMenu from '@d/common/components/mobile/MobileMenu.vue'
import Tippy from '@d/common/components/tippy/tippy.vue'
import TopBar from '@d/common/components/TopBar.vue'
import { useMenuStore } from '@d/common/stores/menu-store'
import { useRoute, useRouter } from 'vue-router'

import {
  enter,
  forceActiveMenu,
  type FormattedMenu,
  leave,
  linkTo,
  nestedMenu,
  type ProvideForceActiveMenu,
  type Route
} from './simple-menu'

const route: Route = useRoute()
const router = useRouter()
const formattedMenu = reactive<(FormattedMenu | 'divider')[]>([])
const menuStore = useMenuStore()

const setFormattedMenu = (
  computedFormattedMenu: (FormattedMenu | 'divider')[]
) => {
  Object.assign(formattedMenu, computedFormattedMenu)
}
const menu = computed(() =>
  // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-return
  nestedMenu(menuStore.menu, route)
)

provide<ProvideForceActiveMenu>('forceActiveMenu', (page: string) => {
  forceActiveMenu(route, page)
  setFormattedMenu(menu.value)
})

watch(menu, () => setFormattedMenu(menu.value))

watch(
  computed(() => route.path),
  () => {
    delete route.forceActiveMenu
  }
)

onMounted(() => setFormattedMenu(menu.value))
</script>

<template>
  <div
    :class="[
      'rubick px-5 sm:px-8 py-5',
      'before:content-[\'\'] before:bg-gradient-to-b before:from-theme-1 before:to-theme-2 dark:before:from-darkmode-800 dark:before:to-darkmode-800 before:fixed before:inset-0 before:z-[-1]'
    ]"
  >
    <MobileMenu />
    <div class="flex mt-[4.7rem] md:mt-0">
      <nav
        class="side-nav side-nav--simple pr-5 pb-16 overflow-x-hidden hidden md:block w-[80px]"
      >
        <NuxtLink to="/dash" class="flex items-center intro-x">
          <img alt="Timesheets" class="w-40" src="/logo.png" />
        </NuxtLink>
        <div class="my-6 side-nav__divider"></div>
        <ul>
          <template v-for="(menuItem, menuKey) in formattedMenu">
            <li
              v-if="menuItem == 'divider'"
              :key="'divider-' + menuKey"
              type="li"
              class="my-6 side-nav__divider"
            ></li>
            <li v-else :key="menuKey">
              <Tippy
                as="a"
                :content="menuItem.title"
                :options="{
                  placement: 'right'
                }"
                :href="
                  menuItem.subMenu
                    ? '#'
                    : ((page: string | undefined) => {
                        try {
                          return router.resolve({
                            name: page
                          }).fullPath
                        } catch (err) {
                          return ''
                        }
                      })(menuItem.page)
                "
                :class="[
                  menuItem.active ? 'side-menu side-menu--active' : 'side-menu'
                ]"
                @click="
                  (event: MouseEvent) => {
                    event.preventDefault()
                    linkTo(menuItem)
                    setFormattedMenu([...formattedMenu])
                  }
                "
              >
                <div class="side-menu__icon">
                  <component :is="menuItem.icon" />
                </div>
                <div class="side-menu__title">
                  {{ menuItem.title }}
                  <div
                    v-if="menuItem.subMenu"
                    :class="[
                      'side-menu__sub-icon',
                      { 'transform rotate-180': menuItem.activeDropdown }
                    ]"
                  >
                    <LucideChevronDown />
                  </div>
                </div>
              </Tippy>
              <Transition
                :show="menuItem.subMenu && menuItem.activeDropdown"
                @enter="enter"
                @leave="leave"
              >
                <ul
                  v-if="menuItem.subMenu && menuItem.activeDropdown"
                  :class="{ 'side-menu__sub-open': menuItem.activeDropdown }"
                >
                  <li
                    v-for="(subMenu, subMenuKey) in menuItem.subMenu"
                    :key="subMenuKey"
                  >
                    <Tippy
                      as="a"
                      :content="subMenu.title"
                      :options="{
                        placement: 'right'
                      }"
                      :href="
                        subMenu.subMenu
                          ? '#'
                          : ((page: string | undefined) => {
                              try {
                                return router.resolve({
                                  name: page
                                }).fullPath
                              } catch (err) {
                                return ''
                              }
                            })(subMenu.page)
                      "
                      :class="[
                        subMenu.active
                          ? 'side-menu side-menu--active'
                          : 'side-menu'
                      ]"
                      @click="
                        (event: MouseEvent) => {
                          event.preventDefault()
                          linkTo(subMenu)
                          setFormattedMenu([...formattedMenu])
                        }
                      "
                    >
                      <div class="side-menu__icon">
                        <component :is="subMenu.icon" />
                      </div>
                      <div class="side-menu__title">
                        {{ subMenu.title }}
                        <div
                          v-if="subMenu.subMenu"
                          :class="[
                            'side-menu__sub-icon',
                            { 'transform rotate-180': subMenu.activeDropdown }
                          ]"
                        >
                          <LucideChevronDown />
                        </div>
                      </div>
                    </Tippy>
                    <Transition
                      :show="menuItem.activeDropdown && subMenu.activeDropdown"
                      @enter="enter"
                      @leave="leave"
                    >
                      <ul
                        v-if="subMenu.subMenu && subMenu.activeDropdown"
                        :class="{
                          'side-menu__sub-open': subMenu.activeDropdown
                        }"
                      >
                        <li
                          v-for="(
                            lastSubMenu, lastSubMenuKey
                          ) in subMenu.subMenu"
                          :key="lastSubMenuKey"
                        >
                          <Tippy
                            as="a"
                            :content="lastSubMenu.title"
                            :options="{
                              placement: 'right'
                            }"
                            :href="
                              lastSubMenu.subMenu
                                ? '#'
                                : ((page: string | undefined) => {
                                    try {
                                      return router.resolve({
                                        name: page
                                      }).fullPath
                                    } catch (err) {
                                      return ''
                                    }
                                  })(lastSubMenu.page)
                            "
                            :class="[
                              lastSubMenu.active
                                ? 'side-menu side-menu--active'
                                : 'side-menu'
                            ]"
                            @click="
                              (event: MouseEvent) => {
                                event.preventDefault()
                                linkTo(lastSubMenu)
                                setFormattedMenu([...formattedMenu])
                              }
                            "
                          >
                            <div class="side-menu__icon">
                              <component :is="lastSubMenu.icon" />
                            </div>
                            <div class="side-menu__title">
                              {{ lastSubMenu.title }}
                            </div>
                          </Tippy>
                        </li>
                      </ul>
                    </Transition>
                  </li>
                </ul>
              </Transition>
            </li>
          </template>
        </ul>
      </nav>
      <div
        class="rounded-[30px] min-w-0 min-h-screen flex-1 pb-10 bg-slate-100 dark:bg-darkmode-700 px-4 md:px-[22px] max-w-full md:max-w-auto before:content-[''] before:w-full before:h-px before:block"
      >
        <TopBar />
        <slot />
      </div>
    </div>
  </div>
</template>
