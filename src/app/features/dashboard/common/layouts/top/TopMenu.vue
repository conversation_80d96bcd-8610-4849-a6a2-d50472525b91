<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-redundant-type-constituents -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<script setup lang="ts">
import '~/resources/themes/top-nav.css'

import MenuButton from '@d/common/components/menu/Button.vue'
import MenuDivider from '@d/common/components/menu/Divider.vue'
import MenuHeader from '@d/common/components/menu/Header.vue'
import MenuItem from '@d/common/components/menu/Item.vue'
import MenuItems from '@d/common/components/menu/Items.vue'
import Menu from '@d/common/components/menu/Menu.vue'
import MobileMenu from '@d/common/components/mobile/MobileMenu.vue'
import { useMenuStore } from '@d/common/stores/menu-store'
import { useRoute, useRouter } from 'vue-router'

import { useLoginStore } from '@/common/stores/login-store'

import {
  forceActiveMenu,
  type FormattedMenu,
  linkTo,
  nestedMenu,
  type ProvideForceActiveMenu,
  type Route
} from './top-menu'

const route: Route = useRoute()
const router = useRouter()
const formattedMenu = reactive<(FormattedMenu | 'divider')[]>([])
const menuStore = useMenuStore()
const loginStore = useLoginStore()

const setFormattedMenu = (
  computedFormattedMenu: (FormattedMenu | 'divider')[]
) => Object.assign(formattedMenu, computedFormattedMenu)

const menu = computed(() =>
  // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-member-access
  nestedMenu(menuStore.menu, route)
)

const logout = async () => {
  loginStore.logout()
  await $fetch('/auth/logout', { method: 'POST' })
  navigateTo('/')
}

provide<ProvideForceActiveMenu>('forceActiveMenu', (page: string) => {
  forceActiveMenu(route, page)
  setFormattedMenu(menu.value)
})

watch(menu, () => setFormattedMenu(menu.value))

watch(
  computed(() => route.path),
  () => {
    delete route.forceActiveMenu
  }
)

onMounted(() => setFormattedMenu(menu.value))
</script>

<template>
  <div
    :class="[
      'rubick px-5 sm:px-8 py-5',
      'before:content-[\'\'] before:bg-gradient-to-b before:from-theme-1 before:to-theme-2 dark:before:from-darkmode-800 dark:before:to-darkmode-800 before:fixed before:inset-0 before:z-[-1]'
    ]"
  >
    <MobileMenu />
    <div
      class="border-b border-white/[0.08] mt-[2.2rem] md:-mt-5 -mx-3 sm:-mx-8 px-3 sm:px-8 pt-3 md:pt-0 mb-10"
    >
      <div class="flex items-center h-[70px] z-[51] relative">
        <NuxtLink to="/dash" class="hidden -intro-x md:flex">
          <img alt="Timesheets" class="w-36" src="/logo-large.png" />
        </NuxtLink>
        <div class="flex flex-row w-full justify-between">
          <img alt="Timesheets" class="w-36 border-l ml-4 pl-3" src="/tm.png" />
          <Menu>
            <MenuButton
              class="block w-8 h-8 overflow-hidden scale-110 rounded-full shadow-lg image-fit zoom-in intro-x"
            >
              <LucideUser />
            </MenuButton>
            <MenuItems class="w-56 mt-px text-white bg-primary" placement="top">
              <MenuHeader class="font-normal">
                <div class="font-medium">
                  {{ loginStore.user?.nombre ?? 'Usuario' }}
                  {{ loginStore.user?.apellido ?? '' }}
                </div>
                <div class="text-xs text-white/70 mt-0.5 dark:text-slate-500">
                  Consultor
                </div>
              </MenuHeader>
              <MenuDivider class="bg-white/[0.08]" />
              <MenuItem class="hover:bg-white/5">
                <NuxtLink class="flex flex-row" to="/dash/cambio-contrasena">
                  <LucideRotateCcwKey class="w-4 h-4 mr-2" /> Cambio de Clave
                </NuxtLink>
              </MenuItem>
              <MenuItem class="hover:bg-white/5" @click="logout">
                <LucideDoorClosed class="w-4 h-4 mr-2" /> Cerrar sesión
              </MenuItem>
            </MenuItems>
          </Menu>
        </div>
      </div>
    </div>
    <nav class="relative z-50 hidden top-nav md:block">
      <ul class="pb-3 xl:pb-0 xl:px-[50px] flex flex-wrap">
        <li v-for="(menuItem, menuKey) in formattedMenu" :key="menuKey">
          <template v-if="menuItem != 'divider'">
            <a
              :href="
                menuItem.subMenu
                  ? '#'
                  : ((page: string | undefined) => {
                      try {
                        return router.resolve({
                          name: page
                        }).fullPath
                      } catch (err) {
                        return ''
                      }
                    })(menuItem.page)
              "
              :class="[
                menuItem.active ? 'top-menu top-menu--active' : 'top-menu'
              ]"
              @click="
                (event: MouseEvent) => {
                  event.preventDefault()
                  linkTo(menuItem)
                }
              "
            >
              <div class="top-menu__icon">
                <component :is="menuItem.icon" />
              </div>
              <div class="top-menu__title">
                {{ menuItem.title }}
                <LucideChevronDown
                  v-if="menuItem.subMenu"
                  class="top-menu__sub-icon"
                />
              </div>
            </a>
            <ul
              v-if="menuItem.subMenu"
              :class="{ 'side-menu__sub-open': menuItem.activeDropdown }"
            >
              <li
                v-for="(subMenu, subMenuKey) in menuItem.subMenu"
                :key="subMenuKey"
              >
                <a
                  :href="
                    subMenu.subMenu
                      ? '#'
                      : ((page: string | undefined) => {
                          try {
                            return router.resolve({
                              name: page
                            }).fullPath
                          } catch (err) {
                            return ''
                          }
                        })(subMenu.page)
                  "
                  class="top-menu"
                  @click="
                    (event: MouseEvent) => {
                      event.preventDefault()
                      linkTo(subMenu)
                    }
                  "
                >
                  <div class="top-menu__icon">
                    <component :is="subMenu.icon" />
                  </div>
                  <div class="top-menu__title">
                    {{ subMenu.title }}
                    <LucideChevronDown
                      v-if="subMenu.subMenu"
                      class="top-menu__sub-icon"
                    />
                  </div>
                </a>
                <ul
                  v-if="subMenu.subMenu"
                  :class="{ 'side-menu__sub-open': subMenu.activeDropdown }"
                >
                  <li
                    v-for="(lastSubMenu, lastSubMenuKey) in subMenu.subMenu"
                    :key="lastSubMenuKey"
                  >
                    <a
                      :href="
                        lastSubMenu.subMenu
                          ? '#'
                          : ((page: string | undefined) => {
                              try {
                                return router.resolve({
                                  name: page
                                }).fullPath
                              } catch (err) {
                                return ''
                              }
                            })(lastSubMenu.page)
                      "
                      class="top-menu"
                      @click="
                        (event: MouseEvent) => {
                          event.preventDefault()
                          linkTo(lastSubMenu)
                        }
                      "
                    >
                      <div class="top-menu__icon">
                        <component :is="lastSubMenu.icon" />
                      </div>
                      <div class="top-menu__title">
                        {{ lastSubMenu.title }}
                      </div>
                    </a>
                  </li>
                </ul>
              </li>
            </ul>
          </template>
        </li>
      </ul>
    </nav>
    <div
      class="rounded-[30px] min-w-0 min-h-screen flex-1 pb-10 bg-slate-100 dark:bg-darkmode-700 px-4 md:px-[22px] max-w-full md:max-w-auto before:content-[''] before:w-full before:h-px before:block"
    >
      <slot />
    </div>
  </div>
</template>
