import type { RouteLocationNormalizedLoaded } from 'vue-router'
import type { Menu } from '@d/common/data/menu-data'

export type Route = RouteLocationNormalizedLoaded & {
  forceActiveMenu?: string
}

export type FormattedMenu = Menu & {
  active?: boolean
  activeDropdown?: boolean
  subMenu?: FormattedMenu[]
}

export type ProvideForceActiveMenu = (page: string) => void

export const forceActiveMenu = (route: Route, page: string) => {
  route.forceActiveMenu = page
}

const findActiveMenu = (subMenu: Menu[], route: Route): boolean => {
  let match = false
  subMenu.forEach(item => {
    if (
      ((route.forceActiveMenu !== undefined &&
        item.page === route.forceActiveMenu) ||
        (route.forceActiveMenu === undefined && item.page === route.name)) &&
      !item.ignore
    ) {
      match = true
    } else if (!match && item.subMenu) {
      match = findActiveMenu(item.subMenu, route)
    }
  })
  return match
}

export const nestedMenu = (menu: Array<Menu | 'divider'>, route: Route) => {
  const formattedMenu: Array<FormattedMenu | 'divider'> = []
  menu.forEach(item => {
    if (typeof item !== 'string') {
      const menuItem: FormattedMenu = {
        icon: item.icon,
        title: item.title,
        page: item.page,
        subMenu: item.subMenu,
        ignore: item.ignore
      }
      menuItem.active =
        ((route.forceActiveMenu !== undefined &&
          menuItem.page === route.forceActiveMenu) ||
          (route.forceActiveMenu === undefined &&
            menuItem.page === route.name) ||
          (menuItem.subMenu && findActiveMenu(menuItem.subMenu, route))) &&
        !menuItem.ignore

      if (menuItem.subMenu) {
        menuItem.activeDropdown = findActiveMenu(menuItem.subMenu, route)

        // Nested menu
        const subMenu: Array<FormattedMenu> = []
        nestedMenu(menuItem.subMenu, route).map(
          menu => typeof menu !== 'string' && subMenu.push(menu)
        )
        menuItem.subMenu = subMenu
      }

      formattedMenu.push(menuItem)
    } else {
      formattedMenu.push(item)
    }
  })

  return formattedMenu
}

export const linkTo = (menu: FormattedMenu) => {
  if (menu.subMenu) {
    menu.activeDropdown = !menu.activeDropdown
  } else {
    if (menu.page !== undefined) navigateTo(menu.page)
  }
}
