export const useSelected = <T extends Record<string, unknown>>(
  key: keyof T
) => {
  const selectedRows = shallowRef<T[]>([])
  const selectedRowsLength = computed(() => selectedRows.value.length)

  const handleCheckboxChange = (isChecked: boolean, row: T) =>
    isChecked
      ? selectedRows.value.push(row)
      : (selectedRows.value = selectedRows.value.filter(
          item => item[key] !== row[key]
        ))

  return {
    selectedRows,
    handleCheckboxChange,
    selectedRowsLength
  }
}
