import {
  useMutation,
  type MutationFunction,
  type QueryObserverResult,
  type RefetchOptions
} from '@tanstack/vue-query'
import { useAlertStore } from '@c/stores/alert-store'

export const useAlertMutate = <T, U, V>({
  mutationFn,
  refetchSecondary,
  alertSuccessMessage,
  alertErrorMessage
}: {
  mutationFn: MutationFunction<T, V>
  refetchSecondary?: (
    options?: RefetchOptions
  ) => Promise<QueryObserverResult<U>>
  alertSuccessMessage?: string
  alertErrorMessage?: string
}) => {
  const alertStore = useAlertStore()

  const { mutate, mutateAsync, isPending } = useMutation({
    mutationFn,
    onError: () => alertStore.showErrorAlert(alertErrorMessage),
    onSuccess: () => {
      alertStore.showSuccessAlert(alertSuccessMessage)
      if (refetchSecondary) refetchSecondary()
    }
  })

  return { mutate, mutateAsync, isPending }
}
