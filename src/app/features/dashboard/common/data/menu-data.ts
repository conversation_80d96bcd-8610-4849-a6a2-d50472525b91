import type { Consultores } from '@d/admin/models/consultores'
import {
  LucideCalendarClock,
  LucideHouse,
  /*LucideShieldUser,
  LucideFileText,
  LucideListCheck,
  LucideListChecks,
  LucideUserCheck,
  LucideBookOpenCheck,*/
  LucideTreePalm,
  // LucideScanEye,
  type LucideProps,
  LucideClipboardClock
  /* LucideTicketsPlane
  LucideUserLock,
  LucideBookKey,
  LucideBrickWallFire,
  LucideFolderLock,
  LucideUserPen,
  LucideBriefcase,
  LucideLandmark,
  LucideCalendarSync,
  LucideClock4,
  LucideClockArrowDown,
  LucideGalleryHorizontalEnd,
  LucideTentTree,
  LucideSiren,
  LucideBolt*/
} from 'lucide-vue-next'
import type { FunctionalComponent } from 'vue'

export type Menu = {
  icon: FunctionalComponent<LucideProps, {}, any, {}>
  title: string
  page?: string
  subMenu?: Menu[]
  ignore?: boolean
}

const commonMenu: Array<Menu | 'divider'> = [
  {
    icon: LucideHouse,
    title: 'Inicio',
    subMenu: [
      {
        icon: LucideCalendarClock,
        title: 'Horas Trabajadas',
        page: '/dash'
      },
      {
        icon: LucideClipboardClock,
        title: 'Inf. Horas Registradas',
        page: '/dash/inf-horas-registradas'
      },
      {
        icon: LucideTreePalm,
        title: 'Solicitudes Vacaciones',
        page: '/dash/solicitudes-vacaciones'
      }
    ]
  },
  'divider'
]

export const menuContent = (_: Partial<Consultores>): (Menu | 'divider')[] => [
  ...commonMenu
  /*...(user.role !== 'user'
    ? [
        {
          icon: LucideShieldUser,
          title: 'Administración',
          subMenu: [
            ...(user.role !== 'pm'
              ? [
                  {
                    icon: LucideListCheck,
                    page: '/dash/admin/aprob-wm',
                    title: 'Aprobaciones Horas WM'
                  }
                ]
              : []),
            ...(user.role !== 'wm'
              ? [
                  {
                    icon: LucideListCheck,
                    page: '/dash/admin/aprob-pm',
                    title: 'Aprobaciones Horas PM'
                  }
                ]
              : []),
            ...(user.role !== 'pm'
              ? [
                  {
                    icon: LucideListCheck,
                    page: '/dash/admin/aprob-vac',
                    title: 'Aprobaciones Vacaciones'
                  }
                ]
              : []),
            ...(user.role !== 'wm'
              ? [
                  {
                    icon: LucideListChecks,
                    page: '/dash/admin/tareas',
                    title: 'Tareas'
                  }
                ]
              : []),
            {
              icon: LucideUserCheck,
              page: '/dash/admin/consultor-proyecto',
              title: 'Consultor/Proyecto'
            },
            ...(user.role !== 'wm'
              ? [
                  {
                    icon: LucideUserCheck,
                    page: '/dash/admin/consultor-tarea',
                    title: 'Consultor/Tarea'
                  }
                ]
              : []),
            ...(user.role === 'admin'
              ? [
                  {
                    icon: LucideUserPen,
                    page: '/dash/admin/consultores',
                    title: 'Consultores'
                  },
                  {
                    icon: LucideBriefcase,
                    page: '/dash/admin/proyectos',
                    title: 'Proyectos'
                  },
                  {
                    icon: LucideLandmark,
                    page: '/dash/admin/clientes',
                    title: 'Clientes'
                  },
                  {
                    icon: LucideCalendarSync,
                    page: '/dash/admin/calendario-festivos',
                    title: 'Calendario Festivos'
                  },
                  {
                    icon: LucideClock4,
                    page: '/dash/admin/limites-horas',
                    title: 'Limites Horas'
                  },
                  {
                    icon: LucideTicketsPlane,
                    page: '/dash/admin/conf-vacaciones',
                    title: 'Conf. Vacaciones'
                  }
                ]
              : [])
          ]
        },
        {
          icon: LucideFileText,
          page: '/dash/informes',
          title: 'Informes',
          subMenu: [
            {
              icon: LucideClockArrowDown,
              page: '/dash/informes/horas',
              title: 'Horas'
            },
            {
              icon: LucideGalleryHorizontalEnd,
              page: '/dash/informes/varios',
              title: 'Varios'
            },
            {
              icon: LucideTentTree,
              page: '/dash/informes/vacaciones',
              title: 'Vacaciones'
            },
            {
              icon: LucideBookOpenCheck,
              page: '/dash/informes/completitud',
              title: 'Completitud'
            },
            {
              icon: LucideSiren,
              page: '/dash/informes/alertas',
              title: 'Alertas'
            },
            {
              icon: LucideBolt,
              page: '/dash/informes/control-recursos',
              title: 'Control Recursos'
            }
          ]
        }
      ]
    : []),
  ...(user.role === 'wpm' || user.role === 'pm' || user.role === 'admin'
    ? [
        {
          icon: LucideBookOpenCheck,
          page: '/dash/consultas',
          title: 'Consultas'
        }
      ]
    : []),
  ...(user.role === 'wpm' || user.role === 'pm' || user.role === 'admin'
    ? [
        {
          icon: LucideScanEye,
          page: '/dash/accesos',
          title: 'Control de Accesos',
          subMenu: [
            {
              icon: LucideUserLock,
              page: '/dash/accesos/accesos',
              title: 'Accesos'
            },
            {
              icon: LucideBookKey,
              page: '/dash/accesos/proveedores',
              title: 'Proveedores'
            },
            {
              icon: LucideBrickWallFire,
              page: '/dash/accesos/sistemas',
              title: 'Sistemas'
            },
            {
              icon: LucideFolderLock,
              page: '/dash/accesos/sistemas-proyectos',
              title: 'Sistemas Proyectos'
            }
          ]
        }
      ]
    : [])*/
]
