export const useProyectoStore = defineStore('proyecto', () => {
  const proyecto = ref<string | null>(null)
  const proyectoId = ref<number | null>(null)

  const isProyecto = computed(() => proyecto.value !== null)
  const isProyectoId = computed(() => proyectoId.value !== null)

  const setProyecto = (value: string | null) => (proyecto.value = value)

  const setProyectoId = (value: number | null) => (proyectoId.value = value)

  return {
    proyecto,
    setProyecto,
    isProyecto,
    proyectoId,
    setProyectoId,
    isProyectoId
  }
})
