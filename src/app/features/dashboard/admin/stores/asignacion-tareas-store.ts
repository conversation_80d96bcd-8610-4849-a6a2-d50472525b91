export const useAsignacionTareasStore = defineStore('asignacion-tareas', () => {
  const consultorId = ref<number | null>(null)
  const proyectoId = ref<number | null>(null)
  const proyectocorta = ref<string | null>(null)
  const proyectonombre = ref<string | null>(null)

  const isConsultorId = computed(() => consultorId.value !== null)
  const isProyectoId = computed(() => proyectoId.value !== null)
  const isProyectocorta = computed(() => proyectocorta.value !== null)

  const setConsultorId = (id: number | null) => (consultorId.value = id)

  const setProyectoId = (id: number | null) => (proyectoId.value = id)

  const setProyectonombre = (nombre: string | null) =>
    (proyectonombre.value = nombre)

  const setProyectocorta = (corta: string | null) =>
    (proyectocorta.value = corta)

  return {
    consultorId,
    proyectoId,
    proyectocorta,
    proyectonombre,
    isConsultorId,
    isProyectoId,
    isProyectocorta,
    setConsultorId,
    setProyectonombre,
    setProyectoId,
    setProyectocorta
  }
})
