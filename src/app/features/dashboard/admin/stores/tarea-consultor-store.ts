export const useTareaConsultorStore = defineStore('proyecto', () => {
  const proyecto = ref<{
    proyectoCorta: string
    proyectoId: number
  } | null>(null)

  const tareaId = ref<number | null>(null)

  const isProyecto = computed(() => proyecto.value !== null)
  const isTareaId = computed(() => tareaId.value !== null)

  const setProyecto = (
    value: { proyectoCorta: string; proyectoId: number } | null
  ) => (proyecto.value = value)

  const setTareaId = (value: number | null) => (tareaId.value = value)

  return {
    proyecto,
    setProyecto,
    isProyecto,
    tareaId,
    setTareaId,
    isTareaId
  }
})
