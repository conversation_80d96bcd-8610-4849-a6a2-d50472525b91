import type { Consultores } from '@d/admin/models/consultores'
import {
  getPaises,
  consultorJefesFunc
} from '@d/admin/repository/consultores-repository'
import { useQuery } from '@tanstack/vue-query'

export const roleOpts = ['admin', 'wpm', 'pm', 'user', 'cese', 'wm']

export const useCreateEditConsultor = (
  selectedRow: Ref<Consultores | null>
) => {
  const selectedJefeFuncional = reactive<{
    model: string
    id: number | null
  }>({ model: '', id: null })

  const { data: dataPaises, isFetching: isPaisesPending } = useQuery({
    queryFn: getPaises,
    queryKey: ['consultores-paises']
  })

  const { data: dataJefesFunc, isFetching: isJefesFuncPending } = useQuery({
    queryFn: consultorJefesFunc,
    queryKey: ['consultores-jefesfunc']
  })

  const formReactive = reactive({
    nombre: '',
    apellido: '',
    direccion: '',
    usuario: '',
    pais: '',
    passwd: '',
    email: '',
    esjefefunc: false,
    esjefe<PERSON>roy: false,
    role: 'user',
    fechaingreso: ''
  })

  watch(selectedRow, value => {
    if (value != null) {
      formReactive.nombre = value.nombre
      formReactive.apellido = value.apellido
      formReactive.direccion = value.direccion ?? ''
      formReactive.pais = value.pais
      selectedJefeFuncional.id = value.jefefuncional ?? null
      selectedJefeFuncional.model = value.jefefuncionaluser ?? ''
      formReactive.usuario = value.usuario
      formReactive.passwd = ''
      formReactive.email = value.email ?? ''
      formReactive.esjefefunc = value.esjefefunc === 'S'
      formReactive.esjefeproy = value.esjefeproy === 'S'
      formReactive.role = value.role
      formReactive.fechaingreso = value.fechaingreso
    }
  })

  const onSubmit = (): Omit<Consultores, 'id'> => ({
    nombre: formReactive.nombre,
    apellido: formReactive.apellido,
    direccion: formReactive.direccion,
    pais: formReactive.pais,
    jefefuncional: selectedJefeFuncional.id ?? 0,
    usuario: formReactive.usuario,
    passwd: formReactive.passwd,
    email: formReactive.email,
    esjefefunc: formReactive.esjefefunc ? 'S' : 'N',
    esjefeproy: formReactive.esjefeproy ? 'S' : 'N',
    role: formReactive.role as 'admin' | 'wpm' | 'pm' | 'user' | 'cese' | 'wm',
    fechaingreso: formReactive.fechaingreso
  })

  return {
    dataJefesFunc,
    dataPaises,
    formReactive,
    isJefesFuncPending,
    isPaisesPending,
    onSubmit,
    selectedJefeFuncional
  }
}
