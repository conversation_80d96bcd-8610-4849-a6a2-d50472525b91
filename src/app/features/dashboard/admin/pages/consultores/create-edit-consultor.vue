<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-redundant-type-constituents -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import FormCheck from '@c/components/form/FormCheck/Input.vue'
import FormInput from '@c/components/form/FormInput.vue'
import SlideoverDescription from '@c/components/slideover/Description.vue'
import SlideoverPanel from '@c/components/slideover/Panel.vue'
import Slideover from '@c/components/slideover/Slideover.vue'
import type { Consultores } from '@d/admin/models/consultores'
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'

import FormSelect from '@/common/components/form/FormSelect.vue'
import Loading from '@/common/components/loading.vue'

import { roleOpts, useCreateEditConsultor } from './create-edit-consultor'

const props = defineProps<{
  open: boolean
  selectedRow: Consultores | null
}>()

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'submit', value: Omit<Consultores, 'id'>): void
}>()

const { selectedRow } = toRefs(props)

const {
  dataJefesFunc,
  dataPaises,
  formReactive,
  isJefesFuncPending,
  isPaisesPending,
  onSubmit,
  selectedJefeFuncional
} = useCreateEditConsultor(selectedRow)
</script>

<template>
  <Slideover size="lg" :open="props.open" @close="$emit('close')">
    <SlideoverPanel>
      <SlideoverDescription
        class="px-8 pt-10 pb-8 flex flex-col justify-between"
      >
        <div class="flex flex-col">
          <div class="flex flex-row w-full justify-between">
            <div class="text-xl font-medium">
              {{
                props.selectedRow == null
                  ? 'Insertar Consultor:'
                  : 'Editar Consultor:'
              }}
            </div>
          </div>
          <div class="intro-y box w-full">
            <div
              v-if="isPaisesPending || isJefesFuncPending"
              class="flex flex-warp p-5 gap-3 w-full justify-center"
            >
              <Loading icon="puff" class="mx-auto" />
            </div>
            <div v-else class="flex flex-col p-5 gap-5">
              <div class="flex flex-row justify-between gap-3">
                <label class="cursor-pointer select-none mt-3"> Nombre: </label>
                <FormInput
                  v-model="formReactive.nombre"
                  class="block px-4 py-3 w-8/12"
                  placeholder="Nombre"
                  label="Nombre"
                  type="text"
                />
              </div>
              <div class="flex flex-row justify-between gap-4">
                <label class="cursor-pointer select-none mt-3"> Pais: </label>
                <FormInput
                  v-model="formReactive.apellido"
                  class="block px-4 py-3 w-8/12"
                  placeholder="Apellido"
                  label="Apellido"
                  type="text"
                />
              </div>
              <div class="flex flex-row justify-between gap-4">
                <label class="cursor-pointer select-none mt-3"> Email: </label>
                <FormInput
                  v-model="formReactive.direccion"
                  class="block px-4 py-3 w-8/12"
                  placeholder="Dirección"
                  label="Dirección"
                  type="text"
                />
              </div>
              <div class="flex flex-row justify-between gap-3">
                <label class="cursor-pointer select-none mt-1"> País: </label>
                <FormSelect v-model="formReactive.pais" class="w-8/12">
                  <option value="" @click="formReactive.pais = ''">
                    Seleccionar País
                  </option>
                  <option
                    v-for="rowPais in dataPaises"
                    :key="rowPais.idpais"
                    :value="rowPais.codpaiscorep.toString()"
                    @click="
                      () =>
                        (formReactive.pais = rowPais.codpaiscorep.toString())
                    "
                  >
                    {{ rowPais.codpaiscorep }} - &nbsp;
                    {{ rowPais.nombrepaissgt.trimEnd() }}
                  </option>
                </FormSelect>
              </div>
              <div class="flex flex-row justify-between gap-3">
                <label class="cursor-pointer select-none mt-1">
                  Jefe Funcional:
                </label>
                <FormSelect
                  v-model="selectedJefeFuncional.model"
                  class="w-8/12"
                >
                  <option value="" @click="selectedJefeFuncional.model = ''">
                    Seleccionar Jefe Funcional
                  </option>
                  <option
                    v-for="rowJefe in dataJefesFunc"
                    :key="rowJefe.id"
                    :value="rowJefe.id.toString()"
                    @click="
                      () => {
                        selectedJefeFuncional.model = rowJefe.usuario
                        selectedJefeFuncional.id = rowJefe.id
                      }
                    "
                  >
                    {{ rowJefe.nombre }} - {{ rowJefe.apellido }}
                  </option>
                </FormSelect>
              </div>
              <div class="flex flex-row justify-between gap-4">
                <label class="cursor-pointer select-none mt-3">
                  Usuario:
                </label>
                <FormInput
                  v-model="formReactive.usuario"
                  class="block px-4 py-3 w-8/12"
                  placeholder="Usuario"
                  label="Usuario"
                  type="text"
                />
              </div>
              <div class="flex flex-row justify-between gap-4">
                <label class="cursor-pointer select-none mt-3"> Passwd: </label>
                <FormInput
                  v-model="formReactive.passwd"
                  class="block px-4 py-3 w-8/12"
                  placeholder="Passwd"
                  label="Passwd"
                  type="text"
                />
              </div>
              <div class="flex flex-row justify-between gap-3">
                <label class="cursor-pointer select-none mt-1"> País: </label>
                <FormSelect v-model="formReactive.role" class="w-8/12">
                  <option value="" @click="formReactive.role = ''">
                    Seleccionar Role
                  </option>
                  <option
                    v-for="rowRole in roleOpts"
                    :key="rowRole"
                    :value="rowRole"
                    @click="() => (formReactive.role = rowRole)"
                  >
                    {{ rowRole }}
                  </option>
                </FormSelect>
              </div>
              <div class="flex flex-row justify-between gap-3">
                <label class="cursor-pointer select-none mt-2">
                  Fecha Ingreso:
                </label>
                <litepicker v-model="formReactive.fechaingreso" />
              </div>
              <div class="flex flex-row justify-start gap-4">
                <label class="cursor-pointer select-none">
                  Es Jefe Func:
                </label>
                <FormCheck
                  id="remember-me"
                  v-model:boolean="formReactive.esjefefunc"
                  class="mr-2 border"
                  type="checkbox"
                />
              </div>
              <div class="flex flex-row justify-start gap-4">
                <label class="cursor-pointer select-none">
                  Es Jefe Proy:
                </label>
                <FormCheck
                  id="remember-me"
                  v-model:boolean="formReactive.esjefeproy"
                  class="mr-2 border"
                  type="checkbox"
                />
              </div>
              <div class="flex flex-row justify-between gap-4">
                <label class="cursor-pointer select-none mt-3"> Pais: </label>
                <FormInput
                  v-model="formReactive.email"
                  class="block px-4 py-3 w-8/12"
                  placeholder="Email"
                  label="Email"
                  type="text"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="flex flex-row gap-3 justify-end">
          <Button
            variant="primary"
            class="w-24 mr-1"
            @click="
              () => {
                $emit('close')
                emit('submit', onSubmit())
              }
            "
          >
            Guardar
          </Button>
          <Button
            variant="danger"
            class="w-24 mr-1"
            @click="() => $emit('close')"
          >
            Cancelar
          </Button>
        </div>
      </SlideoverDescription>
    </SlideoverPanel>
  </Slideover>
</template>
