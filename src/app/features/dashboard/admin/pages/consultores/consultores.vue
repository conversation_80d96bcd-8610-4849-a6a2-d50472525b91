<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import FormInput from '@c/components/form/FormInput.vue'
import { consultoresAllMetadata } from '@d/admin/models/consultores'
import Table from '@d/common/components/table/Table.vue'
import TBody from '@d/common/components/table/Tbody.vue'
import Td from '@d/common/components/table/Td.vue'
import Th from '@d/common/components/table/Th.vue'
import Thead from '@d/common/components/table/Thead.vue'
import Tr from '@d/common/components/table/Tr.vue'

import FormSelect from '@/common/components/form/FormSelect.vue'
import Loading from '@/common/components/loading.vue'

import { useConsultores } from './consultores'
import CreateEditConsultor from './create-edit-consultor.vue'

const {
  columnQuery,
  dataConsultores,
  filteredData,
  isConsultoresPending,
  mutateConsultorInit,
  mutateDeleteConsultor,
  onEdit,
  onSubmit,
  openDialog,
  refetchConsultores,
  searchQuery,
  selectedIndex,
  selectedRow,
  selectedRowForData
} = useConsultores()
</script>

<template>
  <div>
    <CreateEditConsultor
      :open="openDialog"
      :selected-row="selectedRow"
      @submit="onSubmit"
      @close="() => (openDialog = false)"
    />
    <div class="flex items-center mt-8 mb-3 intro-y">
      <h2 class="mr-auto text-2xl font-medium">Consultores</h2>
    </div>
    <div
      v-if="isConsultoresPending"
      class="box flex flex-col items-center py-4"
    >
      <Loading icon="three-dots" />
    </div>
    <div v-else class="box">
      <div
        class="flex flex-col items-center p-5 border-b sm:flex-row border-slate-200/60 justify-between"
      >
        <div class="flex flex-col sm:flex-row gap-3">
          <FormSelect class="w-22">
            <option value="" @click="columnQuery = ''">
              Seleccionar Columna
            </option>
            <option
              v-for="row in consultoresAllMetadata"
              :key="row.key"
              :value="row.key"
              @click="columnQuery = row.key"
            >
              {{ row.label }}
            </option>
          </FormSelect>
          <div class="relative text-slate-500 w-11/12">
            <FormInput
              v-model="searchQuery"
              placeholder="Buscar..."
              class="pr-10"
              :disabled="columnQuery === ''"
              type="text"
            />
            <LucideSearch
              class="absolute inset-y-0 right-0 w-4 h-4 my-auto mr-3"
            />
          </div>
        </div>
        <div class="flex flex-col sm:flex-row gap-3">
          <Button
            class="w-56"
            variant="primary"
            :disabled="selectedIndex == null"
            @click="() => mutateConsultorInit(selectedRowForData?.id ?? 0)"
          >
            Asignar Proyecto y tareas
          </Button>
          <Button
            class="w-28"
            variant="primary"
            @click="
              () => {
                selectedRow = null
                openDialog = true
              }
            "
          >
            Nuevo
          </Button>
          <Button variant="dark" @click="refetchConsultores">
            <LucideRefreshCcw class="h-4 w-4" />
          </Button>
        </div>
      </div>
      <div v-if="dataConsultores && filteredData.length > 0" class="p-5">
        <div class="overflow-auto max-h-[69dvh]">
          <Table hover sm>
            <Thead variant="dark">
              <Tr>
                <Th
                  v-for="row in consultoresAllMetadata"
                  :key="row.key"
                  class="whitespace-nowrap font-bold"
                >
                  {{ row.label }}
                </Th>
                <Th class="whitespace-nowrap font-bold">Acciones</Th>
              </Tr>
            </Thead>
            <TBody>
              <Tr
                v-for="el in filteredData"
                :key="el.id"
                :class="`dark:hover:bg-[#303761] hover:bg-[#dadef7] ${
                  selectedIndex === el.id
                    ? 'dark:bg-[#303761] bg-[#dadef7]'
                    : ''
                }`"
                @click="
                  () => {
                    selectedIndex = el.id
                    selectedRowForData = el
                  }
                "
              >
                <Td>{{ el.id }}</Td>
                <Td>{{ el.nombre }}</Td>
                <Td>{{ el.apellido }}</Td>
                <Td>{{ el.email }}</Td>
                <Td>{{ el.role }}</Td>
                <Td>{{ el.fechaingreso }}</Td>
                <Td>
                  <div class="flex flex-row gap-3">
                    <Button variant="dark" @click="() => onEdit(el)">
                      <LucidePencil class="h-4 w-4" />
                    </Button>
                    <Button
                      variant="dark"
                      @click="() => mutateDeleteConsultor(el.id)"
                    >
                      <LucideEraser class="h-4 w-4" />
                    </Button>
                  </div>
                </Td>
              </Tr>
            </TBody>
          </Table>
        </div>
      </div>
      <div
        v-else-if="dataConsultores && filteredData.length === 0"
        class="p-10"
      >
        <h3 class="text-center font-bold text-xl">
          No hay datos disponibles para mostrar
        </h3>
      </div>
    </div>
  </div>
</template>
