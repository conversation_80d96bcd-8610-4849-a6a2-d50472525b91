import {
  getConsultores,
  createConsultor,
  updateConsultor,
  deleteConsultor,
  consultorInit
} from '@d/admin/repository/consultores-repository'
import { useQuery, useMutation } from '@tanstack/vue-query'
import { useAlertStore } from '@c/stores/alert-store'
import type { Consultores } from '@d/admin/models/consultores'

export const useConsultores = () => {
  const columnQuery = ref('')
  const searchQuery = ref('')
  const openDialog = ref(false)
  const alertStore = useAlertStore()
  const selectedRow = ref<Consultores | null>(null)
  const selectedRowForData = ref<Consultores | null>(null)
  const selectedIndex = ref<number | null>(null)

  const debouncedSearchQuery = useDebounce(searchQuery, 150)

  const {
    data: dataConsultores,
    isFetching: isConsultoresPending,
    refetch: refetchConsultores
  } = useQuery({
    queryFn: getConsultores,
    queryKey: ['accesos-consultores']
  })

  const { mutate: mutateCreateConsultor, isPending: isCreatingConsultor } =
    useMutation({
      mutationFn: (sis: Omit<Consultores, 'id'>) => createConsultor(sis),
      onError: () => alertStore.showErrorAlert(),
      onSuccess: () => {
        alertStore.showSuccessAlert()
      }
    })

  const { mutate: mutateConsultorInit, isPending: isConsultorInitPending } =
    useMutation({
      mutationFn: (id: number) => consultorInit(id),
      onError: () => alertStore.showErrorAlert(),
      onSuccess: () => alertStore.showSuccessAlert()
    })

  const { mutate: mutateUpdateConsultor, isPending: isUpdatingConsultor } =
    useMutation({
      mutationFn: (input: Omit<Consultores, 'id'>) =>
        updateConsultor(selectedRow.value?.id ?? 0, input),
      onError: () => alertStore.showErrorAlert(),
      onSuccess: () => {
        alertStore.showSuccessAlert()
      }
    })

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim()) return dataConsultores.value ?? []

    return (dataConsultores.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof Consultores]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  const { mutate: mutateDeleteConsultor, isPending: isDeletingConsultor } =
    useMutation({
      mutationFn: deleteConsultor,
      onError: () => alertStore.showErrorAlert(),
      onSuccess: () => {
        alertStore.showSuccessAlert()
        refetchConsultores()
      }
    })

  const onEdit = (row: Consultores) => {
    selectedRow.value = row
    setTimeout(() => (openDialog.value = true), 250)
  }

  const onSubmit = (res: Omit<Consultores, 'id'>) => {
    if (selectedRow.value) {
      mutateUpdateConsultor(res)
    } else {
      mutateCreateConsultor(res)
    }
  }

  const loadingOperations = computed(
    () =>
      isConsultoresPending ||
      isCreatingConsultor ||
      isUpdatingConsultor ||
      isDeletingConsultor
  )

  return {
    columnQuery,
    dataConsultores,
    filteredData,
    isConsultoresPending,
    isDeletingConsultor,
    loadingOperations,
    mutateConsultorInit,
    mutateDeleteConsultor,
    onEdit,
    onSubmit,
    openDialog,
    refetchConsultores,
    searchQuery,
    selectedIndex,
    selectedRow,
    selectedRowForData
  }
}
