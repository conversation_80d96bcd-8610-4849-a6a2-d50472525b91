import {
  getLimitesHoras,
  updateLimitesHoras
} from '@d/admin/repository/limites-horas-repository'
import { useQuery, useMutation } from '@tanstack/vue-query'
import { useAlertStore } from '@c/stores/alert-store'
import type { Paises } from '@d/admin/models/paises'

export const useLimitesHoras = () => {
  const columnQuery = ref('')
  const searchQuery = ref('')
  const openDialog = ref(false)
  const alertStore = useAlertStore()
  const selectedRow = ref<Paises | null>(null)

  const debouncedSearchQuery = useDebounce(searchQuery, 150)

  const {
    data: dataLimitesHoras,
    isFetching: isLimitesHorasPending,
    refetch: refetchLimitesHoras
  } = useQuery({
    queryFn: getLimitesHoras,
    queryKey: ['admin-limites-horas']
  })

  const {
    mutate: mutateUpdateLimitesHoras,
    isPending: isUpdatingLimitesHoras
  } = useMutation({
    mutationFn: (input: Omit<Paises, 'idpais'>) =>
      updateLimitesHoras(selectedRow.value?.idpais ?? 0, input),
    onError: () => alertStore.showErrorAlert(),
    onSuccess: () => alertStore.showSuccessAlert()
  })

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim()) return dataLimitesHoras.value ?? []

    return (dataLimitesHoras.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof Paises]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  const onEdit = (row: Paises) => {
    selectedRow.value = row
    setTimeout(() => (openDialog.value = true), 250)
  }

  const onSubmit = (res: { horasmax: number; nombrepaissgt: string }) => {
    if (selectedRow.value) {
      mutateUpdateLimitesHoras({
        horasmax: res.horasmax,
        codpaissgt: selectedRow.value.codpaissgt,
        nombrepaissgt: res.nombrepaissgt,
        codpaiscorep: selectedRow.value.codpaiscorep
      })
    }
  }

  return {
    columnQuery,
    dataLimitesHoras,
    filteredData,
    isLimitesHorasPending,
    isUpdatingLimitesHoras,
    onEdit,
    onSubmit,
    openDialog,
    refetchLimitesHoras,
    searchQuery,
    selectedRow
  }
}
