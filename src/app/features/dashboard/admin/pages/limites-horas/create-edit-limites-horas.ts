import type { Paises } from '@d/admin/models/paises'

export const useCreateEditLimitesHoras = (selectedRow: Ref<Paises | null>) => {
  const formReactive = reactive({ nombrepaissgt: '', horasmax: 0 })

  watch(selectedRow, value => {
    if (value != null) {
      formReactive.horasmax = value.horasmax ?? 0
      formReactive.nombrepaissgt = value.nombrepaissgt.trimEnd() ?? ''
    }
  })

  const onSubmit = () => ({
    horasmax: formReactive.horasmax,
    nombrepaissgt: formReactive.nombrepaissgt
  })

  return { formReactive, onSubmit }
}
