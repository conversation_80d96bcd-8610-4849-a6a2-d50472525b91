<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-redundant-type-constituents -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import FormInput from '@c/components/form/FormInput.vue'
import SlideoverDescription from '@c/components/slideover/Description.vue'
import SlideoverPanel from '@c/components/slideover/Panel.vue'
import Slideover from '@c/components/slideover/Slideover.vue'
import type { Paises } from '@d/admin/models/paises'

import { useCreateEditLimitesHoras } from './create-edit-limites-horas'

const props = defineProps<{
  open: boolean
  selectedRow: Paises | null
}>()

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'submit', value: { horasmax: number; nombrepaissgt: string }): void
}>()

const { selectedRow } = toRefs(props)

const { formReactive, onSubmit } = useCreateEditLimitesHoras(selectedRow)
</script>

<template>
  <Slideover size="lg" :open="props.open" @close="$emit('close')">
    <SlideoverPanel>
      <SlideoverDescription
        class="px-8 pt-10 pb-8 flex flex-col justify-between"
      >
        <div class="flex flex-col">
          <div class="flex flex-row w-full justify-between">
            <div class="text-xl font-medium">Editar Límites Horas</div>
          </div>
          <div class="intro-y box w-full">
            <div class="flex flex-col p-5 gap-5">
              <div class="flex flex-row justify-between gap-3">
                <label class="cursor-pointer select-none mt-3"> País: </label>
                <FormInput
                  v-model="formReactive.nombrepaissgt"
                  class="block px-4 py-3 w-8/12"
                  placeholder="Nombre"
                  label="Nombre"
                  type="text"
                  readonly
                />
              </div>
            </div>
            <div class="flex flex-col p-5 gap-5">
              <div class="flex flex-row justify-between gap-3">
                <label class="cursor-pointer select-none mt-3">
                  Horas Max:
                </label>
                <FormInput
                  v-model="formReactive.horasmax"
                  class="block px-4 py-3 w-8/12"
                  placeholder="Nombre"
                  label="Nombre"
                  type="number"
                />
              </div>
            </div>
          </div>
        </div>

        <div class="flex flex-row gap-3 justify-end">
          <Button
            variant="primary"
            class="w-24 mr-1"
            @click="
              () => {
                $emit('close')
                emit('submit', onSubmit())
              }
            "
          >
            Guardar
          </Button>
          <Button
            variant="danger"
            class="w-24 mr-1"
            @click="() => $emit('close')"
          >
            Cancelar
          </Button>
        </div>
      </SlideoverDescription>
    </SlideoverPanel>
  </Slideover>
</template>
