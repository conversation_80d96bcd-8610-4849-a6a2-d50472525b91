import {
  getDataHorasAprobadas,
  putPendienteHoras
} from '@d/admin/repository/aprob-pm-repository'
import type { Horas } from '@d/admin/models/horas'
import { useAlertStore } from '@c/stores/alert-store'
import { useMutation, useQuery } from '@tanstack/vue-query'

export const useHorasAprobadas = () => {
  const selectedRows = ref<Horas[]>([])
  const selectedRowsLength = computed(() => selectedRows.value.length)
  const columnQuery = ref('')
  const searchQuery = ref('')
  const debouncedSearchQuery = useDebounce(searchQuery, 150)
  const alertStore = useAlertStore()

  const {
    isFetching: isHorasAprobadasPending,
    data: dataHorasAprobadas,
    refetch: refetchHorasAprobadas
  } = useQuery({
    queryKey: ['horas-aprobadaspm'],
    queryFn: () => getDataHorasAprobadas()
  })

  const { mutate: mutatePendienteHoras, isPending: isPendienteHorasPending } =
    useMutation({
      mutationFn: (id: number) => putPendienteHoras(id),
      onSuccess: () => {
        alertStore.showSuccessAlert()
        refetchHorasAprobadas()
      },
      onError: () => alertStore.showErrorAlert()
    })

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim())
      return dataHorasAprobadas.value ?? []

    return (dataHorasAprobadas.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof Horas]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  return {
    columnQuery,
    dataHorasAprobadas,
    filteredData,
    isPendienteHorasPending,
    mutatePendienteHoras,
    isHorasAprobadasPending,
    searchQuery,
    selectedRowsLength,
    refetchHorasAprobadas
  }
}
