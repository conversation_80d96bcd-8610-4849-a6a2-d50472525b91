import {
  getDataHorasAprobar,
  putRechazarHoras,
  putAprobarHoras
} from '@d/admin/repository/aprob-pm-repository'
import { useAlertStore } from '@c/stores/alert-store'
import type { Horas } from '@d/admin/models/horas'
import { useMutation, useQuery } from '@tanstack/vue-query'

export const useHorasAprobar = () => {
  const selectedRows = ref<Horas[]>([])
  const selectedRowsLength = computed(() => selectedRows.value.length)
  const columnQuery = ref('')
  const searchQuery = ref('')
  const showDeleteDialog = ref(false)
  const rejectInput = ref('')
  const debouncedSearchQuery = useDebounce(searchQuery, 150)

  const alertStore = useAlertStore()

  const {
    isFetching: isHorasAprobarPending,
    data: dataHorasAprobar,
    refetch: refetchHorasAprobar
  } = useQuery({
    queryKey: ['horas-aprobarpm'],
    queryFn: () => getDataHorasAprobar()
  })

  const { mutate: mutateAprobarHoras, isPending: isAprobarHorasPending } =
    useMutation({
      mutationFn: (id: number) => putAprobarHoras(id),
      onSuccess: () => {
        alertStore.showSuccessAlert()
        refetchHorasAprobar()
      },
      onError: () => alertStore.showErrorAlert()
    })

  const { mutate: mutateRechazarHoras, isPending: isRechazarHorasPending } =
    useMutation({
      mutationFn: (id: number) => putRechazarHoras(id, rejectInput.value),
      onSuccess: () => {
        alertStore.showSuccessAlert()
        refetchHorasAprobar()
      },
      onError: () => alertStore.showErrorAlert()
    })

  const handleCheckboxChange = (event: Event, tarea: Horas) => {
    const isChecked = (event.target as HTMLInputElement).checked
    if (isChecked) {
      selectedRows.value.push(tarea)
    } else {
      selectedRows.value = selectedRows.value.filter(
        item => item.id !== tarea.id
      )
    }
  }

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim()) return dataHorasAprobar.value ?? []

    return (dataHorasAprobar.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof Horas]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  return {
    columnQuery,
    dataHorasAprobar,
    filteredData,
    handleCheckboxChange,
    isAprobarHorasPending,
    isRechazarHorasPending,
    isHorasAprobarPending,
    mutateAprobarHoras,
    mutateRechazarHoras,
    refetchHorasAprobar,
    rejectInput,
    showDeleteDialog,
    searchQuery,
    selectedRowsLength
  }
}
