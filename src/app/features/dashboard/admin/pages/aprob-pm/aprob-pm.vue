<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script>
import DisclosureButton from '@d/common/components/disclosure/Button.vue'
import Disclosure from '@d/common/components/disclosure/Disclosure/Disclosure.vue'
import DisclosureGroup from '@d/common/components/disclosure/Group.vue'
import DisclosurePanel from '@d/common/components/disclosure/Panel.vue'

import HorasAprobadas from './components/horas-aprobadas.vue'
import HorasAprobar from './components/horas-aprobar.vue'
import HorasRechazadas from './components/horas-rechazadas.vue'

export default {
  components: {
    Disclosure,
    DisclosureButton,
    DisclosureGroup,
    DisclosurePanel,
    HorasAprobadas,
    HorasAprobar,
    HorasRechazadas
  }
}
</script>

<template>
  <div>
    <div class="flex items-center mt-8 intro-y">
      <h2 class="mr-auto text-2xl font-medium">Aprobaciones Horas PM</h2>
    </div>
    <div class="grid grid-cols-12 gap-6 mt-5 intro-y">
      <div class="col-span-full">
        <DisclosureGroup class="mt-3">
          <Disclosure class="box my-3" index="0">
            <DisclosureButton
              class="px-4 flex flex-row justify-between"
              :openDisc="true"
            >
              <h4 class="mt-0.5 text-xl">Horas Por Aprobar</h4>
              <LucidePlus />
            </DisclosureButton>
            <DisclosurePanel>
              <HorasAprobar />
            </DisclosurePanel>
          </Disclosure>
          <Disclosure class="box my-3" index="1">
            <DisclosureButton class="px-4 flex flex-row justify-between">
              <h4 class="mt-0.5 text-xl">Horas Aprobadas</h4>
              <LucidePlus />
            </DisclosureButton>
            <DisclosurePanel>
              <HorasAprobadas />
            </DisclosurePanel>
          </Disclosure>
          <Disclosure class="box my-3" index="1">
            <DisclosureButton class="px-4 flex flex-row justify-between">
              <h4 class="mt-0.5 text-xl">Horas Rechazadas</h4>
              <LucidePlus />
            </DisclosureButton>
            <DisclosurePanel>
              <HorasRechazadas />
            </DisclosurePanel>
          </Disclosure>
        </DisclosureGroup>
      </div>
    </div>
  </div>
</template>
