<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script>
import DisclosureButton from '@d/common/components/disclosure/Button.vue'
import Disclosure from '@d/common/components/disclosure/Disclosure/Disclosure.vue'
import DisclosureGroup from '@d/common/components/disclosure/Group.vue'
import DisclosurePanel from '@d/common/components/disclosure/Panel.vue'

import Informes from './components/informes.vue'
import LegislacionesPais from './components/legislaciones-pais.vue'
import VacacionesConsultor from './components/vacaciones-consultor.vue'

export default {
  components: {
    Disclosure,
    DisclosureButton,
    DisclosureGroup,
    DisclosurePanel,
    Informes,
    LegislacionesPais,
    VacacionesConsultor
  }
}
</script>

<template>
  <div>
    <div class="flex items-center mt-8 intro-y">
      <h2 class="mr-auto text-2xl font-medium">Conf. Vacaciones</h2>
    </div>
    <div class="grid grid-cols-12 gap-6 mt-5 intro-y">
      <div class="col-span-full">
        <DisclosureGroup class="mt-3">
          <Disclosure class="box my-3" index="0">
            <DisclosureButton
              class="px-4 flex flex-row justify-between"
              :openDisc="true"
            >
              <h4 class="mt-0.5 text-xl">Configuración Vacaciones Consultor</h4>
              <LucidePlus />
            </DisclosureButton>
            <DisclosurePanel class="grid grid-cols-2 justify-center gap-3">
              <VacacionesConsultor />
            </DisclosurePanel>
          </Disclosure>
          <Disclosure class="box my-3" index="1">
            <DisclosureButton class="px-4 flex flex-row justify-between">
              <h4 class="mt-0.5 text-xl">Legislaciones por País</h4>
              <LucidePlus />
            </DisclosureButton>
            <DisclosurePanel>
              <LegislacionesPais />
            </DisclosurePanel>
          </Disclosure>
          <Disclosure class="box my-3" index="1">
            <DisclosureButton class="px-4 flex flex-row justify-between">
              <h4 class="mt-0.5 text-xl">Informes</h4>
              <LucidePlus />
            </DisclosureButton>
            <DisclosurePanel>
              <Informes />
            </DisclosurePanel>
          </Disclosure>
        </DisclosureGroup>
      </div>
    </div>
  </div>
</template>
