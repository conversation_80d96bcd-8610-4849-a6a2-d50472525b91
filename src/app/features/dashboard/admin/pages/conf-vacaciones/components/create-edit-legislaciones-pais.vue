<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-redundant-type-constituents -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import FormInput from '@c/components/form/FormInput.vue'
import SlideoverDescription from '@c/components/slideover/Description.vue'
import SlideoverPanel from '@c/components/slideover/Panel.vue'
import Slideover from '@c/components/slideover/Slideover.vue'
import type { Legislab } from '@d/admin/models/legislab'

import FormSelect from '@/common/components/form/FormSelect.vue'
import Loading from '@/common/components/loading.vue'

import { useCreateEditLegislacionesPais } from './create-edit-legislaciones-pais'

const props = defineProps<{
  open: boolean
  selectedRow: Legislab | null
}>()

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'submit', value: Legislab): void
}>()

const { selectedRow } = toRefs(props)

const { dataPaises, formReactive, isPaisesPending, onSubmit } =
  useCreateEditLegislacionesPais(selectedRow)
</script>

<template>
  <Slideover size="lg" :open="props.open" @close="$emit('close')">
    <SlideoverPanel>
      <SlideoverDescription
        class="px-8 pt-10 pb-8 flex flex-col justify-between"
      >
        <div class="flex flex-col">
          <div class="flex flex-row w-full justify-between">
            <div class="text-xl font-medium">
              {{
                props.selectedRow == null
                  ? 'Insertar Legislación País:'
                  : 'Editar Legislación País:'
              }}
            </div>
          </div>
          <div class="intro-y box w-full">
            <div
              v-if="isPaisesPending"
              class="flex flex-warp p-5 gap-3 w-full justify-center"
            >
              <Loading icon="puff" class="mx-auto" />
            </div>
            <div v-else class="flex flex-col p-5 gap-5">
              <div class="flex flex-row justify-between gap-3">
                <label class="cursor-pointer select-none mt-1">
                  Legislación:
                </label>
                <FormSelect v-model="formReactive.legislacion" class="w-8/12">
                  <option value="" @click="formReactive.legislacion = ''">
                    Seleccionar Legislación
                  </option>
                  <option
                    v-for="rowPais in dataPaises"
                    :key="rowPais.idpais"
                    :value="rowPais.codpaiscorep.toString()"
                    @click="
                      () =>
                        (formReactive.legislacion =
                          rowPais.codpaiscorep.toString())
                    "
                  >
                    {{ rowPais.nombrepaissgt.trimEnd() }}
                  </option>
                </FormSelect>
              </div>
              <div class="flex flex-row justify-between gap-3">
                <label class="cursor-pointer select-none mt-1">
                  Nat Hab:
                </label>
                <FormSelect v-model="formReactive.nathab" class="w-8/12">
                  <option value="" @click="formReactive.nathab = ''">
                    Seleccionar Nat Hab
                  </option>
                  <option
                    v-for="natHab in ['H', 'N']"
                    :key="natHab[0]"
                    :value="natHab"
                    @click="() => (formReactive.nathab = natHab)"
                  >
                    {{ natHab }}
                  </option>
                </FormSelect>
              </div>
              <div class="flex flex-row justify-between gap-3">
                <label class="cursor-pointer select-none mt-3">
                  Días por Año:
                </label>
                <FormInput
                  v-model="formReactive.diasper"
                  class="block px-4 py-3 w-8/12"
                  placeholder="Días por Año"
                  label="Días por Año"
                  type="number"
                />
              </div>
            </div>
          </div>
        </div>

        <div class="flex flex-row gap-3 justify-end">
          <Button
            variant="primary"
            class="w-24 mr-1"
            @click="
              () => {
                $emit('close')
                emit('submit', onSubmit())
              }
            "
          >
            Guardar
          </Button>
          <Button
            variant="danger"
            class="w-24 mr-1"
            @click="() => $emit('close')"
          >
            Cancelar
          </Button>
        </div>
      </SlideoverDescription>
    </SlideoverPanel>
  </Slideover>
</template>
