import {
  getLegislab,
  createLegislab,
  updateLegislab,
  deleteLegislab
} from '@d/admin/repository/conf-vacaciones-repository'
import { useQuery, useMutation } from '@tanstack/vue-query'
import { useAlertStore } from '@c/stores/alert-store'
import type { Legislab } from '@d/admin/models/legislab'

export const uselegislacionesPais = () => {
  const openDialog = ref(false)
  const alertStore = useAlertStore()
  const selectedRow = ref<Legislab | null>(null)

  const {
    data: datalegislacionesPais,
    isFetching: islegislacionesPaisPending,
    refetch: refetchlegislacionesPais
  } = useQuery({
    queryFn: getLegislab,
    queryKey: ['admin-confvac-legis']
  })

  const {
    mutate: mutateCreatelegislacionesPais,
    isPending: isCreatinglegislacionesPais
  } = useMutation({
    mutationFn: (sis: Legislab) => createLegislab(sis),
    onError: () => alertStore.showErrorAlert(),
    onSuccess: () => {
      alertStore.showSuccessAlert()
    }
  })

  const {
    mutate: mutateUpdatelegislacionesPais,
    isPending: isUpdatinglegislacionesPais
  } = useMutation({
    mutationFn: (input: Legislab) =>
      updateLegislab(selectedRow.value?.legislacion ?? '', input),
    onError: () => alertStore.showErrorAlert(),
    onSuccess: () => alertStore.showSuccessAlert()
  })

  const {
    mutate: mutateDeletelegislacionesPais,
    isPending: isDeletinglegislacionesPais
  } = useMutation({
    mutationFn: deleteLegislab,
    onError: () => alertStore.showErrorAlert(),
    onSuccess: () => {
      alertStore.showSuccessAlert()
      refetchlegislacionesPais()
    }
  })

  const onEdit = (row: Legislab) => {
    selectedRow.value = row
    setTimeout(() => (openDialog.value = true), 250)
  }

  const onSubmit = (legislab: Legislab) => {
    if (selectedRow.value) {
      mutateUpdatelegislacionesPais(legislab)
    } else {
      mutateCreatelegislacionesPais(legislab)
    }
  }

  return {
    datalegislacionesPais,
    islegislacionesPaisPending,
    isUpdatinglegislacionesPais,
    mutateDeletelegislacionesPais,
    onEdit,
    onSubmit,
    openDialog,
    refetchlegislacionesPais,
    selectedRow
  }
}
