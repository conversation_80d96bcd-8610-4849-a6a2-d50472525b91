<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'

import Button from '@/common/components/button/button.vue'

const dateModel = ref('')
</script>

<template>
  <div>
    <div class="box">
      <div class="flex flex-col items-center p-5 b sm:flex-row justify-evenly">
        <div class="flex flex-col sm:flex-row gap-3">
          <div class="flex flex-col gap-2">
            <div class="flex flex-row justify-between gap-3">
              <label class="cursor-pointer select-none mt-2 mr-3">
                Fecha Ingreso:
              </label>
              <litepicker v-model="dateModel" />
            </div>
          </div>
        </div>
        <div class="flex flex-col sm:flex-row gap-3">
          <Button variant="success" class="w-72">
            <LucideSheet class="w-4 h-4 mr-2" /> Informe Situación Vacaciones
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
