import { getDataConsultores } from '@d/admin/repository/consultor-proyecto-repository'
import type { Consultores } from '@d/admin/models/consultores'
import { useAlertStore } from '@c/stores/alert-store'
import type { ConsultaVacaciones } from '@d/home/<USER>/consulta-vacaciones'
import { getConsultaVacaciones } from '@d/home/<USER>/consulta-vacaciones-repository'

import { useMutation, useQuery } from '@tanstack/vue-query'
import { getPaises } from '@d/admin/repository/consultores-repository'

import {
  getConfVacaciones,
  updateConfVacaciones
} from '@d/admin/repository/conf-vacaciones-repository'

export const useVacacionesConsultor = () => {
  const columnQuery = ref('')
  const searchQuery = ref('')
  const debouncedSearchQuery = useDebounce(searchQuery, 150)
  const selectedConsultor = ref<Consultores | null>(null)

  const alertStore = useAlertStore()

  const formReactive = reactive({
    legislacion: '',
    idconsultor: '',
    vacdisf: 0,
    diasperm: 0,
    vacnogast: 0
  })

  const {
    isFetching: isConsultasVacacionesPending,
    data: dataConsultasVacaciones,
    refetch: refetchConsultasVacaciones
  } = useQuery({
    enabled: computed(() => selectedConsultor.value !== null),
    queryKey: ['admin-confvac-consultas-vacaciones'],
    queryFn: () => getConsultaVacaciones(selectedConsultor.value?.id ?? 4)
  })

  const dataSingle = computed(
    () => dataConsultasVacaciones?.value?.[0] ?? ({} as ConsultaVacaciones)
  )

  const fechaCalculo = ref(dataSingle.value?.fechainforme ?? 0)
  const fechaIncorporacion = ref(dataSingle.value?.fechaingreso ?? 0)
  const diasVacDerCons = ref(dataSingle.value?.derechoconsolidado ?? 0)
  const diasVacAno = ref(dataSingle.value?.diasper ?? 0)
  const diasPer = ref(dataSingle.value?.diaspermisoadic ?? 0)
  const diasVacNoDisf = ref(dataSingle.value?.vacnogastadas ?? 0)
  const diasVacDisfMesAnt = ref(dataSingle.value?.diasvacdisfmesant ?? 0)
  const diasVacDisfMesAct = ref(dataSingle.value?.diasvacdisfmesact ?? 0)
  const diasDisponibles = ref(dataSingle.value?.diasdisponibles ?? 0)

  const { data: dataPaises, isFetching: isPaisesPending } = useQuery({
    queryFn: getPaises,
    queryKey: ['admin-confvac-legis-consultor']
  })

  const { mutate: mutateUpdateConfVac, isPending: isUpdatingConfVac } =
    useMutation({
      mutationFn: updateConfVacaciones,
      onError: () => alertStore.showErrorAlert(),
      onSuccess: () => alertStore.showSuccessAlert()
    })

  const {
    isFetching: isConfVacPending,
    data: dataConfVac,
    refetch: refetchConfVac
  } = useQuery({
    enabled: computed(() => selectedConsultor.value !== null),
    queryKey: ['admin-confvac-confvac'],
    queryFn: () =>
      getConfVacaciones(selectedConsultor.value?.id ?? 4).then(res => {
        formReactive.legislacion = selectedConsultor.value?.pais ?? ''
        formReactive.idconsultor = selectedConsultor.value?.id.toString() ?? ''
        formReactive.vacdisf = res?.[0]?.vacinic ?? 0
        formReactive.diasperm = res?.[0]?.diaspermisoadic ?? 0
        formReactive.vacnogast = res?.[0]?.vacnogastadas ?? 0
        return res
      })
  })

  const {
    isFetching: isConsultoresPending,
    data: dataConsultores,
    refetch: refetchConsultores
  } = useQuery({
    queryKey: ['vacaciones-consultores-config'],
    queryFn: () => getDataConsultores()
  })

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim()) return dataConsultores.value ?? []

    return (dataConsultores.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof Consultores]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  const resetConsultores = () => {
    columnQuery.value = ''
    searchQuery.value = ''
    selectedConsultor.value = null
  }

  const onUpdate = () => {
    if (!selectedConsultor.value) return

    mutateUpdateConfVac({
      consultorid: parseInt(formReactive.idconsultor),
      vacinic: formReactive.vacdisf,
      legislacion: formReactive.legislacion,
      diaspermisoadic: formReactive.diasperm,
      vacnogastadas: formReactive.vacnogast
    })
  }

  return {
    columnQuery,
    dataConfVac,
    dataConsultasVacaciones,
    diasDisponibles,
    diasPer,
    diasVacAno,
    diasVacDerCons,
    diasVacDisfMesAct,
    diasVacDisfMesAnt,
    diasVacNoDisf,
    fechaCalculo,
    fechaIncorporacion,
    dataPaises,
    filteredData,
    formReactive,
    isConfVacPending,
    isConsultasVacacionesPending,
    isConsultoresPending,
    isPaisesPending,
    onUpdate,
    refetchConfVac,
    refetchConsultasVacaciones,
    refetchConsultores,
    resetConsultores,
    searchQuery,
    selectedConsultor
  }
}
