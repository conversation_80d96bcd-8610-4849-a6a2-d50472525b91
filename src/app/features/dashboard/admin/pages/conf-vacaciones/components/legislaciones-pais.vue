<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import { legislabMetadata } from '@d/admin/models/legislab'
import Table from '@d/common/components/table/Table.vue'
import TBody from '@d/common/components/table/Tbody.vue'
import Td from '@d/common/components/table/Td.vue'
import Th from '@d/common/components/table/Th.vue'
import Thead from '@d/common/components/table/Thead.vue'
import Tr from '@d/common/components/table/Tr.vue'

import Loading from '@/common/components/loading.vue'

import CreateEditLegislacionesPais from './create-edit-legislaciones-pais.vue'
import { uselegislacionesPais } from './legislaciones-pais'

const {
  datalegislacionesPais,
  islegislacionesPaisPending,
  mutateDeletelegislacionesPais,
  onEdit,
  onSubmit,
  openDialog,
  refetchlegislacionesPais,
  selectedRow
} = uselegislacionesPais()
</script>

<template>
  <div>
    <CreateEditLegislacionesPais
      :open="openDialog"
      :selected-row="selectedRow"
      @submit="onSubmit"
      @close="() => (openDialog = false)"
    />
    <div
      v-if="islegislacionesPaisPending"
      class="box flex flex-col items-center py-4"
    >
      <Loading icon="three-dots" />
    </div>
    <div v-else class="box">
      <div
        class="flex flex-col items-center p-5 border-b sm:flex-row border-slate-200/60 justify-end"
      >
        <div class="flex flex-col sm:flex-row gap-3">
          <Button
            class="w-28"
            variant="primary"
            @click="
              () => {
                selectedRow = null
                openDialog = true
              }
            "
          >
            Nuevo
          </Button>
          <Button variant="dark" @click="refetchlegislacionesPais">
            <LucideRefreshCcw class="h-4 w-4" />
          </Button>
        </div>
      </div>
      <div
        v-if="datalegislacionesPais && datalegislacionesPais.length > 0"
        class="p-5"
      >
        <div class="overflow-auto max-h-[69dvh]">
          <Table hover sm>
            <Thead variant="dark">
              <Tr>
                <Th
                  v-for="row in legislabMetadata"
                  :key="row.key"
                  class="whitespace-nowrap font-bold"
                >
                  {{ row.label }}
                </Th>
                <Th class="whitespace-nowrap font-bold">Acciones</Th>
              </Tr>
            </Thead>
            <TBody>
              <Tr
                v-for="el in datalegislacionesPais"
                :key="el.legislacion"
                class="dark:hover:bg-[#303761] hover:bg-[#dadef7]"
              >
                <Td>{{ el.legislacion }}</Td>
                <Td>{{ el.nathab }}</Td>
                <Td>{{ el.diasper }}</Td>
                <Td>
                  <div class="flex flex-row gap-3">
                    <Button variant="dark" @click="() => onEdit(el)">
                      <LucidePencil class="h-4 w-4" />
                    </Button>
                    <Button
                      variant="dark"
                      @click="
                        () => mutateDeletelegislacionesPais(el.legislacion)
                      "
                    >
                      <LucideEraser class="h-4 w-4" />
                    </Button>
                  </div>
                </Td>
              </Tr>
            </TBody>
          </Table>
        </div>
      </div>
      <div
        v-else-if="datalegislacionesPais && datalegislacionesPais.length === 0"
        class="p-10"
      >
        <h3 class="text-center font-bold text-xl">
          No hay datos disponibles para mostrar
        </h3>
      </div>
    </div>
  </div>
</template>
