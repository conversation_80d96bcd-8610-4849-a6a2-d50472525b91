<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import FormInput from '@c/components/form/FormInput.vue'
import { consultoresProyectoMetadata } from '@d/admin/models/consultores'
import Table from '@d/common/components/table/Table.vue'
import TBody from '@d/common/components/table/Tbody.vue'
import Td from '@d/common/components/table/Td.vue'
import Th from '@d/common/components/table/Th.vue'
import Thead from '@d/common/components/table/Thead.vue'
import Tr from '@d/common/components/table/Tr.vue'

import FormSelect from '@/common/components/form/FormSelect.vue'
import Loading from '@/common/components/loading.vue'

import { useVacacionesConsultor } from './vacaciones-consultor'

const {
  columnQuery,
  dataPaises,
  diasDisponibles,
  diasPer,
  diasVacAno,
  diasVacDerCons,
  diasVacDisfMesAct,
  diasVacDisfMesAnt,
  diasVacNoDisf,
  fechaCalculo,
  fechaIncorporacion,
  filteredData,
  formReactive,
  isConfVacPending,
  isConsultasVacacionesPending,
  isConsultoresPending,
  isPaisesPending,
  onUpdate,
  refetchConfVac,
  refetchConsultasVacaciones,
  refetchConsultores,
  resetConsultores,
  searchQuery,
  selectedConsultor
} = useVacacionesConsultor()
</script>

<template>
  <div class="intro-y box w-full col-span-2">
    <div
      :class="`flex flex-wrap p-5 gap-3 w-full border-b border-slate-200/60 justify-between`"
    >
      <div class="flex gap-3">
        <FormSelect formSelectSize="lg" class="w-6/12">
          <option value="" @click="columnQuery = ''">Seleccionar ...</option>
          <option
            v-for="row in consultoresProyectoMetadata"
            :key="row.key"
            :value="row.key"
            @click="columnQuery = row.key"
          >
            {{ row.label }}
          </option>
        </FormSelect>
        <div class="relative text-slate-500 w-5/12">
          <FormInput
            v-model="searchQuery"
            placeholder="Buscar..."
            class="pr-10"
            :disabled="columnQuery === ''"
            type="text"
          />
          <LucideSearch
            class="absolute inset-y-0 right-0 w-4 h-4 my-auto mr-3"
          />
        </div>
      </div>
      <div class="flex flex-col sm:flex-row gap-3">
        <Button
          variant="dark"
          @click="
            () => {
              refetchConsultores()
              resetConsultores()
            }
          "
        >
          <LucideRefreshCcw class="h-4 w-4" />
        </Button>
      </div>
    </div>
    <div
      v-if="isConsultoresPending"
      class="flex flex-col items-center m-5 justify-end col-span-6 sm:col-span-3 xl:col-span-2"
    >
      <Loading icon="three-dots" />
    </div>
    <div
      v-else-if="
        !isConsultoresPending && filteredData && filteredData.length > 0
      "
      class="p-5"
    >
      <div class="overflow-auto max-h-[360px]">
        <Table hover sm>
          <Thead variant="dark">
            <Tr>
              <Th
                v-for="row in consultoresProyectoMetadata"
                :key="row.key"
                class="whitespace-nowrap font-bold"
              >
                {{ row.label }}
              </Th>
            </Tr>
          </Thead>
          <TBody>
            <Tr
              v-for="el in filteredData"
              :key="el.id"
              :class="`dark:hover:bg-[#303761] hover:bg-[#dadef7] ${
                selectedConsultor?.id === el.id
                  ? 'dark:bg-[#303761] bg-[#dadef7]'
                  : ''
              }`"
              @click="
                () => {
                  selectedConsultor = el
                  refetchConfVac()
                  refetchConsultasVacaciones()
                }
              "
            >
              <Td>{{ el.nombre }}</Td>
              <Td>{{ el.apellido }}</Td>
            </Tr>
          </TBody>
        </Table>
      </div>
    </div>
    <div
      v-else-if="
        !isConsultoresPending && filteredData && filteredData.length === 0
      "
      class="p-10"
    >
      <h3 class="text-center font-bold text-xl">
        No hay datos disponibles para mostrar
      </h3>
    </div>
  </div>
  <div v-if="selectedConsultor != null" class="intro-y box">
    <div
      v-if="isPaisesPending || isConfVacPending"
      class="flex flex-warp p-5 gap-3 w-full h-full justify-center"
    >
      <Loading icon="puff" class="mx-auto" />
    </div>
    <template v-else>
      <div class="flex flex-col p-5 gap-5">
        <div class="flex flex-row justify-between gap-3">
          <label class="cursor-pointer select-none mt-3"> ID Consultor: </label>
          <FormInput
            v-model="formReactive.idconsultor"
            class="block px-4 py-3 w-8/12"
            placeholder="ID"
            label="ID"
            type="text"
            readonly
          />
        </div>
      </div>
      <div class="flex flex-col p-5 gap-5">
        <div class="flex flex-row justify-between gap-3">
          <label class="cursor-pointer select-none mt-1"> Legislación: </label>
          <FormSelect v-model="formReactive.legislacion" class="w-7/12">
            <option value="" @click="formReactive.legislacion = ''">
              Legislación
            </option>
            <option
              v-for="rowPais in dataPaises"
              :key="rowPais.idpais"
              :value="rowPais.codpaiscorep.toString()"
              @click="
                () =>
                  (formReactive.legislacion = rowPais.codpaiscorep.toString())
              "
            >
              {{ rowPais.nombrepaissgt.trimEnd() }}
            </option>
          </FormSelect>
        </div>
      </div>
      <div class="flex flex-col p-5 gap-5">
        <div class="flex flex-row justify-between gap-3">
          <label class="cursor-pointer select-none mt-3">
            Vac Disfrutadas a 31/10/2022:
          </label>
          <FormInput
            v-model="formReactive.vacdisf"
            class="block px-4 py-3 w-8/12"
            placeholder="Vac Disfrutadas"
            type="number"
          />
        </div>
      </div>
      <div class="flex flex-col p-5 gap-5">
        <div class="flex flex-row justify-between gap-3">
          <label class="cursor-pointer select-none mt-3">
            Días Permiso Adic:
          </label>
          <FormInput
            v-model="formReactive.diasperm"
            class="block px-4 py-3 w-8/12"
            placeholder="Días Permiso Adic"
            type="number"
          />
        </div>
      </div>
      <div
        v-if="formReactive.legislacion === 'ES'"
        class="flex flex-col p-5 gap-5"
      >
        <div class="flex flex-row justify-between gap-3">
          <label class="cursor-pointer select-none mt-3">
            Vac No Gastadas:
          </label>
          <FormInput
            v-model="formReactive.vacnogast"
            class="block px-4 py-3 w-8/12"
            placeholder="Vac No Gastadas"
            type="number"
          />
        </div>
      </div>
      <div class="flex flex-col p-5 gap-5">
        <Button variant="primary" class="w-9/12 mx-auto" @click="onUpdate">
          Modificar
        </Button>
      </div>
    </template>
  </div>
  <div v-if="selectedConsultor != null" class="intro-y box">
    <div
      v-if="isConsultasVacacionesPending"
      class="flex flex-warp p-5 gap-3 w-full h-full justify-center"
    >
      <Loading icon="puff" class="mx-auto" />
    </div>
    <div v-else class="flex flex-col gap-7 w-full">
      <div class="flex flex-row gap-8 mx-4">
        <p class="mr-2 mt-3 w-5/6">Fecha Cálculo:</p>
        <FormInput
          v-model="fechaCalculo"
          class="block px-4 py-3 w-7/12"
          type="text"
          readonly
        />
      </div>
      <div
        v-if="formReactive.legislacion !== 'ES'"
        class="flex flex-row gap-8 mx-4"
      >
        <p class="mr-2 mt-3 w-5/6">Fecha Incorporación:</p>
        <FormInput
          v-model="fechaIncorporacion"
          class="block px-4 py-3 w-7/12"
          type="text"
          readonly
        />
      </div>
      <div
        v-if="formReactive.legislacion !== 'ES'"
        class="flex flex-row gap-8 mx-4"
      >
        <p class="mr-2 mt-3 w-5/6">Días de Vac. Derecho Consolidado:</p>
        <FormInput
          v-model="diasVacDerCons"
          class="block px-4 py-3 w-7/12"
          type="text"
          readonly
        />
      </div>
      <div
        v-if="formReactive.legislacion === 'ES'"
        class="flex flex-row gap-8 mx-4"
      >
        <p class="mr-2 mt-3 w-5/6">Días de Vac. Año:</p>
        <FormInput
          v-model="diasVacAno"
          class="block px-4 py-3 w-7/12"
          type="text"
          readonly
        />
      </div>
      <div class="flex flex-row gap-8 mx-4">
        <p class="mr-2 mt-3 w-5/6">Días permiso adicionales:</p>
        <FormInput
          v-model="diasPer"
          class="block px-4 py-3 w-7/12"
          type="text"
          readonly
        />
      </div>
      <div
        v-if="formReactive.legislacion === 'ES'"
        class="flex flex-row gap-8 mx-4"
      >
        <p class="mr-2 mt-3 w-5/6">Días de Vac. no disfrutadas año anterior:</p>
        <FormInput
          v-model="diasVacNoDisf"
          class="block px-4 py-3 w-7/12"
          type="text"
          readonly
        />
      </div>
      <div class="flex flex-row gap-8 mx-4">
        <p class="mr-2 mt-3 w-5/6">Días vac. disfrutados hasta mes ant:</p>
        <FormInput
          v-model="diasVacDisfMesAnt"
          class="block px-4 py-3 w-7/12"
          type="text"
          readonly
        />
      </div>
      <div class="flex flex-row gap-8 mx-4">
        <p class="mr-2 mt-3 w-5/6">Días vac. disfrutados mes en curso:</p>
        <FormInput
          v-model="diasVacDisfMesAct"
          class="block px-4 py-3 w-7/12"
          type="text"
          readonly
        />
      </div>
      <div class="flex flex-row gap-8 mx-4">
        <p class="mr-2 mt-3 w-5/6 font-bold">Días disponibles:</p>
        <FormInput
          v-model="diasDisponibles"
          class="block px-4 py-3 w-7/12"
          type="text"
          readonly
        />
      </div>
    </div>
  </div>
</template>
