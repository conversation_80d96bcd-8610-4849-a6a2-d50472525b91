import type { Legislab } from '@d/admin/models/legislab'
import { getPaises } from '@d/admin/repository/consultores-repository'
import { useQuery } from '@tanstack/vue-query'

export const useCreateEditLegislacionesPais = (
  selectedRow: Ref<Legislab | null>
) => {
  const formReactive = reactive({ legislacion: '', nathab: 'H', diasper: 0 })

  const { data: dataPaises, isFetching: isPaisesPending } = useQuery({
    queryFn: getPaises,
    queryKey: ['legislaciones-pais-paises']
  })

  watch(selectedRow, value => {
    if (value != null) {
      formReactive.legislacion = value.legislacion
      formReactive.nathab = value.nathab
      formReactive.diasper = value.diasper
    }
  })

  const onSubmit = (): Legislab => ({
    legislacion: formReactive.legislacion,
    nathab: formReactive.nathab as 'H' | 'N',
    diasper: formReactive.diasper
  })

  return { dataPaises, formReactive, isPaisesPending, onSubmit }
}
