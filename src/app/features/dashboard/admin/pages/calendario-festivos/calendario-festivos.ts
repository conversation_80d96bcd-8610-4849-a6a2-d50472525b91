import {
  getCalendarioFestivos,
  createCalendarioFestivos,
  updateCalendarioFestivos,
  deleteCalendarioFestivos
} from '@d/admin/repository/calendario-festivos-repository'
import { useQuery, useMutation } from '@tanstack/vue-query'
import { useAlertStore } from '@c/stores/alert-store'
import type { CalendarioFestivos } from '@d/admin/models/calendario-festivos'

export const useCalendarioFestivos = () => {
  const columnQuery = ref('')
  const searchQuery = ref('')
  const openDialog = ref(false)
  const alertStore = useAlertStore()
  const selectedRow = ref<CalendarioFestivos | null>(null)

  const debouncedSearchQuery = useDebounce(searchQuery, 150)

  const {
    data: dataCalendarioFestivos,
    isFetching: isCalendarioFestivosPending,
    refetch: refetchCalendarioFestivos
  } = useQuery({
    queryFn: getCalendarioFestivos,
    queryKey: ['admin-calendario-festivos']
  })

  const {
    mutate: mutateCreateCalendarioFestivos,
    isPending: isCreatingCalendarioFestivos
  } = useMutation({
    mutationFn: (sis: Omit<CalendarioFestivos, 'id'>) =>
      createCalendarioFestivos(sis),
    onError: () => alertStore.showErrorAlert(),
    onSuccess: () => {
      alertStore.showSuccessAlert()
    }
  })

  const {
    mutate: mutateUpdateCalendarioFestivos,
    isPending: isUpdatingCalendarioFestivos
  } = useMutation({
    mutationFn: (input: Omit<CalendarioFestivos, 'id'>) =>
      updateCalendarioFestivos(selectedRow.value?.id ?? 0, input),
    onError: () => alertStore.showErrorAlert(),
    onSuccess: () => alertStore.showSuccessAlert()
  })

  const {
    mutate: mutateDeleteCalendarioFestivos,
    isPending: isDeletingCalendarioFestivos
  } = useMutation({
    mutationFn: deleteCalendarioFestivos,
    onError: () => alertStore.showErrorAlert(),
    onSuccess: () => {
      alertStore.showSuccessAlert()
      refetchCalendarioFestivos()
    }
  })

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim())
      return dataCalendarioFestivos.value ?? []

    return (dataCalendarioFestivos.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof CalendarioFestivos]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  const onEdit = (row: CalendarioFestivos) => {
    selectedRow.value = row
    setTimeout(() => (openDialog.value = true), 250)
  }

  const onSubmit = (res: Omit<CalendarioFestivos, 'id'>) => {
    if (selectedRow.value) {
      mutateUpdateCalendarioFestivos(res)
    } else {
      mutateCreateCalendarioFestivos(res)
    }
  }

  return {
    columnQuery,
    dataCalendarioFestivos,
    filteredData,
    mutateDeleteCalendarioFestivos,
    isCalendarioFestivosPending,
    isUpdatingCalendarioFestivos,
    onEdit,
    onSubmit,
    openDialog,
    refetchCalendarioFestivos,
    searchQuery,
    selectedRow
  }
}
