<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-redundant-type-constituents -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import SlideoverDescription from '@c/components/slideover/Description.vue'
import SlideoverPanel from '@c/components/slideover/Panel.vue'
import Slideover from '@c/components/slideover/Slideover.vue'
import type { CalendarioFestivos } from '@d/admin/models/calendario-festivos'
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'

import FormSelect from '@/common/components/form/FormSelect.vue'
import Loading from '@/common/components/loading.vue'

import { useCreateEditCalendarioFestivos } from './create-edit-calendario-festivos'

const props = defineProps<{
  open: boolean
  selectedRow: CalendarioFestivos | null
}>()

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'submit', value: Omit<CalendarioFestivos, 'id'>): void
}>()

const { selectedRow } = toRefs(props)

const { dataPaises, formReactive, isPaisesPending, onSubmit } =
  useCreateEditCalendarioFestivos(selectedRow)
</script>

<template>
  <Slideover size="lg" :open="props.open" @close="$emit('close')">
    <SlideoverPanel>
      <SlideoverDescription
        class="px-8 pt-10 pb-8 flex flex-col justify-between"
      >
        <div class="flex flex-col">
          <div class="flex flex-row w-full justify-between">
            <div class="text-xl font-medium">
              {{
                props.selectedRow == null
                  ? 'Insertar Calendario Festivos:'
                  : 'Editar Calendario Festivos:'
              }}
            </div>
          </div>
          <div class="intro-y box w-full">
            <div
              v-if="isPaisesPending"
              class="flex flex-warp p-5 gap-3 w-full justify-center"
            >
              <Loading icon="puff" class="mx-auto" />
            </div>
            <div v-else class="flex flex-col p-5 gap-5">
              <div class="flex flex-row justify-between gap-3">
                <label class="cursor-pointer select-none mt-1"> País: </label>
                <FormSelect v-model="formReactive.calendario" class="w-8/12">
                  <option value="" @click="formReactive.calendario = ''">
                    Seleccionar País
                  </option>
                  <option
                    v-for="rowPais in dataPaises"
                    :key="rowPais.idpais"
                    :value="rowPais.codpaiscorep.toString()"
                    @click="
                      () =>
                        (formReactive.calendario =
                          rowPais.codpaiscorep.toString())
                    "
                  >
                    {{ rowPais.codpaiscorep }}
                  </option>
                </FormSelect>
              </div>
              <div class="flex flex-row justify-between gap-3">
                <label class="cursor-pointer select-none mt-2">
                  Fecha Festiva:
                </label>
                <litepicker v-model="formReactive.fechafestiva" />
              </div>
            </div>
          </div>
        </div>

        <div class="flex flex-row gap-3 justify-end">
          <Button
            variant="primary"
            class="w-24 mr-1"
            @click="
              () => {
                $emit('close')
                emit('submit', onSubmit())
              }
            "
          >
            Guardar
          </Button>
          <Button
            variant="danger"
            class="w-24 mr-1"
            @click="() => $emit('close')"
          >
            Cancelar
          </Button>
        </div>
      </SlideoverDescription>
    </SlideoverPanel>
  </Slideover>
</template>
