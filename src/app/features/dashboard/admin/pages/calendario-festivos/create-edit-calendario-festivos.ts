import type { CalendarioFestivos } from '@d/admin/models/calendario-festivos'
import { getPaises } from '@d/admin/repository/consultores-repository'
import { useQuery } from '@tanstack/vue-query'

export const useCreateEditCalendarioFestivos = (
  selectedRow: Ref<CalendarioFestivos | null>
) => {
  const formReactive = reactive({ calendario: '', fechafestiva: '' })

  const { data: dataPaises, isFetching: isPaisesPending } = useQuery({
    queryFn: getPaises,
    queryKey: ['calendario-festivos-paises']
  })

  watch(selectedRow, value => {
    if (value != null) {
      formReactive.calendario = value.calendario ?? ''
      formReactive.fechafestiva = value.fechafestiva ?? ''
    }
  })

  const onSubmit = (): Omit<CalendarioFestivos, 'id'> => ({
    calendario: formReactive.calendario,
    fechafestiva: formReactive.fechafestiva
  })

  return { dataPaises, formReactive, isPaisesPending, onSubmit }
}
