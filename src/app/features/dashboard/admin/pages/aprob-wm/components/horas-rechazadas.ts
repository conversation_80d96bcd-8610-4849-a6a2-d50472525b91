import {
  getDataHorasRechazadas,
  putPendienteHoras
} from '@d/admin/repository/aprob-wm-repository'
import { useAlertStore } from '@c/stores/alert-store'
import type { Horas } from '@d/admin/models/horas'
import { useMutation, useQuery } from '@tanstack/vue-query'

export const useHorasRechazadas = () => {
  const selectedRows = ref<Horas[]>([])
  const selectedRowsLength = computed(() => selectedRows.value.length)
  const columnQuery = ref('')
  const searchQuery = ref('')
  const debouncedSearchQuery = useDebounce(searchQuery, 150)
  const alertStore = useAlertStore()

  const {
    isFetching: isHorasRechazadasPending,
    data: dataHorasRechazadas,
    refetch: refetchHorasRechazadas
  } = useQuery({
    queryKey: ['horas-rechazadaswm'],
    queryFn: () => getDataHorasRechazadas()
  })

  const { mutate: mutatePendienteHoras, isPending: isPendienteHorasPending } =
    useMutation({
      mutationFn: (id: number) => putPendienteHoras(id),
      onSuccess: () => {
        alertStore.showSuccessAlert()
        refetchHorasRechazadas()
      },
      onError: () => alertStore.showErrorAlert()
    })

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim())
      return dataHorasRechazadas.value ?? []

    return (dataHorasRechazadas.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof Horas]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  return {
    columnQuery,
    dataHorasRechazadas,
    filteredData,
    isPendienteHorasPending,
    mutatePendienteHoras,
    isHorasRechazadasPending,
    refetchHorasRechazadas,
    searchQuery,
    selectedRowsLength
  }
}
