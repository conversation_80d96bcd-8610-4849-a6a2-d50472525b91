<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import FormInput from '@c/components/form/FormInput.vue'
import SlideoverDescription from '@c/components/slideover/Description.vue'
import SlideoverPanel from '@c/components/slideover/Panel.vue'
import Slideover from '@c/components/slideover/Slideover.vue'
import { horasMetadata } from '@d/admin/models/horas'
import Table from '@d/common/components/table/Table.vue'
import TBody from '@d/common/components/table/Tbody.vue'
import Td from '@d/common/components/table/Td.vue'
import Th from '@d/common/components/table/Th.vue'
import Thead from '@d/common/components/table/Thead.vue'
import Tr from '@d/common/components/table/Tr.vue'

import FormCheck from '@/common/components/form/FormCheck/FormCheck.vue'
import FormCheckInput from '@/common/components/form/FormCheck/Input.vue'
import FormSelect from '@/common/components/form/FormSelect.vue'
import Loading from '@/common/components/loading.vue'

import { useHorasAprobar } from './horas-aprobar'

const {
  columnQuery,
  dataHorasAprobar,
  filteredData,
  handleCheckboxChange,
  isHorasAprobarPending,
  refetchHorasAprobar,
  rejectInput,
  searchQuery,
  selectedRowsLength,
  showDeleteDialog
} = useHorasAprobar()
</script>

<template>
  <div class="intro-y box">
    <div
      class="flex flex-col items-center p-5 border-b sm:flex-row border-slate-200/60 justify-between"
    >
      <div class="flex flex-col sm:flex-row gap-3">
        <FormSelect formSelectSize="lg" class="w-22">
          <option value="" @click="columnQuery = ''">
            Seleccionar Columna
          </option>
          <option
            v-for="row in horasMetadata"
            :key="row.key"
            :value="row.key"
            @click="columnQuery = row.key"
          >
            {{ row.label }}
          </option>
        </FormSelect>
        <div class="relative text-slate-500 w-11/12">
          <FormInput
            v-model="searchQuery"
            placeholder="Buscar..."
            class="pr-10"
            :disabled="columnQuery === ''"
            type="text"
          />
          <LucideSearch
            class="absolute inset-y-0 right-0 w-4 h-4 my-auto mr-3"
          />
        </div>
      </div>

      <div class="flex flex-col sm:flex-row gap-3">
        <Button variant="dark" @click="refetchHorasAprobar">
          <LucideRefreshCcw class="h-4 w-4" />
        </Button>
      </div>
    </div>
    <div
      v-if="isHorasAprobarPending"
      class="flex flex-col items-center m-5 justify-end col-span-6 sm:col-span-3 xl:col-span-2"
    >
      <Loading icon="three-dots" />
    </div>
    <div
      v-else-if="
        !isHorasAprobarPending && dataHorasAprobar && filteredData.length > 0
      "
      class="p-5"
    >
      <div class="overflow-auto max-h-[360px]">
        <Table hover sm>
          <Thead variant="dark">
            <Tr>
              <Th class="whitespace-nowrap font-bold">
                <LucideCheck class="h-3 w-3" />
              </Th>
              <Th
                v-for="row in horasMetadata"
                :key="row.key"
                class="whitespace-nowrap font-bold"
              >
                {{ row.label }}
              </Th>
            </Tr>
          </Thead>
          <TBody>
            <Tr
              v-for="el in filteredData"
              :key="el.id"
              class="dark:hover:bg-[#303761] hover:bg-[#dadef7]"
            >
              <Td>
                <FormCheck>
                  <FormCheckInput
                    type="checkbox"
                    :value="el.id"
                    @change="e => handleCheckboxChange(e, el)"
                  />
                </FormCheck>
              </Td>
              <Td>{{ el.fecha }}</Td>
              <Td>{{ el.nombrecompleto }}</Td>
              <Td>{{ el.horastrabajadas }}</Td>
              <Td>{{ el.proyecto }}</Td>
              <Td>{{ el.nombretarea }}</Td>
              <Td>{{ el.etapa }}</Td>
              <Td :class="el.descripcion === '' ? 'text-slate-500 italic' : ''">
                {{ el.descripcion === '' ? 'Vacío' : el.descripcion }}
              </Td>
            </Tr>
          </TBody>
        </Table>
      </div>
      <div class="flex flex-row w-full justify-center">
        <div class="mt-6 flex flex-row gap-3">
          <Button
            :variant="selectedRowsLength > 0 ? 'primary' : 'dark'"
            :disabled="selectedRowsLength === 0"
            class="w-24 mr-1"
          >
            Aprobar
          </Button>
          <Button
            :variant="selectedRowsLength > 0 ? 'danger' : 'dark'"
            :disabled="selectedRowsLength === 0"
            class="w-24 mr-1"
            @click="showDeleteDialog = true"
          >
            Rechazar
          </Button>
        </div>
      </div>
    </div>
    <div
      v-else-if="
        !isHorasAprobarPending && dataHorasAprobar && filteredData.length === 0
      "
      class="p-10"
    >
      <h3 class="text-center font-bold text-xl">
        No hay datos disponibles para mostrar
      </h3>
    </div>
  </div>
  <Slideover
    size="lg"
    :open="showDeleteDialog"
    @close="() => (showDeleteDialog = false)"
  >
    <SlideoverPanel>
      <SlideoverDescription
        class="px-8 pt-10 pb-8 flex flex-col justify-between"
      >
        <div class="flex flex-col">
          <div class="text-xl font-medium">Comentario de Rechazo:</div>
          <div class="intro-y box">
            <div class="flex flex-warp p-5 gap-3">
              <div class="flex gap-3">
                <FormInput
                  v-model="rejectInput"
                  class="block px-4 py-3 intro-x min-w-full xl:min-w-[450px]"
                  placeholder="Motivo de rechazo..."
                  type="text"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="flex flex-row gap-3 justify-end">
          <Button
            variant="primary"
            class="w-24 mr-1"
            :disabled="rejectInput.trim() === ''"
          >
            Enviar
          </Button>
          <Button
            variant="danger"
            class="w-24 mr-1"
            @click="() => (showDeleteDialog = false)"
          >
            Cancelar
          </Button>
        </div>
      </SlideoverDescription>
    </SlideoverPanel>
  </Slideover>
</template>
