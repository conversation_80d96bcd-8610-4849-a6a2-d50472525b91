<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script>
import Proyecto from './components/proyecto.vue'
import TareasProyecto from './components/tareas-proyecto.vue'

export default { components: { Proyecto, TareasProyecto } }
</script>

<template>
  <div>
    <div class="flex items-center mt-8 intro-y">
      <h2 class="mr-auto text-2xl font-medium"><PERSON><PERSON><PERSON></h2>
    </div>
    <div class="grid grid-cols-12 gap-6 mt-5 intro-y">
      <div class="col-span-full">
        <Proyecto />
        <TareasProyecto />
      </div>
    </div>
  </div>
</template>
