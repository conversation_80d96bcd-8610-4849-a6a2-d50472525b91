import {
  getTareaByProyecto,
  insertTarea,
  updateTarea
} from '@d/admin/repository/tareas-repository'
import type { Tarea } from '@d/admin/models/tarea'
import { useMutation, useQuery } from '@tanstack/vue-query'
import { useProyectoStore } from '@d/admin/stores/proyecto-store'

export const defaultForm = {
  clavecorta: '',
  nombretarea: '',
  clavecortaproyecto: '',
  estatus: 'A',
  horas: 0,
  fechainicio: new Date().toISOString().split('T')[0] ?? '',
  fechafin: new Date().toISOString().split('T')[0] ?? ''
}

export const useTareasProyecto = () => {
  const columnQuery = ref('')
  const searchQuery = ref('')
  const selectedIndex = ref<number | null>(null)
  const showDialog = ref(false)
  const debouncedSearchQuery = useDebounce(searchQuery, 150)
  const store = useProyectoStore()

  const {
    isFetching: isTareasProyectoPending,
    data: dataTareasProyecto,
    refetch: refetchTareasProyecto
  } = useQuery({
    queryKey: ['tareasproyecto'],
    enabled: !store.isProyecto,
    queryFn: () => getTareaByProyecto(store.proyecto ?? '')
  })

  const { mutate: createTarea, isPending: isCreatingTarea } = useMutation({
    mutationFn: (data: typeof defaultForm) =>
      insertTarea(
        data.estatus,
        data.horas ?? 0,
        data.fechainicio ?? '',
        data.fechafin ?? '',
        data.clavecorta
      ),
    onSuccess: () => {
      refetchTareasProyecto()
    }
  })

  const { mutate: editTarea, isPending: isEditingTarea } = useMutation({
    mutationFn: (data: typeof defaultForm) =>
      updateTarea(
        data.estatus,
        data.horas ?? 0,
        data.fechainicio ?? '',
        data.fechafin ?? '',
        data.clavecorta
      ),
    onSuccess: () => {
      refetchTareasProyecto()
    }
  })

  const formData = reactive(defaultForm)

  watch(
    () => store.proyecto,
    proyecto => {
      if (proyecto !== null) {
        refetchTareasProyecto()
      } else {
        columnQuery.value = ''
        searchQuery.value = ''
      }
    }
  )

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim())
      return dataTareasProyecto.value ?? []

    return (dataTareasProyecto.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof Tarea]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  const onSubmit = () => {
    showDialog.value = false
    if (selectedIndex.value !== null) {
      editTarea(formData)
    } else {
      createTarea(formData)
    }
  }

  return {
    columnQuery,
    isTareasProyectoPending,
    filteredData,
    formData,
    onSubmit,
    isCreatingTarea,
    isEditingTarea,
    refetchTareasProyecto,
    searchQuery,
    selectedIndex,
    showDialog
  }
}
