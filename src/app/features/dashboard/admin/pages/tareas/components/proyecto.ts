import { getProyectos } from '@d/admin/repository/tareas-repository'
import type { Proyecto } from '@d/admin/models/proyecto'
import { useQuery } from '@tanstack/vue-query'
import { useProyectoStore } from '@d/admin/stores/proyecto-store'

export const useProyecto = () => {
  const columnQuery = ref('')
  const searchQuery = ref('')
  const selectedIndex = ref<number | null>(null)
  const debouncedSearchQuery = useDebounce(searchQuery, 150)

  const store = useProyectoStore()

  const {
    isFetching: isProyectoPending,
    data: dataProyecto,
    refetch: refetchProyecto
  } = useQuery({
    queryKey: ['proyecto'],
    queryFn: () => getProyectos()
  })

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim()) return dataProyecto.value ?? []

    return (dataProyecto.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof Proyecto]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  const onRowClick = (proyecto: string, index: number) => {
    store.setProyecto(proyecto)
    selectedIndex.value = index
  }

  const resetProyecto = () => {
    store.setProyecto(null)
    selectedIndex.value = null
  }

  return {
    columnQuery,
    isProyectoPending,
    filteredData,
    refetchProyecto,
    onRowClick,
    resetProyecto,
    searchQuery,
    selectedIndex
  }
}
