<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import FormInput from '@c/components/form/FormInput.vue'
import SlideoverDescription from '@c/components/slideover/Description.vue'
import SlideoverPanel from '@c/components/slideover/Panel.vue'
import Slideover from '@c/components/slideover/Slideover.vue'
import { tareaMetadata } from '@d/admin/models/tarea'
import { useProyectoStore } from '@d/admin/stores/proyecto-store'
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'
import Table from '@d/common/components/table/Table.vue'
import TBody from '@d/common/components/table/Tbody.vue'
import Td from '@d/common/components/table/Td.vue'
import Th from '@d/common/components/table/Th.vue'
import Thead from '@d/common/components/table/Thead.vue'
import Tr from '@d/common/components/table/Tr.vue'

import FormSelect from '@/common/components/form/FormSelect.vue'
import Loading from '@/common/components/loading.vue'

import { defaultForm, useTareasProyecto } from './tareas-proyecto'

const {
  columnQuery,
  filteredData,
  formData,
  isTareasProyectoPending,
  onSubmit,
  refetchTareasProyecto,
  searchQuery,
  selectedIndex,
  showDialog
} = useTareasProyecto()

const store = useProyectoStore()
</script>

<template>
  <div v-if="store.isProyecto" class="intro-y box mt-5">
    <div
      class="flex flex-col items-center p-5 border-b sm:flex-row border-slate-200/60 justify-between"
    >
      <h2 class="text-lg font-medium">Tareas por Proyecto</h2>
      <div class="flex flex-row gap-3">
        <div class="flex flex-col sm:flex-row gap-3">
          <FormSelect formSelectSize="lg" class="w-22">
            <option value="" @click="columnQuery = ''">
              Seleccionar Columna
            </option>
            <option
              v-for="row in tareaMetadata"
              :key="row.key"
              :value="row.key"
              @click="columnQuery = row.key"
            >
              {{ row.label }}
            </option>
          </FormSelect>
          <div class="relative text-slate-500 w-11/12">
            <FormInput
              v-model="searchQuery"
              placeholder="Buscar..."
              class="pr-10"
              :disabled="columnQuery === ''"
              type="text"
            />
            <LucideSearch
              class="absolute inset-y-0 right-0 w-4 h-4 my-auto mr-3"
            />
          </div>
        </div>

        <div class="flex flex-col sm:flex-row gap-3">
          <Button
            variant="dark"
            @click="
              () => {
                refetchTareasProyecto()
                selectedIndex = null
              }
            "
          >
            <LucideRefreshCcw class="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
    <div
      v-if="isTareasProyectoPending"
      class="flex flex-col items-center m-5 justify-end col-span-6 sm:col-span-3 xl:col-span-2"
    >
      <Loading icon="three-dots" />
    </div>
    <div
      v-else-if="
        !isTareasProyectoPending && filteredData && filteredData.length > 0
      "
      class="p-5"
    >
      <div class="overflow-auto max-h-[360px]">
        <Table hover sm>
          <Thead variant="dark">
            <Tr>
              <Th
                v-for="row in tareaMetadata"
                :key="row.key"
                class="whitespace-nowrap font-bold"
              >
                {{ row.label }}
              </Th>
            </Tr>
          </Thead>
          <TBody>
            <Tr
              v-for="el in filteredData"
              :key="el.idtarea"
              :class="`dark:hover:bg-[#303761] hover:bg-[#dadef7] ${
                selectedIndex === el.idtarea
                  ? 'dark:bg-[#303761] bg-[#dadef7]'
                  : ''
              }`"
              @click="() => (selectedIndex = el.idtarea)"
            >
              <Td>{{ el.idtarea }}</Td>
              <Td>{{ el.clavecorta }}</Td>
              <Td>{{ el.nombretarea }}</Td>
              <Td>{{ el.clavecortaproyecto }}</Td>
              <Td>{{ el.estatus }}</Td>
              <Td :class="!el.horas ? 'text-slate-500 italic' : ''">
                {{ !el.horas ? 'Vacío' : el.horas }}
              </Td>
              <Td :class="!el.fechainicio ? 'text-slate-500 italic' : ''">
                {{ !el.fechainicio ? 'Vacío' : el.fechainicio }}
              </Td>
              <Td :class="!el.fechafin ? 'text-slate-500 italic' : ''">
                {{ !el.fechafin ? 'Vacío' : el.fechafin }}
              </Td>
            </Tr>
          </TBody>
        </Table>
      </div>
      <div class="flex flex-row w-full justify-center">
        <div class="mt-6 flex flex-row gap-3">
          <Button
            variant="primary"
            class="w-24 mr-1"
            @click="
              () => {
                showDialog = true
                formData = { ...defaultForm }
              }
            "
          >
            Nuevo
          </Button>
          <Button
            variant="primary"
            class="w-24 mr-1"
            :disabled="selectedIndex === null"
            @click="
              () => {
                showDialog = true
                const selectedItem =
                  filteredData.find(item => item.idtarea === selectedIndex) ??
                  null
                formData = {
                  clavecorta: selectedItem?.clavecorta ?? '',
                  nombretarea: selectedItem?.nombretarea ?? '',
                  clavecortaproyecto: selectedItem?.clavecortaproyecto ?? '',
                  estatus: selectedItem?.estatus ?? 'A',
                  horas: selectedItem?.horas ?? 0,
                  fechainicio: selectedItem?.fechainicio ?? '',
                  fechafin: selectedItem?.fechafin ?? ''
                }
              }
            "
          >
            Editar
          </Button>
        </div>
      </div>
    </div>
    <div
      v-else-if="
        !isTareasProyectoPending && filteredData && filteredData.length === 0
      "
      class="p-10"
    >
      <h3 class="text-center font-bold text-xl">
        No hay datos disponibles para mostrar
      </h3>
    </div>
  </div>
  <Slideover size="lg" :open="showDialog" @close="() => (showDialog = false)">
    <SlideoverPanel>
      <SlideoverDescription
        class="px-8 pt-10 pb-8 flex flex-col justify-between"
      >
        <div class="flex flex-col">
          <div class="text-xl font-medium">Nueva Tarea</div>
          <div class="intro-y box">
            <div class="flex flex-warp p-5 gap-3">
              <form id="new-tarea-form" class="flex flex-col gap-8 w-full">
                <div class="flex flex-col">
                  <FormLabel htmlFor="new-tarea-form" class="mb-1">
                    Clave Corta: *
                  </FormLabel>
                  <FormInput
                    id="new-tarea-form"
                    v-model="formData.clavecorta"
                    type="text"
                    placeholder="Clave Corta"
                  />
                </div>
                <div class="flex flex-col">
                  <FormLabel htmlFor="new-tarea-form" class="mb-1">
                    Nombre Tarea: *
                  </FormLabel>
                  <FormInput
                    id="new-tarea-form"
                    v-model="formData.nombretarea"
                    type="text"
                    placeholder="Nombre Tarea"
                  />
                </div>
                <div class="flex flex-col">
                  <FormLabel htmlFor="new-tarea-form" class="mb-1">
                    Estatus:
                  </FormLabel>
                  <FormSelect
                    id="new-tarea-form"
                    v-model="formData.estatus"
                    formSelectSize="lg"
                    class="w-22"
                  >
                    <option value="A">A</option>
                    <option value="C">C</option>
                  </FormSelect>
                </div>
                <div class="flex flex-col">
                  <FormLabel htmlFor="new-tarea-form" class="mb-1">
                    Horas Presupuestadas:
                  </FormLabel>
                  <FormInput
                    id="new-tarea-form"
                    v-model.number="formData.horas"
                    type="number"
                  />
                </div>
                <div class="flex flex-col">
                  <FormLabel htmlFor="new-tarea-form" class="mb-1">
                    Fecha Inicio:
                  </FormLabel>
                  <Litepicker
                    v-model="formData.fechainicio"
                    :options="{
                      autoApply: false,
                      showWeekNumbers: true,
                      dropdowns: {
                        minYear: 2020,
                        maxYear: 2035,
                        months: true,
                        years: true
                      }
                    }"
                  />
                </div>
                <div class="flex flex-col">
                  <FormLabel htmlFor="new-tarea-form" class="mb-1">
                    Fecha Fin:
                  </FormLabel>
                  <Litepicker
                    v-model="formData.fechafin"
                    :options="{
                      autoApply: false,
                      showWeekNumbers: true,
                      dropdowns: {
                        minYear: 2020,
                        maxYear: 2035,
                        months: true,
                        years: true
                      }
                    }"
                  />
                </div>
              </form>
            </div>
          </div>
        </div>
        <div class="flex flex-row gap-3 justify-end">
          <Button variant="primary" class="w-24 mr-1" @click="onSubmit">
            Enviar
          </Button>
          <Button
            variant="danger"
            class="w-24 mr-1"
            @click="() => (showDialog = false)"
          >
            Cancelar
          </Button>
        </div>
      </SlideoverDescription>
    </SlideoverPanel>
  </Slideover>
</template>
