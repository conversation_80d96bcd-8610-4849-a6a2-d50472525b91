import {
  getProyec<PERSON>,
  createProy<PERSON>to,
  updateProyecto,
  deleteProyecto
} from '@d/admin/repository/proyectos-repository'
import { useQuery, useMutation } from '@tanstack/vue-query'
import { useAlertStore } from '@c/stores/alert-store'
import type { Proyecto } from '@d/admin/models/proyecto'

export const useProyectos = () => {
  const columnQuery = ref('')
  const searchQuery = ref('')
  const openDialog = ref(false)
  const alertStore = useAlertStore()
  const selectedRow = ref<Proyecto | null>(null)

  const debouncedSearchQuery = useDebounce(searchQuery, 150)

  const {
    data: dataProyectos,
    isFetching: isProyectosPending,
    refetch: refetchProyectos
  } = useQuery({
    queryFn: getProyectos,
    queryKey: ['admin-proyectos']
  })

  const { mutate: mutateCreateProyecto, isPending: isCreatingProyecto } =
    useMutation({
      mutationFn: (sis: Omit<Proyecto, 'idproyecto'>) => createProyecto(sis),
      onError: () => alertStore.showErrorAlert(),
      onSuccess: () => {
        alertStore.showSuccessAlert()
      }
    })

  const { mutate: mutateUpdateProyecto, isPending: isUpdatingProyecto } =
    useMutation({
      mutationFn: (input: Omit<Proyecto, 'idproyecto'>) =>
        updateProyecto(selectedRow.value?.idproyecto ?? 0, input),
      onError: () => alertStore.showErrorAlert(),
      onSuccess: () => {
        alertStore.showSuccessAlert()
      }
    })

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim()) return dataProyectos.value ?? []

    return (dataProyectos.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof Proyecto]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  const { mutate: mutateDeleteProyecto, isPending: isDeletingProyecto } =
    useMutation({
      mutationFn: deleteProyecto,
      onError: () => alertStore.showErrorAlert(),
      onSuccess: () => {
        alertStore.showSuccessAlert()
        refetchProyectos()
      }
    })

  const onEdit = (row: Proyecto) => {
    selectedRow.value = row
    setTimeout(() => (openDialog.value = true), 250)
  }

  const onSubmit = (res: Omit<Proyecto, 'idproyecto'>) => {
    if (selectedRow.value) {
      mutateUpdateProyecto(res)
    } else {
      mutateCreateProyecto(res)
    }
  }

  const loadingOperations = computed(
    () =>
      isProyectosPending ||
      isCreatingProyecto ||
      isUpdatingProyecto ||
      isDeletingProyecto
  )

  return {
    columnQuery,
    dataProyectos,
    filteredData,
    isProyectosPending,
    isDeletingProyecto,
    loadingOperations,
    mutateDeleteProyecto,
    onEdit,
    onSubmit,
    openDialog,
    refetchProyectos,
    searchQuery,
    selectedRow
  }
}
