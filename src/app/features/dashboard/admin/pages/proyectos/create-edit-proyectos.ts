import type { Proyecto } from '@d/admin/models/proyecto'
import {
  getPaises,
  consultorJefesFunc
} from '@d/admin/repository/consultores-repository'
import { getClientes } from '@d/admin/repository/clientes-repository'
import { useQuery } from '@tanstack/vue-query'

export const roleOpts = ['admin', 'wpm', 'pm', 'user', 'cese', 'wm']

export const useCreateEditProyectos = (selectedRow: Ref<Proyecto | null>) => {
  const selectedJefeProyecto = reactive<{
    model: string
    id: number | null
  }>({ model: '', id: null })

  const selectedCliente = reactive<{
    model: string
    id: number | null
  }>({ model: '', id: null })

  const { data: dataPaises, isFetching: isPaisesPending } = useQuery({
    queryFn: getPaises,
    queryKey: ['proyectos-paises']
  })

  const { data: dataJefesFunc, isFetching: isJefesFuncPending } = useQuery({
    queryFn: consultorJefesFunc,
    queryKey: ['proyectos-jefesfunc']
  })

  const { data: dataClientes, isFetching: isClientesPending } = useQuery({
    queryFn: getClientes,
    queryKey: ['proyectos-clientes']
  })

  const formReactive = reactive({
    clavecorta: '',
    nombreproyecto: '',
    pais: '',
    fechacreacion: '',
    jefedeproyecto: 0,
    cliente: 0,
    horasproy: 0
  })

  watch(selectedRow, value => {
    if (value != null) {
      formReactive.clavecorta = value.clavecorta
      formReactive.nombreproyecto = value.nombreproyecto
      formReactive.pais = value.pais
      selectedJefeProyecto.id = value.jefedeproyecto ?? null
      selectedJefeProyecto.model = value.usuariojefe ?? ''
      selectedCliente.id =
        typeof value.cliente === 'number' ? value.cliente : null
      selectedCliente.model = value.nombrecliente ?? ''
      formReactive.fechacreacion = value.fechacreacion
      formReactive.jefedeproyecto = value.jefedeproyecto
      formReactive.cliente =
        typeof value.cliente === 'number' ? value.cliente : 0
      formReactive.horasproy = value.horasproy ?? 0
    }
  })

  const onSubmit = (): Omit<Proyecto, 'idproyecto'> => ({
    clavecorta: formReactive.clavecorta,
    nombreproyecto: formReactive.nombreproyecto,
    pais: formReactive.pais,
    fechacreacion: formReactive.fechacreacion,
    jefedeproyecto: selectedJefeProyecto.id ?? 0,
    cliente: formReactive.cliente,
    horasproy: formReactive.horasproy
  })

  return {
    dataClientes,
    dataJefesFunc,
    dataPaises,
    formReactive,
    isClientesPending,
    isJefesFuncPending,
    isPaisesPending,
    onSubmit,
    selectedCliente,
    selectedJefeProyecto
  }
}
