<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-redundant-type-constituents -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import FormInput from '@c/components/form/FormInput.vue'
import SlideoverDescription from '@c/components/slideover/Description.vue'
import SlideoverPanel from '@c/components/slideover/Panel.vue'
import Slideover from '@c/components/slideover/Slideover.vue'
import type { Proyecto } from '@d/admin/models/proyecto'
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'

import FormSelect from '@/common/components/form/FormSelect.vue'
import Loading from '@/common/components/loading.vue'

import { useCreateEditProyectos } from './create-edit-proyectos'

const props = defineProps<{
  open: boolean
  selectedRow: Proyecto | null
}>()

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'submit', value: Omit<Proyecto, 'idproyecto'>): void
}>()

const { selectedRow } = toRefs(props)

const {
  dataClientes,
  dataJefesFunc,
  dataPaises,
  formReactive,
  isClientesPending,
  isJefesFuncPending,
  isPaisesPending,
  onSubmit,
  selectedCliente,
  selectedJefeProyecto
} = useCreateEditProyectos(selectedRow)
</script>

<template>
  <Slideover size="lg" :open="props.open" @close="$emit('close')">
    <SlideoverPanel>
      <SlideoverDescription
        class="px-8 pt-10 pb-8 flex flex-col justify-between"
      >
        <div class="flex flex-col">
          <div class="flex flex-row w-full justify-between">
            <div class="text-xl font-medium">
              {{
                props.selectedRow == null
                  ? 'Insertar Proyecto:'
                  : 'Editar Proyecto:'
              }}
            </div>
          </div>
          <div class="intro-y box w-full">
            <div
              v-if="isPaisesPending || isJefesFuncPending || isClientesPending"
              class="flex flex-warp p-5 gap-3 w-full justify-center"
            >
              <Loading icon="puff" class="mx-auto" />
            </div>
            <div v-else class="flex flex-col p-5 gap-5">
              <div class="flex flex-row justify-between gap-3">
                <label class="cursor-pointer select-none mt-3">
                  Clave Corta:
                </label>
                <FormInput
                  v-model="formReactive.clavecorta"
                  class="block px-4 py-3 w-8/12"
                  placeholder="Clave Corta"
                  label="Clave Corta"
                  type="text"
                />
              </div>
              <div class="flex flex-row justify-between gap-4">
                <label class="cursor-pointer select-none mt-3">
                  Nombre Proyecto:
                </label>
                <FormInput
                  v-model="formReactive.nombreproyecto"
                  class="block px-4 py-3 w-8/12"
                  placeholder="Nombre Proyecto"
                  label="Nombre Proyecto"
                  type="text"
                />
              </div>
              <div class="flex flex-row justify-between gap-3">
                <label class="cursor-pointer select-none mt-1"> País: </label>
                <FormSelect v-model="formReactive.pais" class="w-8/12">
                  <option value="" @click="formReactive.pais = ''">
                    Seleccionar País
                  </option>
                  <option
                    v-for="rowPais in dataPaises"
                    :key="rowPais.idpais"
                    :value="rowPais.codpaiscorep.toString()"
                    @click="
                      () =>
                        (formReactive.pais = rowPais.codpaiscorep.toString())
                    "
                  >
                    {{ rowPais.codpaiscorep }} - &nbsp;
                    {{ rowPais.nombrepaissgt.trimEnd() }}
                  </option>
                </FormSelect>
              </div>
              <div class="flex flex-row justify-between gap-3">
                <label class="cursor-pointer select-none mt-2">
                  Fecha Ingreso:
                </label>
                <div class="relative w-80">
                  <div
                    class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
                  >
                    <LucideCalendar class="w-4 h-4" />
                  </div>
                  <ClientOnly>
                    <Litepicker
                      v-model="formReactive.fechacreacion"
                      class="pl-12"
                      :options="{
                        autoApply: true,
                        dropdowns: {
                          minYear: 2020,
                          maxYear: 2035,
                          months: true,
                          years: true
                        }
                      }"
                    />
                  </ClientOnly>
                </div>
              </div>
              <div class="flex flex-row justify-between gap-3">
                <label class="cursor-pointer select-none mt-1">
                  Jefe de Proyecto:
                </label>
                <FormSelect v-model="selectedJefeProyecto.model" class="w-8/12">
                  <option value="" @click="selectedJefeProyecto.model = ''">
                    Seleccionar Jefe de Proyecto
                  </option>
                  <option
                    v-for="rowJefe in dataJefesFunc"
                    :key="rowJefe.id"
                    :value="rowJefe.id.toString()"
                    @click="
                      () => {
                        selectedJefeProyecto.model = rowJefe.usuario
                        selectedJefeProyecto.id = rowJefe.id
                      }
                    "
                  >
                    {{ rowJefe.nombre }} - {{ rowJefe.apellido }}
                  </option>
                </FormSelect>
              </div>
              <div class="flex flex-row justify-between gap-3">
                <label class="cursor-pointer select-none mt-1">
                  Cliente:
                </label>
                <FormSelect v-model="selectedCliente.model" class="w-8/12">
                  <option value="" @click="selectedCliente.model = ''">
                    Seleccionar Cliente
                  </option>
                  <option
                    v-for="rowCliente in dataClientes"
                    :key="rowCliente.id"
                    :value="rowCliente"
                    @click="
                      () => (selectedCliente.model = rowCliente.nombrecliente)
                    "
                  >
                    {{ rowCliente.nombrecliente }}
                  </option>
                </FormSelect>
              </div>

              <div class="flex flex-row justify-between gap-4">
                <label class="cursor-pointer select-none mt-3">
                  Horas Presupuestadas:
                </label>
                <FormInput
                  v-model="formReactive.horasproy"
                  class="block px-4 py-3 w-8/12"
                  placeholder="Horas Presupuestadas"
                  label="Horas Presupuestadas"
                  type="number"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="flex flex-row gap-3 justify-end">
          <Button
            variant="primary"
            class="w-24 mr-1"
            @click="
              () => {
                $emit('close')
                emit('submit', onSubmit())
              }
            "
          >
            Guardar
          </Button>
          <Button
            variant="danger"
            class="w-24 mr-1"
            @click="() => $emit('close')"
          >
            Cancelar
          </Button>
        </div>
      </SlideoverDescription>
    </SlideoverPanel>
  </Slideover>
</template>
