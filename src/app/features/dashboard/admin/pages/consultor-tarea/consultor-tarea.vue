<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup>
import DisclosureButton from '@d/common/components/disclosure/Button.vue'
import Disclosure from '@d/common/components/disclosure/Disclosure/Disclosure.vue'
import DisclosureGroup from '@d/common/components/disclosure/Group.vue'
import DisclosurePanel from '@d/common/components/disclosure/Panel.vue'

import Consultores from './components/consultores.vue'
import ConsultoresTarea from './components/consultores-tarea.vue'
import ProyectoConsultor from './components/proyecto-consultor.vue'
import Proyectos from './components/proyectos.vue'
import Tareas from './components/tareas.vue'
import TareasAsignadas from './components/tareas-asignadas.vue'
import TareasProyecto from './components/tareas-proyecto.vue'
</script>

<template>
  <div>
    <div class="flex items-center mt-8 intro-y">
      <h2 class="mr-auto text-2xl font-medium">Consultor / Tarea</h2>
    </div>
    <div class="grid grid-cols-12 gap-6 mt-5 intro-y">
      <div class="col-span-full">
        <DisclosureGroup class="mt-3">
          <Disclosure class="box my-3" index="0">
            <DisclosureButton
              class="px-4 flex flex-row justify-between"
              :openDisc="true"
            >
              <h4 class="mt-0.5 text-xl">Asignación Tareas</h4>
              <LucidePlus />
            </DisclosureButton>
            <DisclosurePanel class="grid grid-cols-2 gap-5 justify-center">
              <Consultores />
              <ProyectoConsultor />
              <TareasProyecto />
              <TareasAsignadas />
            </DisclosurePanel>
          </Disclosure>
          <Disclosure class="box my-3" index="0">
            <DisclosureButton
              class="px-4 flex flex-row justify-between"
              :openDisc="true"
            >
              <h4 class="mt-0.5 text-xl">Proyecto - Tarea - Consultor</h4>
              <LucidePlus />
            </DisclosureButton>
            <DisclosurePanel class="flex flex-row justify-center gap-3">
              <Proyectos />
              <Tareas />
              <ConsultoresTarea />
            </DisclosurePanel>
          </Disclosure>
        </DisclosureGroup>
      </div>
    </div>
  </div>
</template>
