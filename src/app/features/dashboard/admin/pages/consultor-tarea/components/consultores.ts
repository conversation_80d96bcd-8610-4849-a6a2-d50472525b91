import { getConsultores } from '@d/admin/repository/consultor-tareas-repository'
import type { Consultores } from '@d/admin/models/consultores'
import { useQuery } from '@tanstack/vue-query'
import { useAsignacionTareasStore } from '@d/admin/stores/asignacion-tareas-store'

export const useConsultores = () => {
  const columnQuery = ref('')
  const searchQuery = ref('')
  const openAsignar = ref(false)
  const selectedIndex = ref<number | null>(null)
  const debouncedSearchQuery = useDebounce(searchQuery, 150)
  const selectedConsultor = ref<Consultores | null>(null)

  const store = useAsignacionTareasStore()

  const {
    isFetching: isConsultoresPending,
    data: dataConsultores,
    refetch: refetchConsultores
  } = useQuery({
    queryKey: ['consultores-prtarea'],
    queryFn: () => getConsultores()
  })

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim()) return dataConsultores.value ?? []

    return (dataConsultores.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof Consultores]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  const onRowClick = (id: number) => store.setConsultorId(id)

  const resetConsultores = () => store.setConsultorId(null)

  return {
    columnQuery,
    filteredData,
    isConsultoresPending,
    onRowClick,
    selectedConsultor,
    openAsignar,
    refetchConsultores,
    resetConsultores,
    searchQuery,
    selectedIndex
  }
}
