import { getTareasProyecto } from '@d/admin/repository/consultor-tareas-repository'
import type { Tarea } from '@d/admin/models/tarea'
import { useQuery } from '@tanstack/vue-query'
import { useTareaConsultorStore } from '@d/admin/stores/tarea-consultor-store'

export const useTareas = () => {
  const columnQuery = ref('')
  const searchQuery = ref('')
  const debouncedSearchQuery = useDebounce(searchQuery, 150)
  const store = useTareaConsultorStore()
  const selectedIndex = ref<number | null>(null)

  const {
    isFetching: isTareasPending,
    data: dataTareas,
    refetch: refetchTareas
  } = useQuery({
    queryKey: ['tareasProyecto-consultor'],
    enabled: store.isProyecto,
    queryFn: () => getTareasProyecto(store.proyecto?.proyectoCorta ?? '')
  })

  watch(
    () => store.proyecto,
    proyecto => {
      if (proyecto !== null) {
        refetchTareas()
      } else {
        columnQuery.value = ''
        searchQuery.value = ''
      }
    }
  )

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim()) return dataTareas.value ?? []

    return (dataTareas.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof Tarea]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  const onRowClick = (tareaId: number) => store.setTareaId(tareaId)

  const resetTareas = () => store.setTareaId(null)

  return {
    onRowClick,
    resetTareas,
    filteredData,
    isTareasPending,
    refetchTareas,
    selectedIndex
  }
}
