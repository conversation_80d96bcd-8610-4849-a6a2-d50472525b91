import { getConsultoresPT } from '@d/admin/repository/consultor-tareas-repository'
import type { Consultores } from '@d/admin/models/consultores'
import { useQuery } from '@tanstack/vue-query'
import { useTareaConsultorStore } from '@d/admin/stores/tarea-consultor-store'

export const useConsultoresTarea = () => {
  const columnQuery = ref('')
  const searchQuery = ref('')
  const debouncedSearchQuery = useDebounce(searchQuery, 150)
  const store = useTareaConsultorStore()

  const {
    isFetching: isConsultoresTareaPending,
    data: dataConsultoresTarea,
    refetch: refetchConsultoresTarea
  } = useQuery({
    queryKey: ['consultoresTarea-consultor'],
    enabled: store.isTareaId,
    queryFn: () =>
      getConsultoresPT(
        store.proyecto?.proyectoId ?? 0,
        store.tareaId ?? 0
      ).then(data => {
        console.log(data)
        return data
      })
  })

  watch(
    () => store.tareaId,
    tareaId => {
      if (tareaId !== null) {
        refetchConsultoresTarea()
      } else {
        columnQuery.value = ''
        searchQuery.value = ''
      }
    }
  )

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim())
      return dataConsultoresTarea.value ?? []

    return (dataConsultoresTarea.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof Consultores]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  return {
    filteredData,
    isConsultoresTareaPending,
    refetchConsultoresTarea
  }
}
