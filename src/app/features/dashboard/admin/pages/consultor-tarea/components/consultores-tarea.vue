<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import { consultoresIdMetadata } from '@d/admin/models/consultores'
import { useTareaConsultorStore } from '@d/admin/stores/tarea-consultor-store'
import Table from '@d/common/components/table/Table.vue'
import TBody from '@d/common/components/table/Tbody.vue'
import Td from '@d/common/components/table/Td.vue'
import Th from '@d/common/components/table/Th.vue'
import Thead from '@d/common/components/table/Thead.vue'
import Tr from '@d/common/components/table/Tr.vue'

import Loading from '@/common/components/loading.vue'

import { useConsultoresTarea } from './consultores-tarea'

const { filteredData, isConsultoresTareaPending, refetchConsultoresTarea } =
  useConsultoresTarea()

const store = useTareaConsultorStore()
</script>

<template>
  <div v-if="store.isTareaId" class="intro-y box w-full">
    <div
      class="flex flex-wrap p-5 gap-3 border-b border-slate-200/60 justify-between"
    >
      <h2 class="text-lg font-medium">Consultores</h2>
      <div class="flex gap-3">
        <div class="flex flex-col sm:flex-row gap-3">
          <Button variant="dark" @click="refetchConsultoresTarea">
            <LucideRefreshCcw class="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
    <div
      v-if="isConsultoresTareaPending"
      class="flex flex-col items-center m-5 justify-end col-span-6 sm:col-span-3 xl:col-span-2"
    >
      <Loading icon="three-dots" />
    </div>
    <div
      v-else-if="
        !isConsultoresTareaPending && filteredData && filteredData.length > 0
      "
      class="p-5"
    >
      <div class="overflow-auto max-h-[360px]">
        <Table hover sm>
          <Thead variant="dark">
            <Tr>
              <Th
                v-for="row in consultoresIdMetadata"
                :key="row.key"
                class="whitespace-nowrap font-bold"
              >
                {{ row.label }}
              </Th>
            </Tr>
          </Thead>
          <TBody>
            <Tr v-for="el in filteredData" :key="el.id">
              <Td>{{ el.id }}</Td>
              <Td>{{ el.nombre }}</Td>
            </Tr>
          </TBody>
        </Table>
      </div>
    </div>
    <div
      v-else-if="
        !isConsultoresTareaPending && filteredData && filteredData.length === 0
      "
      class="p-10"
    >
      <h3 class="text-center font-bold text-xl">
        No hay datos disponibles para mostrar
      </h3>
    </div>
  </div>
</template>
