import {
  getListaTareasConsultorCorta,
  insertarTarea
} from '@d/admin/repository/consultor-tareas-repository'
import type { Tarea } from '@d/admin/models/tarea'
import { useAlertStore } from '@c/stores/alert-store'
import { useMutation, useQuery } from '@tanstack/vue-query'
import { useAsignacionTareasStore } from '@d/admin/stores/asignacion-tareas-store'

export const useTareasProyecto = () => {
  const columnQuery = ref('')
  const searchQuery = ref('')
  const debouncedSearchQuery = useDebounce(searchQuery, 150)
  const store = useAsignacionTareasStore()

  const alertStore = useAlertStore()

  const {
    isFetching: isTareasProyectoPending,
    data: dataTareasProyecto,
    refetch: refetchTareasProyecto
  } = useQuery({
    queryKey: ['listaTareasConsultorCorta'],
    enabled: store.isProyectoId,
    queryFn: () => getListaTareasConsultorCorta(store.proyectocorta ?? '')
  })

  const { mutate: insertTarea, isPending: isInsertingTarea } = useMutation({
    mutationFn: (obj: { consultor: number; tarea: number; proyecto: number }) =>
      insertarTarea(obj.consultor, obj.tarea, obj.proyecto),
    onSuccess: () => {
      alertStore.showSuccessAlert()
      refetchTareasProyecto()
    },
    onError: () => alertStore.showErrorAlert()
  })

  watch(
    () => store.proyectoId,
    proyectoId => {
      if (proyectoId !== null) {
        refetchTareasProyecto()
      } else {
        columnQuery.value = ''
        searchQuery.value = ''
      }
    }
  )

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim())
      return dataTareasProyecto.value ?? []

    return (dataTareasProyecto.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof Tarea]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  return {
    columnQuery,
    filteredData,
    insertTarea,
    isInsertingTarea,
    isTareasProyectoPending,
    refetchTareasProyecto,
    searchQuery
  }
}
