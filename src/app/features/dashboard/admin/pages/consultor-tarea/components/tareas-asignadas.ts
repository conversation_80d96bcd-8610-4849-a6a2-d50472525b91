import {
  getListaTareasConsultor,
  borrarTarea
} from '@d/admin/repository/consultor-tareas-repository'
import type { Tarea } from '@d/admin/models/tarea'
import { useAlertStore } from '@c/stores/alert-store'
import { useMutation, useQuery } from '@tanstack/vue-query'
import { useAsignacionTareasStore } from '@d/admin/stores/asignacion-tareas-store'

export const useTareasAsignadas = () => {
  const columnQuery = ref('')
  const searchQuery = ref('')
  const debouncedSearchQuery = useDebounce(searchQuery, 150)
  const selectedIndex = ref<number | null>(null)

  const alertStore = useAlertStore()
  const store = useAsignacionTareasStore()

  const {
    isFetching: isTareasAsignadasPending,
    data: dataTareasAsignadas,
    refetch: refetchTareasAsignadas
  } = useQuery({
    queryKey: ['getListaTareasConsultor'],
    enabled: store.isProyectoId,
    queryFn: () =>
      getListaTareasConsultor(store.consultorId ?? 0, store.proyectoId ?? 0)
  })

  const { mutate: deleteTarea, isPending: isDeletingTarea } = useMutation({
    mutationFn: (obj: { consultor: number; tarea: number }) =>
      borrarTarea(obj.consultor, obj.tarea),
    onSuccess: () => {
      alertStore.showSuccessAlert()
      refetchTareasAsignadas()
    },
    onError: () => alertStore.showErrorAlert()
  })

  watch(
    () => store.proyectoId,
    proyectoId => {
      if (proyectoId !== null) {
        refetchTareasAsignadas()
      } else {
        columnQuery.value = ''
        searchQuery.value = ''
      }
    }
  )

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim())
      return dataTareasAsignadas.value ?? []

    return (dataTareasAsignadas.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof Tarea]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  return {
    filteredData,
    isTareasAsignadasPending,
    deleteTarea,
    isDeletingTarea,
    selectedIndex,
    refetchTareasAsignadas
  }
}
