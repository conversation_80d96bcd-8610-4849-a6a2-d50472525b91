import { getProyectosConsultory } from '@d/admin/repository/consultor-tareas-repository'
import type { ConsultoresProyecto } from '@d/admin/models/consultores-proyecto'
import { useQuery } from '@tanstack/vue-query'
import { useAsignacionTareasStore } from '@d/admin/stores/asignacion-tareas-store'

export const useProyectoConsultor = () => {
  const columnQuery = ref('')
  const searchQuery = ref('')
  const debouncedSearchQuery = useDebounce(searchQuery, 150)
  const selectedIndex = ref<number | null>(null)
  const store = useAsignacionTareasStore()

  const {
    isFetching: isProyectoConsultorPending,
    data: dataProyectoConsultor,
    refetch: refetchProyectoConsultor
  } = useQuery({
    queryKey: ['proyectoConsultor-pr'],
    enabled: store.isConsultorId,
    queryFn: () => getProyectosConsultory(store.consultorId ?? 0)
  })

  watch(
    () => store.consultorId,
    consultores => {
      if (consultores !== null) {
        refetchProyectoConsultor()
      } else {
        columnQuery.value = ''
        searchQuery.value = ''
      }
    }
  )

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim())
      return dataProyectoConsultor.value ?? []

    return (dataProyectoConsultor.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof ConsultoresProyecto]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  const onRowClick = (
    proyectocorta: string,
    proyectonombre: string,
    proyectoid: number
  ) => {
    store.setProyectocorta(proyectocorta)
    store.setProyectonombre(proyectonombre)
    store.setProyectoId(proyectoid)
  }

  const reset = () => {
    store.setProyectocorta(null)
    store.setProyectoId(null)
  }

  return {
    filteredData,
    isProyectoConsultorPending,
    onRowClick,
    refetchProyectoConsultor,
    reset,
    selectedIndex
  }
}
