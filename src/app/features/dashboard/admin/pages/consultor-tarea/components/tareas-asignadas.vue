<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import { tareaAsignadasMetadata } from '@d/admin/models/tarea'
import Table from '@d/common/components/table/Table.vue'
import TBody from '@d/common/components/table/Tbody.vue'
import Td from '@d/common/components/table/Td.vue'
import Th from '@d/common/components/table/Th.vue'
import Thead from '@d/common/components/table/Thead.vue'
import Tr from '@d/common/components/table/Tr.vue'

import Loading from '@/common/components/loading.vue'
import { useAsignacionTareasStore } from '@d/admin/stores/asignacion-tareas-store'

import { useTareasAsignadas } from './tareas-asignadas'

const {
  filteredData,
  isTareasAsignadasPending,
  refetchTareasAsignadas,
  selectedIndex
} = useTareasAsignadas()

const store = useAsignacionTareasStore()
</script>

<template>
  <div v-if="store.isProyectoId" class="intro-y box">
    <div
      class="flex flex-wrap p-5 gap-3 border-b border-slate-200/60 justify-between"
    >
      <h2 class="text-lg font-medium">Tareas Asignadas</h2>
      <div class="flex gap-3">
        <div class="flex flex-col sm:flex-row gap-3">
          <Button variant="dark" @click="refetchTareasAsignadas">
            <LucideRefreshCcw class="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
    <div
      v-if="isTareasAsignadasPending"
      class="flex flex-col items-center m-5 justify-end col-span-6 sm:col-span-3 xl:col-span-2"
    >
      <Loading icon="three-dots" />
    </div>
    <div
      v-else-if="
        !isTareasAsignadasPending && filteredData && filteredData.length > 0
      "
      class="p-5"
    >
      <div class="overflow-auto max-h-[360px]">
        <Table hover sm>
          <Thead variant="dark">
            <Tr>
              <Th
                v-for="row in tareaAsignadasMetadata"
                :key="row.key"
                class="whitespace-nowrap font-bold"
              >
                {{ row.label }}
              </Th>
            </Tr>
          </Thead>
          <TBody>
            <Tr
              v-for="el in filteredData"
              :key="el.idtarea"
              :class="`dark:hover:bg-[#303761] hover:bg-[#dadef7] ${
                selectedIndex === el.idtarea
                  ? 'dark:bg-[#303761] bg-[#dadef7]'
                  : ''
              }`"
              @click="() => (selectedIndex = el.idtarea)"
            >
              <Td>{{ el.nombretarea }}</Td>
              <Td>{{ store.proyectonombre ?? '' }}</Td>
            </Tr>
          </TBody>
        </Table>
        <div class="flex flex-row w-full justify-center">
          <div class="mt-6 flex flex-row gap-3">
            <Button variant="primary" class="w-24 mr-1"> Borrar </Button>
          </div>
        </div>
      </div>
    </div>
    <div
      v-else-if="
        !isTareasAsignadasPending && filteredData && filteredData.length === 0
      "
      class="p-10"
    >
      <h3 class="text-center font-bold text-xl">
        No hay datos disponibles para mostrar
      </h3>
    </div>
  </div>
</template>
