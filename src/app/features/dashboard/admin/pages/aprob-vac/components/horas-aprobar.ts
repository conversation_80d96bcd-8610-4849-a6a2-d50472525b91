import {
  getDataHorasAprobar,
  putAprobarVac,
  putRechazarVac
} from '@d/admin/repository/aprob-vac-repository'
import { useAlertStore } from '@c/stores/alert-store'
import type { HorasVac } from '@d/admin/models/horas-vac'
import { useMutation, useQuery } from '@tanstack/vue-query'

export const useHorasAprobar = () => {
  const selectedRows = ref<HorasVac[]>([])
  const selectedRowsLength = computed(() => selectedRows.value.length)
  const columnQuery = ref('')
  const searchQuery = ref('')
  const debouncedSearchQuery = useDebounce(searchQuery, 150)

  const alertStore = useAlertStore()

  const {
    isFetching: isHorasAprobarPending,
    data: dataHorasAprobar,
    refetch: refetchHorasAprobar
  } = useQuery({
    queryKey: ['horas-aprobarvac'],
    queryFn: () => getDataHorasAprobar()
  })

  const { mutate: mutateAprobarVac, isPending: isAprobarVacPending } =
    useMutation({
      mutationFn: (id: number) => putAprobarVac(id),
      onSuccess: () => {
        alertStore.showSuccessAlert()
        refetchHorasAprobar()
      },
      onError: () => alertStore.showErrorAlert()
    })

  const { mutate: mutateRechazarVac, isPending: isRechazarVacPending } =
    useMutation({
      mutationFn: (id: number) => putRechazarVac(id),
      onSuccess: () => {
        alertStore.showSuccessAlert()
        refetchHorasAprobar()
      },
      onError: () => alertStore.showErrorAlert()
    })

  const handleCheckboxChange = (event: Event, tarea: HorasVac) => {
    const isChecked = (event.target as HTMLInputElement).checked
    if (isChecked) {
      selectedRows.value.push(tarea)
    } else {
      selectedRows.value = selectedRows.value.filter(
        item => item.idvac !== tarea.idvac
      )
    }
  }

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim()) return dataHorasAprobar.value ?? []

    return (dataHorasAprobar.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof HorasVac]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  return {
    columnQuery,
    dataHorasAprobar,
    filteredData,
    handleCheckboxChange,
    isAprobarVacPending,
    isHorasAprobarPending,
    isRechazarVacPending,
    mutateAprobarVac,
    mutateRechazarVac,
    refetchHorasAprobar,
    searchQuery,
    selectedRowsLength
  }
}
