import {
  getDataHorasRechazadas,
  putPendienteVac
} from '@d/admin/repository/aprob-vac-repository'
import type { HorasVac } from '@d/admin/models/horas-vac'
import { useAlertStore } from '@c/stores/alert-store'
import { useMutation, useQuery } from '@tanstack/vue-query'

export const useHorasRechazadas = () => {
  const selectedRows = ref<HorasVac[]>([])
  const selectedRowsLength = computed(() => selectedRows.value.length)
  const columnQuery = ref('')
  const searchQuery = ref('')
  const debouncedSearchQuery = useDebounce(searchQuery, 150)
  const alertStore = useAlertStore()

  const {
    isFetching: isHorasRechazadasPending,
    data: dataHorasRechazadas,
    refetch: refetchHorasRechazadas
  } = useQuery({
    queryKey: ['horas-rechazadasvac'],
    queryFn: () => getDataHorasRechazadas()
  })

  const { mutate: mutateRechazadasHoras, isPending: isRechazadasHorasPending } =
    useMutation({
      mutationFn: (id: number) => putPendienteVac(id),
      onSuccess: () => {
        alertStore.showSuccessAlert()
        refetchHorasRechazadas()
      },
      onError: () => alertStore.showErrorAlert()
    })

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim())
      return dataHorasRechazadas.value ?? []

    return (dataHorasRechazadas.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof HorasVac]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  return {
    columnQuery,
    dataHorasRechazadas,
    filteredData,
    mutateRechazadasHoras,
    isRechazadasHorasPending,
    isHorasRechazadasPending,
    refetchHorasRechazadas,
    searchQuery,
    selectedRowsLength
  }
}
