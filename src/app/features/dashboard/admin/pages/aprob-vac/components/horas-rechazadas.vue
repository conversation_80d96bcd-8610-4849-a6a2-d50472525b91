<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import FormInput from '@c/components/form/FormInput.vue'
import { horasVacMetadata } from '@d/admin/models/horas-vac'
import Table from '@d/common/components/table/Table.vue'
import TBody from '@d/common/components/table/Tbody.vue'
import Td from '@d/common/components/table/Td.vue'
import Th from '@d/common/components/table/Th.vue'
import Thead from '@d/common/components/table/Thead.vue'
import Tr from '@d/common/components/table/Tr.vue'

import FormSelect from '@/common/components/form/FormSelect.vue'
import Loading from '@/common/components/loading.vue'

import { useHorasRechazadas } from './horas-rechazadas'

const {
  columnQuery,
  dataHorasRechazadas,
  filteredData,
  isHorasRechazadasPending,
  refetchHorasRechazadas,
  searchQuery
} = useHorasRechazadas()
</script>

<template>
  <div class="intro-y box">
    <div
      class="flex flex-col items-center p-5 border-b sm:flex-row border-slate-200/60 justify-between"
    >
      <div class="flex flex-col sm:flex-row gap-3">
        <FormSelect formSelectSize="lg" class="w-22">
          <option value="" @click="columnQuery = ''">
            Seleccionar Columna
          </option>
          <option
            v-for="row in horasVacMetadata"
            :key="row.key"
            :value="row.key"
            @click="columnQuery = row.key"
          >
            {{ row.label }}
          </option>
        </FormSelect>
        <div class="relative text-slate-500 w-11/12">
          <FormInput
            v-model="searchQuery"
            placeholder="Buscar..."
            class="pr-10"
            :disabled="columnQuery === ''"
            type="text"
          />
          <LucideSearch
            class="absolute inset-y-0 right-0 w-4 h-4 my-auto mr-3"
          />
        </div>
      </div>

      <div class="flex flex-col sm:flex-row gap-3">
        <Button variant="dark" @click="refetchHorasRechazadas">
          <LucideRefreshCcw class="h-4 w-4" />
        </Button>
      </div>
    </div>
    <div
      v-if="isHorasRechazadasPending"
      class="flex flex-col items-center m-5 justify-end col-span-6 sm:col-span-3 xl:col-span-2"
    >
      <Loading icon="three-dots" />
    </div>
    <div
      v-else-if="
        !isHorasRechazadasPending &&
        dataHorasRechazadas &&
        filteredData.length > 0
      "
      class="p-5"
    >
      <div class="overflow-auto max-h-[360px]">
        <Table hover sm>
          <Thead variant="dark">
            <Tr>
              <Th
                v-for="row in horasVacMetadata"
                :key="row.key"
                class="whitespace-nowrap font-bold"
              >
                {{ row.label }}
              </Th>
            </Tr>
          </Thead>
          <TBody>
            <Tr
              v-for="el in filteredData"
              :key="el.idvac"
              class="dark:hover:bg-[#303761] hover:bg-[#dadef7]"
            >
              <Td>{{ el.nombrecompleto }}</Td>
              <Td>{{ el.diainicio }}</Td>
              <Td>{{ el.diafin }}</Td>
              <Td>{{ el.estado }}</Td>
            </Tr>
          </TBody>
        </Table>
      </div>
      <div class="flex flex-row w-full justify-center">
        <div class="mt-6 flex flex-row gap-3">
          <Button variant="primary" class="w-24 mr-1"> Pendiente </Button>
        </div>
      </div>
    </div>
    <div
      v-else-if="
        !isHorasRechazadasPending &&
        dataHorasRechazadas &&
        filteredData.length === 0
      "
      class="p-10"
    >
      <h3 class="text-center font-bold text-xl">
        No hay datos disponibles para mostrar
      </h3>
    </div>
  </div>
</template>
