import {
  getDataHorasAprobadas,
  putPendienteVac
} from '@d/admin/repository/aprob-vac-repository'
import { useAlertStore } from '@c/stores/alert-store'
import type { HorasVac } from '@d/admin/models/horas-vac'
import { useMutation, useQuery } from '@tanstack/vue-query'

export const useHorasAprobadas = () => {
  const selectedRows = ref<HorasVac[]>([])
  const selectedRowsLength = computed(() => selectedRows.value.length)
  const columnQuery = ref('')
  const searchQuery = ref('')
  const debouncedSearchQuery = useDebounce(searchQuery, 150)
  const alertStore = useAlertStore()

  const {
    isFetching: isHorasAprobadasPending,
    data: dataHorasAprobadas,
    refetch: refetchHorasAprobadas
  } = useQuery({
    queryKey: ['horas-aprobadasvac'],
    queryFn: () => getDataHorasAprobadas()
  })
  const { mutate: mutateAprobadasHoras, isPending: isAprobadasHorasPending } =
    useMutation({
      mutationFn: (id: number) => putPendienteVac(id),
      onSuccess: () => {
        alertStore.showSuccessAlert()
        refetchHorasAprobadas()
      },
      onError: () => alertStore.showErrorAlert()
    })

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim())
      return dataHorasAprobadas.value ?? []

    return (dataHorasAprobadas.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof HorasVac]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  return {
    columnQuery,
    dataHorasAprobadas,
    filteredData,
    isHorasAprobadasPending,
    mutateAprobadasHoras,
    isAprobadasHorasPending,
    searchQuery,
    selectedRowsLength,
    refetchHorasAprobadas
  }
}
