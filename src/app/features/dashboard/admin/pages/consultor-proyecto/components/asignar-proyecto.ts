import { getProyectos } from '@d/admin/repository/tareas-repository'
import type { Proyecto } from '@d/admin/models/proyecto'
import { useQuery } from '@tanstack/vue-query'

export const useAsignarProyecto = () => {
  const columnQuery = ref('')
  const searchQuery = ref('')
  const selectedProyecto = ref<Proyecto | null>(null)

  const isSelectedProyecto = computed(() => selectedProyecto.value !== null)

  const debouncedSearchQuery = useDebounce(searchQuery, 150)

  const {
    isFetching: isProyectosPending,
    data: proyectos,
    refetch: refetchProyectos
  } = useQuery({
    queryKey: ['asignar-proyecto'],
    queryFn: () => getProyectos()
  })

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim()) return proyectos.value ?? []

    return (proyectos.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof Proyecto]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  const onRowClick = (proyecto: Proyecto) => (selectedProyecto.value = proyecto)

  return {
    columnQuery,
    filteredData,
    isProyectosPending,
    isSelectedProyecto,
    onRowClick,
    refetchProyectos,
    searchQuery
  }
}
