import {
  getDataConsultores,
  insertarProyecto
} from '@d/admin/repository/consultor-proyecto-repository'
import type { Consultores } from '@d/admin/models/consultores'
import { useAlertStore } from '@c/stores/alert-store'
import { useMutation, useQuery } from '@tanstack/vue-query'
import { useConsultoresStore } from '@d/admin/stores/consultores-store'

export const useConsultores = () => {
  const columnQuery = ref('')
  const searchQuery = ref('')
  const openAsignar = ref(false)
  const onlyClickInButton = ref(false)
  const selectedIndex = ref<number | null>(null)
  const debouncedSearchQuery = useDebounce(searchQuery, 150)
  const selectedConsultor = ref<Consultores | null>(null)

  const store = useConsultoresStore()
  const alertStore = useAlertStore()

  const {
    isFetching: isConsultoresPending,
    data: dataConsultores,
    refetch: refetchConsultores
  } = useQuery({
    queryKey: ['consultores'],
    queryFn: () => getDataConsultores()
  })

  const { mutate: addProyecto, isPending: isAddingProyecto } = useMutation({
    mutationFn: (obj: { consultor: number; proyecto: number }) =>
      insertarProyecto(obj.consultor, obj.proyecto),
    onSuccess: () => {
      alertStore.showSuccessAlert()
      refetchConsultores()
    },
    onError: () => alertStore.showErrorAlert()
  })

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim()) return dataConsultores.value ?? []

    return (dataConsultores.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof Consultores]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  const onRowClick = (id: number) => {
    if (onlyClickInButton.value) return
    store.setConsultores(id)
  }

  const onAddClick = (consultor: Consultores) => {
    selectedConsultor.value = consultor
    openAsignar.value = true
  }

  const onCancelClick = () => {
    selectedConsultor.value = null
    openAsignar.value = false
  }

  const resetConsultores = () => store.setConsultores(null)

  return {
    columnQuery,
    filteredData,
    isConsultoresPending,
    addProyecto,
    isAddingProyecto,
    onAddClick,
    onlyClickInButton,
    onRowClick,
    onCancelClick,
    selectedConsultor,
    openAsignar,
    refetchConsultores,
    resetConsultores,
    searchQuery,
    selectedIndex
  }
}
