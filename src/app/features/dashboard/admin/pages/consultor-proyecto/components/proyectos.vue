<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import FormInput from '@c/components/form/FormInput.vue'
import { proyectoConsultorMetadata } from '@d/admin/models/proyecto'
import Table from '@d/common/components/table/Table.vue'
import TBody from '@d/common/components/table/Tbody.vue'
import Td from '@d/common/components/table/Td.vue'
import Th from '@d/common/components/table/Th.vue'
import Thead from '@d/common/components/table/Thead.vue'
import Tr from '@d/common/components/table/Tr.vue'

import FormSelect from '@/common/components/form/FormSelect.vue'
import Loading from '@/common/components/loading.vue'
import { useProyectoStore } from '@d/admin/stores/proyecto-store'

import { useProyectos } from './proyectos'

const {
  columnQuery,
  filteredData,
  isProyectosPending,
  onRowClick,
  refetchProyectos,
  resetProyecto,
  searchQuery,
  selectedIndex
} = useProyectos()

const store = useProyectoStore()
</script>

<template>
  <div :class="`intro-y box ${store.isProyectoId ? ' w-7/12' : 'w-full'}`">
    <div
      :class="`flex flex-warp p-5 gap-3 border-b border-slate-200/60 ${!store.isProyectoId ? 'justify-between' : ''}`"
    >
      <h2 class="text-lg font-medium">Proyectos</h2>
      <div class="flex gap-3">
        <div class="flex gap-3">
          <FormSelect formSelectSize="lg" class="w-10/12">
            <option value="" @click="columnQuery = ''">
              Seleccionar Columna
            </option>
            <option
              v-for="row in proyectoConsultorMetadata"
              :key="row.key"
              :value="row.key"
              @click="columnQuery = row.key"
            >
              {{ row.label }}
            </option>
          </FormSelect>
          <div class="relative text-slate-500 w-11/12">
            <FormInput
              v-model="searchQuery"
              placeholder="Buscar..."
              class="pr-10"
              :disabled="columnQuery === ''"
              type="text"
            />
            <LucideSearch
              class="absolute inset-y-0 right-0 w-4 h-4 my-auto mr-3"
            />
          </div>
        </div>
        <div class="flex flex-col sm:flex-row gap-3">
          <Button
            variant="dark"
            @click="
              () => {
                refetchProyectos()
                resetProyecto()
              }
            "
          >
            <LucideRefreshCcw class="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
    <div
      v-if="isProyectosPending"
      class="flex flex-col items-center m-5 justify-end col-span-6 sm:col-span-3 xl:col-span-2"
    >
      <Loading icon="three-dots" />
    </div>
    <div
      v-else-if="!isProyectosPending && filteredData && filteredData.length > 0"
      class="p-5"
    >
      <div class="overflow-auto max-h-[360px]">
        <Table hover sm>
          <Thead variant="dark">
            <Tr>
              <Th
                v-for="row in proyectoConsultorMetadata"
                :key="row.key"
                class="whitespace-nowrap font-bold"
              >
                {{ row.label }}
              </Th>
            </Tr>
          </Thead>
          <TBody>
            <Tr
              v-for="el in filteredData"
              :key="el.idproyecto"
              :class="`dark:hover:bg-[#303761] hover:bg-[#dadef7] ${
                selectedIndex === el.idproyecto
                  ? 'dark:bg-[#303761] bg-[#dadef7]'
                  : ''
              }`"
              @click="
                () => {
                  onRowClick(el.idproyecto)
                  selectedIndex = el.idproyecto
                }
              "
            >
              <Td>{{ el.clavecorta }}</Td>
              <Td>{{ el.nombreproyecto }}</Td>
              <Td>{{ el.pais }}</Td>
            </Tr>
          </TBody>
        </Table>
      </div>
    </div>
    <div
      v-else-if="
        !isProyectosPending && filteredData && filteredData.length === 0
      "
      class="p-10"
    >
      <h3 class="text-center font-bold text-xl">
        No hay datos disponibles para mostrar
      </h3>
    </div>
  </div>
</template>
