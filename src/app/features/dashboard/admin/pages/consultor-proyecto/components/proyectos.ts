import { getProyectos } from '@d/admin/repository/tareas-repository'
import type { Proyecto } from '@d/admin/models/proyecto'
import { useQuery } from '@tanstack/vue-query'
import { useProyectoStore } from '@d/admin/stores/proyecto-store'

export const useProyectos = () => {
  const columnQuery = ref('')
  const searchQuery = ref('')
  const debouncedSearchQuery = useDebounce(searchQuery, 150)
  const selectedIndex = ref<number | null>(null)

  const store = useProyectoStore()

  const {
    isFetching: isProyectosPending,
    data: proyectos,
    refetch: refetchProyectos
  } = useQuery({
    queryKey: ['proyecto-prconsultor'],
    queryFn: () => getProyectos()
  })

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim()) return proyectos.value ?? []

    return (proyectos.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof Proyecto]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  const onRowClick = (id: number) => store.setProyectoId(id)
  const resetProyecto = () => store.setProyectoId(null)

  return {
    columnQuery,
    filteredData,
    isProyectosPending,
    onRowClick,
    refetchProyectos,
    resetProyecto,
    searchQuery,
    selectedIndex
  }
}
