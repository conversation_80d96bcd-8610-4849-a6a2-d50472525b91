import {
  getConsultorByProyecto,
  borrarProyecto
} from '@d/admin/repository/consultor-proyecto-repository'
import type { Consultores } from '@d/admin/models/consultores'
import { useAlertStore } from '@c/stores/alert-store'
import { useMutation, useQuery } from '@tanstack/vue-query'
import { useProyectoStore } from '@d/admin/stores/proyecto-store'

export const useConsultorProyecto = () => {
  const columnQuery = ref('')
  const searchQuery = ref('')
  const debouncedSearchQuery = useDebounce(searchQuery, 150)
  const store = useProyectoStore()
  const alertStore = useAlertStore()

  const {
    isFetching: isConsultorProyectoPending,
    data: dataConsultorProyecto,
    refetch: refetchConsultorProyecto
  } = useQuery({
    queryKey: ['consultorProyecto-page'],
    enabled: store.isProyectoId,
    queryFn: () => getConsultorByProyecto(store.proyectoId ?? 1)
  })

  const { mutate: deleteProyecto, isPending: isDeletingProyecto } = useMutation(
    {
      mutationFn: (obj: {
        consultor: number
        proyecto: number
        salida: string
      }) => borrarProyecto(obj.consultor, obj.proyecto, obj.salida),
      onSuccess: () => {
        alertStore.showSuccessAlert()
        refetchConsultorProyecto()
      },
      onError: () => alertStore.showErrorAlert()
    }
  )

  watch(
    () => store.proyectoId,
    proyectoId => {
      if (proyectoId !== null) {
        refetchConsultorProyecto()
      } else {
        columnQuery.value = ''
        searchQuery.value = ''
      }
    }
  )

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim())
      return dataConsultorProyecto.value ?? []

    return (dataConsultorProyecto.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof Consultores]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  return {
    columnQuery,
    filteredData,
    deleteProyecto,
    isDeletingProyecto,
    isConsultorProyectoPending,
    refetchConsultorProyecto,
    searchQuery
  }
}
