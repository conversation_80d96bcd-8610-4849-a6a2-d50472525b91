<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import FormInput from '@c/components/form/FormInput.vue'
import SlideoverDescription from '@c/components/slideover/Description.vue'
import SlideoverPanel from '@c/components/slideover/Panel.vue'
import Slideover from '@c/components/slideover/Slideover.vue'
import type { Consultores } from '@d/admin/models/consultores'
import { asignarProyectoMetadata } from '@d/admin/models/proyecto'
import Table from '@d/common/components/table/Table.vue'
import TBody from '@d/common/components/table/Tbody.vue'
import Td from '@d/common/components/table/Td.vue'
import Th from '@d/common/components/table/Th.vue'
import Thead from '@d/common/components/table/Thead.vue'
import Tr from '@d/common/components/table/Tr.vue'

import FormSelect from '@/common/components/form/FormSelect.vue'
import Loading from '@/common/components/loading.vue'

import { useAsignarProyecto } from './asignar-proyecto'

// eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
const props = defineProps<{ consultor: Consultores | null; open: boolean }>()
defineEmits<{ close: () => void }>()

const {
  columnQuery,
  filteredData,
  isProyectosPending,
  isSelectedProyecto,
  onRowClick,
  refetchProyectos,
  searchQuery
} = useAsignarProyecto()
</script>

<template>
  <Slideover size="lg" :open="props.open" @close="$emit('close')">
    <SlideoverPanel>
      <SlideoverDescription
        class="px-8 pt-10 pb-8 flex flex-col justify-between"
      >
        <div class="flex flex-col">
          <div class="text-xl font-medium">
            Asignar Proyecto: {{ props.consultor?.nombre }}
            {{ props.consultor?.apellido }}
          </div>
          <div class="intro-y box">
            <div class="flex flex-warp p-5 gap-3 border-b border-slate-200/60">
              <div class="flex gap-3">
                <FormSelect formSelectSize="lg" class="w-10/12">
                  <option value="" @click="columnQuery = ''">
                    Seleccionar Columna
                  </option>
                  <option
                    v-for="row in asignarProyectoMetadata"
                    :key="row.key"
                    :value="row.key"
                    @click="columnQuery = row.key"
                  >
                    {{ row.label }}
                  </option>
                </FormSelect>
                <div class="relative text-slate-500 w-11/12">
                  <FormInput
                    v-model="searchQuery"
                    placeholder="Buscar..."
                    class="pr-10"
                    :disabled="columnQuery === ''"
                    type="text"
                  />
                  <LucideSearch
                    class="absolute inset-y-0 right-0 w-4 h-4 my-auto mr-3"
                  />
                </div>
              </div>
              <div class="flex flex-col sm:flex-row gap-3">
                <Button variant="dark" @click="refetchProyectos">
                  <LucideRefreshCcw class="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          <div
            v-if="isProyectosPending"
            class="flex flex-col items-center m-5 justify-end col-span-6 sm:col-span-3 xl:col-span-2"
          >
            <Loading icon="three-dots" />
          </div>
          <div
            v-else-if="
              !isProyectosPending && filteredData && filteredData.length > 0
            "
            class="p-5"
          >
            <div class="overflow-auto max-h-[360px]">
              <Table hover sm>
                <Thead variant="dark">
                  <Tr>
                    <Th
                      v-for="row in asignarProyectoMetadata"
                      :key="row.key"
                      class="whitespace-nowrap font-bold"
                    >
                      {{ row.label }}
                    </Th>
                  </Tr>
                </Thead>
                <TBody>
                  <Tr
                    v-for="el in filteredData"
                    :key="el.idproyecto"
                    @click="() => onRowClick(el)"
                  >
                    <Td>{{ el.idproyecto }}</Td>
                    <Td>{{ el.nombreproyecto }}</Td>
                    <Td>{{ el.cliente }}</Td>
                  </Tr>
                </TBody>
              </Table>
            </div>
          </div>
          <div
            v-else-if="
              !isProyectosPending && filteredData && filteredData.length === 0
            "
            class="p-10"
          >
            <h3 class="text-center font-bold text-xl">
              No hay datos disponibles para mostrar
            </h3>
          </div>
        </div>
        <div class="flex flex-row gap-3 justify-end">
          <Button
            variant="primary"
            :disabled="!isSelectedProyecto"
            class="w-24 mr-1"
          >
            Asignar
          </Button>
          <Button
            variant="danger"
            class="w-24 mr-1"
            @click="() => $emit('close')"
          >
            Cancelar
          </Button>
        </div>
      </SlideoverDescription>
    </SlideoverPanel>
  </Slideover>
</template>
