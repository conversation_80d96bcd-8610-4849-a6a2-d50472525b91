import { getProyectosByConsultor } from '@d/admin/repository/consultor-proyecto-repository'
import type { ConsultoresProyecto } from '@d/admin/models/consultores-proyecto'
import { useQuery } from '@tanstack/vue-query'
import { useConsultoresStore } from '@d/admin/stores/consultores-store'

export const useProyectoConsultor = () => {
  const columnQuery = ref('')
  const searchQuery = ref('')
  const debouncedSearchQuery = useDebounce(searchQuery, 150)
  const store = useConsultoresStore()

  const {
    isFetching: isProyectoConsultorPending,
    data: dataProyectoConsultor,
    refetch: refetchProyectoConsultor
  } = useQuery({
    queryKey: ['proyectoConsultor'],
    enabled: store.isConsultores,
    queryFn: () => getProyectosByConsultor(store.consultores ?? 0)
  })

  watch(
    () => store.consultores,
    consultores => {
      if (consultores !== null) {
        refetchProyectoConsultor()
      } else {
        columnQuery.value = ''
        searchQuery.value = ''
      }
    }
  )

  const filteredData = computed(() => {
    if (!debouncedSearchQuery.value.trim())
      return dataProyectoConsultor.value ?? []

    return (dataProyectoConsultor.value ?? []).filter(item => {
      const value = item[columnQuery.value as keyof ConsultoresProyecto]
      if (typeof value === 'string') {
        return value
          .toLowerCase()
          .includes(debouncedSearchQuery.value.toLowerCase())
      } else if (typeof value === 'number') {
        return value.toString().includes(debouncedSearchQuery.value)
      }
      return false
    })
  })

  return {
    columnQuery,
    filteredData,
    isProyectoConsultorPending,
    refetchProyectoConsultor,
    searchQuery
  }
}
