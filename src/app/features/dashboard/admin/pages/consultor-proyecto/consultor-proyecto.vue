<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup>
import DisclosureButton from '@d/common/components/disclosure/Button.vue'
import Disclosure from '@d/common/components/disclosure/Disclosure/Disclosure.vue'
import DisclosureGroup from '@d/common/components/disclosure/Group.vue'
import DisclosurePanel from '@d/common/components/disclosure/Panel.vue'

import ConsultorProyecto from './components/consultor-proyecto.vue'
import Consultores from './components/consultores.vue'
import ProyectoConsultor from './components/proyecto-consultor.vue'
import Proyectos from './components/proyectos.vue'
</script>

<template>
  <div>
    <div class="flex items-center mt-8 intro-y">
      <h2 class="mr-auto text-2xl font-medium">Consultor / Proyecto</h2>
    </div>
    <div class="grid grid-cols-12 gap-6 mt-5 intro-y">
      <div class="col-span-full">
        <DisclosureGroup class="mt-3">
          <Disclosure class="box my-3" index="0">
            <DisclosureButton
              class="px-4 flex flex-row justify-between"
              :openDisc="true"
            >
              <h4 class="mt-0.5 text-xl">Consultor - Proyecto</h4>
              <LucidePlus />
            </DisclosureButton>
            <DisclosurePanel class="flex flex-row justify-center gap-3">
              <Consultores />
              <ProyectoConsultor />
            </DisclosurePanel>
          </Disclosure>
          <Disclosure class="box my-3" index="0">
            <DisclosureButton
              class="px-4 flex flex-row justify-between"
              :openDisc="true"
            >
              <h4 class="mt-0.5 text-xl">Proyecto - Consultor</h4>
              <LucidePlus />
            </DisclosureButton>
            <DisclosurePanel class="flex flex-row justify-center gap-3">
              <Proyectos />
              <ConsultorProyecto />
            </DisclosurePanel>
          </Disclosure>
        </DisclosureGroup>
      </div>
    </div>
  </div>
</template>
