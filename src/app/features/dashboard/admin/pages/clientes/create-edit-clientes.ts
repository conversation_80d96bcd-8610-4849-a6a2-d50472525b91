import type { Cliente } from '@d/admin/models/clientes'
import { getPaises } from '@d/admin/repository/consultores-repository'
import { useQuery } from '@tanstack/vue-query'

export const useCreateEditClients = (selectedRow: Ref<Cliente | null>) => {
  const formReactive = reactive({ nombrecliente: '', pais: '' })

  const { data: dataPaises, isFetching: isPaisesPending } = useQuery({
    queryFn: getPaises,
    queryKey: ['consultores-paises']
  })

  watch(selectedRow, value => {
    if (value != null) {
      formReactive.nombrecliente = value.nombrecliente
      formReactive.pais = value.pais
    }
  })

  const onSubmit = (): Omit<Cliente, 'idcliente'> => ({
    nombrecliente: formReactive.nombrecliente,
    pais: formReactive.pais
  })

  return { dataPaises, formReactive, isPaisesPending, onSubmit }
}
