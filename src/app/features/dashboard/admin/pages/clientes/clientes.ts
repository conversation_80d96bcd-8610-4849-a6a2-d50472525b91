import {
  getClientes,
  createCliente,
  updateCliente,
  deleteCliente
} from '@d/admin/repository/clientes-repository'
import { useQuery, useMutation } from '@tanstack/vue-query'
import { useAlertStore } from '@c/stores/alert-store'
import type { Cliente } from '@d/admin/models/clientes'

export const useClientes = () => {
  const openDialog = ref(false)
  const alertStore = useAlertStore()
  const selectedRow = ref<Cliente | null>(null)

  const {
    data: dataClientes,
    isFetching: isClientesPending,
    refetch: refetchClientes
  } = useQuery({
    queryFn: getClientes,
    queryKey: ['admin-clientes']
  })

  const { mutate: mutateCreateCliente, isPending: isCreatingCliente } =
    useMutation({
      mutationFn: (sis: Omit<Cliente, 'idcliente'>) => createCliente(sis),
      onError: () => alertStore.showErrorAlert(),
      onSuccess: () => {
        alertStore.showSuccessAlert()
      }
    })

  const { mutate: mutateUpdateCliente, isPending: isUpdatingCliente } =
    useMutation({
      mutationFn: (input: Omit<Cliente, 'idcliente'>) =>
        updateCliente(selectedRow.value?.idcliente ?? 0, input),
      onError: () => alertStore.showErrorAlert(),
      onSuccess: () => {
        alertStore.showSuccessAlert()
      }
    })

  const { mutate: mutateDeleteCliente, isPending: isDeletingCliente } =
    useMutation({
      mutationFn: deleteCliente,
      onError: () => alertStore.showErrorAlert(),
      onSuccess: () => {
        alertStore.showSuccessAlert()
        refetchClientes()
      }
    })

  const onEdit = (row: Cliente) => {
    selectedRow.value = row
    setTimeout(() => (openDialog.value = true), 250)
  }

  const onSubmit = (res: Omit<Cliente, 'idcliente'>) => {
    if (selectedRow.value) {
      mutateUpdateCliente(res)
    } else {
      mutateCreateCliente(res)
    }
  }

  const loadingOperations = computed(
    () =>
      isClientesPending ||
      isCreatingCliente ||
      isUpdatingCliente ||
      isDeletingCliente
  )

  return {
    dataClientes,
    isClientesPending,
    isDeletingCliente,
    loadingOperations,
    mutateDeleteCliente,
    onEdit,
    onSubmit,
    openDialog,
    refetchClientes,
    selectedRow
  }
}
