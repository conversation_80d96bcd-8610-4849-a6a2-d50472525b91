<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-redundant-type-constituents -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import FormInput from '@c/components/form/FormInput.vue'
import SlideoverDescription from '@c/components/slideover/Description.vue'
import SlideoverPanel from '@c/components/slideover/Panel.vue'
import Slideover from '@c/components/slideover/Slideover.vue'
import type { Cliente } from '@d/admin/models/clientes'

import FormSelect from '@/common/components/form/FormSelect.vue'
import Loading from '@/common/components/loading.vue'

import { useCreateEditClients } from './create-edit-clientes'

const props = defineProps<{
  open: boolean
  selectedRow: Cliente | null
}>()

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'submit', value: Omit<Cliente, 'idcliente'>): void
}>()

const { selectedRow } = toRefs(props)

const { dataPaises, formReactive, isPaisesPending, onSubmit } =
  useCreateEditClients(selectedRow)
</script>

<template>
  <Slideover size="lg" :open="props.open" @close="$emit('close')">
    <SlideoverPanel>
      <SlideoverDescription
        class="px-8 pt-10 pb-8 flex flex-col justify-between"
      >
        <div class="flex flex-col">
          <div class="flex flex-row w-full justify-between">
            <div class="text-xl font-medium">
              {{
                props.selectedRow == null
                  ? 'Insertar Cliente:'
                  : 'Editar Cliente:'
              }}
            </div>
          </div>
          <div class="intro-y box w-full">
            <div
              v-if="isPaisesPending"
              class="flex flex-warp p-5 gap-3 w-full justify-center"
            >
              <Loading icon="puff" class="mx-auto" />
            </div>
            <div v-else class="flex flex-col p-5 gap-5">
              <div class="flex flex-row justify-between gap-3">
                <label class="cursor-pointer select-none mt-3"> Nombre: </label>
                <FormInput
                  v-model="formReactive.nombrecliente"
                  class="block px-4 py-3 w-8/12"
                  placeholder="Nombre"
                  label="Nombre"
                  type="text"
                />
              </div>
              <div class="flex flex-row justify-between gap-3">
                <label class="cursor-pointer select-none mt-1"> País: </label>
                <FormSelect v-model="formReactive.pais" class="w-8/12">
                  <option value="" @click="formReactive.pais = ''">
                    Seleccionar País
                  </option>
                  <option
                    v-for="rowPais in dataPaises"
                    :key="rowPais.idpais"
                    :value="rowPais.codpaiscorep.toString()"
                    @click="
                      () =>
                        (formReactive.pais = rowPais.codpaiscorep.toString())
                    "
                  >
                    {{ rowPais.codpaiscorep }} - &nbsp;
                    {{ rowPais.nombrepaissgt.trimEnd() }}
                  </option>
                </FormSelect>
              </div>
            </div>
          </div>
        </div>

        <div class="flex flex-row gap-3 justify-end">
          <Button
            variant="primary"
            class="w-24 mr-1"
            @click="
              () => {
                $emit('close')
                emit('submit', onSubmit())
              }
            "
          >
            Guardar
          </Button>
          <Button
            variant="danger"
            class="w-24 mr-1"
            @click="() => $emit('close')"
          >
            Cancelar
          </Button>
        </div>
      </SlideoverDescription>
    </SlideoverPanel>
  </Slideover>
</template>
