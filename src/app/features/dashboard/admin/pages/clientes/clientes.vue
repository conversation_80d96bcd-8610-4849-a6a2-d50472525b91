<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import { clientesMetadata } from '@d/admin/models/clientes'
import Table from '@d/common/components/table/Table.vue'
import TBody from '@d/common/components/table/Tbody.vue'
import Td from '@d/common/components/table/Td.vue'
import Th from '@d/common/components/table/Th.vue'
import Thead from '@d/common/components/table/Thead.vue'
import Tr from '@d/common/components/table/Tr.vue'

import Loading from '@/common/components/loading.vue'

import { useClientes } from './clientes'
import CreateEditClientes from './create-edit-clientes.vue'

const {
  dataClientes,
  isClientesPending,
  isDeletingCliente,
  loadingOperations,
  mutateDeleteCliente,
  onEdit,
  onSubmit,
  openDialog,
  refetchClientes,
  selectedRow
} = useClientes()
</script>

<template>
  <div>
    <CreateEditClientes
      :open="openDialog"
      :selected-row="selectedRow"
      @submit="onSubmit"
      @close="() => (openDialog = false)"
    />
    <div class="flex items-center mt-8 mb-3 intro-y">
      <h2 class="mr-auto text-2xl font-medium">Clientes</h2>
    </div>
    <div v-if="isClientesPending" class="box flex flex-col items-center py-4">
      <Loading icon="three-dots" />
    </div>
    <div v-else class="box">
      <div
        class="flex flex-col items-center p-5 border-b sm:flex-row border-slate-200/60 justify-between"
      >
        <div class="flex flex-col sm:flex-row gap-3">
          <Button
            class="w-28"
            variant="primary"
            @click="
              () => {
                selectedRow = null
                openDialog = true
              }
            "
          >
            Nuevo
          </Button>
        </div>
        <Button variant="dark" @click="refetchClientes">
          <LucideRefreshCcw class="h-4 w-4" />
        </Button>
      </div>
      <div v-if="dataClientes && dataClientes.length > 0" class="p-5">
        <div class="overflow-auto max-h-[69dvh]">
          <Table hover sm>
            <Thead variant="dark">
              <Tr>
                <Th
                  v-for="row in clientesMetadata"
                  :key="row.key"
                  class="whitespace-nowrap font-bold"
                >
                  {{ row.label }}
                </Th>
                <Th class="whitespace-nowrap font-bold">Acciones</Th>
              </Tr>
            </Thead>
            <TBody>
              <Tr
                v-for="el in dataClientes"
                :key="el.idcliente"
                class="dark:hover:bg-[#303761] hover:bg-[#dadef7]"
              >
                <Td>{{ el.idcliente }}</Td>
                <Td>{{ el.nombrecliente }}</Td>
                <Td>{{ el.pais }}</Td>
                <Td>
                  <div class="flex flex-row gap-3">
                    <Button variant="dark" @click="() => onEdit(el)">
                      <LucidePencil class="h-4 w-4" />
                    </Button>
                    <Button
                      variant="dark"
                      @click="() => mutateDeleteCliente(el.idcliente)"
                    >
                      <LucideEraser class="h-4 w-4" />
                    </Button>
                  </div>
                </Td>
              </Tr>
            </TBody>
          </Table>
        </div>
      </div>
      <div v-else-if="dataClientes && dataClientes.length === 0" class="p-10">
        <h3 class="text-center font-bold text-xl">
          No hay datos disponibles para mostrar
        </h3>
      </div>
    </div>
  </div>
</template>
