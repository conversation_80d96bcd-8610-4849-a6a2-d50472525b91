export const adminRoutes = [
  {
    path: '/dash/admin/aprob-wm',
    component: () => import('@d/admin/pages/aprob-wm/aprob-wm.vue'),
    meta: { breadcrumb: 'Aprobaciones Horas WM' }
  },
  {
    path: '/dash/admin/aprob-pm',
    component: () => import('@d/admin/pages/aprob-pm/aprob-pm.vue'),
    meta: { breadcrumb: 'Aprobaciones Horas PM' }
  },
  {
    path: '/dash/admin/aprob-vac',
    component: () => import('@d/admin/pages/aprob-vac/aprob-vac.vue'),
    meta: { breadcrumb: 'Aprobaciones Vacaciones' }
  },
  {
    path: '/dash/admin/tareas',
    component: () => import('@d/admin/pages/tareas/tareas.vue'),
    meta: { breadcrumb: 'Tareas' }
  },
  {
    path: '/dash/admin/consultor-proyecto',
    component: () =>
      import('@d/admin/pages/consultor-proyecto/consultor-proyecto.vue'),
    meta: { breadcrumb: 'Consultor/Proyecto' }
  },
  {
    path: '/dash/admin/consultor-tarea',
    component: () =>
      import('@d/admin/pages/consultor-tarea/consultor-tarea.vue'),
    meta: { breadcrumb: 'Consultor/Tarea' }
  },
  {
    path: '/dash/admin/consultores',
    component: () => import('@d/admin/pages/consultores/consultores.vue'),
    meta: { breadcrumb: 'Consultores' }
  },
  {
    path: '/dash/admin/clientes',
    component: () => import('@d/admin/pages/clientes/clientes.vue'),
    meta: { breadcrumb: 'Clientes' }
  },
  {
    path: '/dash/admin/proyectos',
    component: () => import('@d/admin/pages/proyectos/proyectos.vue'),
    meta: { breadcrumb: 'Proyectos' }
  },
  {
    path: '/dash/admin/calendario-festivos',
    component: () =>
      import('@d/admin/pages/calendario-festivos/calendario-festivos.vue'),
    meta: { breadcrumb: 'Calendario Festivos' }
  },
  {
    path: '/dash/admin/conf-vacaciones',
    component: () =>
      import('@d/admin/pages/conf-vacaciones/conf-vacaciones.vue'),
    meta: { breadcrumb: 'Configuración Vacaciones' }
  },
  {
    path: '/dash/admin/limites-horas',
    component: () => import('@d/admin/pages/limites-horas/limites-horas.vue'),
    meta: { breadcrumb: 'Límites Horas' }
  }
]
