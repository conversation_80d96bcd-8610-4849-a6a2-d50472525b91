import { type } from 'arktype'

export const horas = type({
  descripcion: 'string',
  etapa: 'string',
  fecha: 'string',
  horaextra: 'string',
  horastrabajadas: 'number',
  id: 'number',
  nombrecompleto: 'string',
  nombretarea: 'string',
  proyecto: 'string'
})

export const horasMetadata = [
  {
    key: 'fecha',
    label: 'Fecha'
  },
  {
    key: 'nombrecompleto',
    label: 'Nombre Completo'
  },
  {
    key: 'horastrabajadas',
    label: 'Horas Trabajadas'
  },
  {
    key: 'proyecto',
    label: 'Proyecto'
  },
  {
    key: 'nombretarea',
    label: 'Nombre Tarea'
  },
  {
    key: 'etapa',
    label: 'Etapa'
  },
  {
    key: 'descripcion',
    label: 'Descripción'
  }
]

export type Horas = typeof horas.infer
