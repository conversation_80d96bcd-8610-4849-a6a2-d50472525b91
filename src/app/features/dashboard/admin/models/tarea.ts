import { type } from 'arktype'

export const tarea = type({
  idtarea: 'number',
  clavecorta: 'string',
  nombretarea: 'string',
  clavecortaproyecto: 'string',
  estatus: 'string',
  horas: 'number?',
  fechainicio: 'string?',
  nombreproyecto: 'string?',
  fechafin: 'string?'
})

export const tareaMetadata = [
  {
    key: 'idtarea',
    label: 'ID'
  },
  {
    key: 'clavecorta',
    label: 'Clave Corta'
  },

  {
    key: 'clavecortaproyecto',
    label: 'Clave Corta Proyecto'
  },
  {
    key: 'estatus',
    label: 'Estatus'
  },
  {
    key: 'horas',
    label: 'Horas'
  },
  {
    key: 'fechainicio',
    label: 'Fecha Inicio'
  },
  {
    key: 'fechafin',
    label: 'Fecha Fin'
  }
]

export const tareaProyectoMetadata = [
  {
    key: 'nombretarea',
    label: 'Nombre Tarea'
  }
]

export const tareaAsignadasMetadata = [
  {
    key: 'nombretarea',
    label: 'Nombre Tarea'
  },
  {
    key: 'nombreproyecto',
    label: 'Nombre Proyecto'
  }
]

export const tareaProyectoConsultorMetadata = [
  {
    key: 'nombretarea',
    label: 'Nombre Tarea'
  },
  {
    key: 'clavecorta',
    label: 'Clave Corta'
  }
]

export type Tarea = typeof tarea.infer
