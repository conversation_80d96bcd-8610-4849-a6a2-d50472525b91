import { type } from 'arktype'

export const proyecto = type({
  idproyecto: 'number',
  clavecorta: 'string',
  nombreproyecto: 'string',
  pais: 'string',
  fechacreacion: 'string',
  jefede<PERSON>royecto: 'number',
  cliente: 'number | string',
  horasproy: 'number?',
  nombrecliente: 'string?',
  nombrejefe: 'string?',
  apellidojefe: 'string?',
  usuariojefe: 'string?',
  clientepais: 'string?'
})

export const proyectoAllMetadata = [
  {
    key: 'idproyecto',
    label: 'ID'
  },
  {
    key: 'clavecorta',
    label: 'Clave Corta'
  },
  {
    key: 'nombreproyecto',
    label: 'Proyecto'
  },
  {
    key: 'pais',
    label: 'País'
  },
  {
    key: 'fechacreacion',
    label: 'Fecha Creación'
  },
  {
    key: 'nombrejefe',
    label: 'Jefe Proyecto'
  },
  {
    key: 'nombrecliente',
    label: 'Cliente'
  },
  {
    key: 'clientepais',
    label: '<PERSON><PERSON>'
  },
  {
    key: 'horasproy',
    label: 'Horas Presentadas'
  }
]

export const proyectoMetadata = [
  {
    key: 'idproyecto',
    label: 'ID'
  },
  {
    key: 'clavecorta',
    label: 'Clave Corta'
  },
  {
    key: 'nombreproyecto',
    label: 'Proyecto'
  }
]

export const proyectoConsultorMetadata = [
  {
    key: 'clavecorta',
    label: 'Clave Corta'
  },
  {
    key: 'nombreproyecto',
    label: 'Proyecto'
  },
  {
    key: 'pais',
    label: 'País'
  }
]

export const asignarProyectoMetadata = [
  {
    key: 'id',
    label: 'ID'
  },
  {
    key: 'nombreproyecto',
    label: 'Proyecto'
  },
  {
    key: 'cliente',
    label: 'Cliente'
  }
]

export const accesosProyectoMetadata = [
  {
    key: 'clavecorta',
    label: 'Clave'
  },
  {
    key: 'nombreproyecto',
    label: 'Nombre'
  },
  {
    key: 'cliente',
    label: 'Cliente'
  }
]

export type Proyecto = typeof proyecto.infer
