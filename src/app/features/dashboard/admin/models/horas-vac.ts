import { type } from 'arktype'

export const horasVac = type({
  idvac: 'number',
  consultorid: 'number',
  diainicio: 'string',
  diafin: 'string',
  estado: 'string',
  nombrecompleto: 'string'
})

export const horasVacMetadata = [
  {
    key: 'nombrecompleto',
    label: 'Nombre Completo'
  },
  {
    key: 'diainicio',
    label: 'Día Inicio'
  },
  {
    key: 'diafin',
    label: 'Día Fin'
  },
  {
    key: 'estado',
    label: 'Estado'
  }
]

export const vacMetadata = [
  {
    key: 'diainicio',
    label: 'Día Inicio'
  },
  {
    key: 'diafin',
    label: 'Día Fin'
  },
  {
    key: 'estado',
    label: 'Estado'
  }
]

export type HorasVac = typeof horasVac.infer
