import { type } from 'arktype'

export const consultoresProyecto = type({
  idproyecto: 'number',
  clavecorta: 'string',
  nombreproyecto: 'string',
  pais: 'string',
  fechacreacion: 'string',
  jefedeproyecto: 'number',
  cliente: 'number',
  horasproy: 'number?'
})

export const consultoresProyectoMetadata = [
  {
    key: 'clavecorta',
    label: 'Clave Corta'
  },
  {
    key: 'nombreproyecto',
    label: 'Nombre Proyecto'
  }
]

export type ConsultoresProyecto = typeof consultoresProyecto.infer
