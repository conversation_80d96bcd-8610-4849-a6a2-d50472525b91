import { type } from 'arktype'

export const consultores = type({
  id: 'number',
  consultorid: 'number?',
  nombre: 'string',
  apellido: 'string',
  direccion: 'string',
  pais: 'string',
  jefefuncional: 'number',
  usuario: 'string',
  passwd: 'string',
  role: '"admin" | "wpm" | "pm" | "user" | "cese" | "wm"',
  fechaingreso: 'string',
  esjefefunc: '"S" | "N"',
  esjefe<PERSON>roy: '"S" | "N"',
  vacdispperantnat: 'number?',
  vacdispperantabil: 'number?',
  vacdispanoantnat: 'number?',
  vacdispanoantabil: 'number?',
  vacdispperactnat: 'number?',
  vacdispperactabil: 'number?',
  vacdispanoactnat: 'number?',
  vacdispanoactabil: 'number?',
  observac: 'string?',
  email: 'string?',
  jefefuncionaluser: 'string?'
})

export const consultoresMetadata = [
  {
    key: 'id',
    label: 'ID'
  },
  {
    key: 'nombre',
    label: 'Nombre'
  },
  {
    key: 'apellido',
    label: 'Apellido'
  }
]

export const consultoresAllMetadata = [
  {
    key: 'id',
    label: 'ID'
  },
  {
    key: 'nombre',
    label: 'Nombre'
  },
  {
    key: 'apellido',
    label: 'Apellido'
  },
  {
    key: 'email',
    label: 'Email'
  },
  {
    key: 'role',
    label: 'Role'
  },
  {
    key: 'fechaingreso',
    label: 'Fecha de Ingreso'
  }
]

export const consultoresProyectoMetadata = [
  {
    key: 'nombre',
    label: 'Nombre'
  },
  {
    key: 'apellido',
    label: 'Apellido'
  }
]

export const consultoresIdMetadata = [
  {
    key: 'id',
    label: 'ID'
  },
  {
    key: 'nombre',
    label: 'Nombre'
  }
]

export const consultorCambioContrasenaMetadata = [
  {
    key: 'id',
    label: 'ID'
  },
  {
    key: 'nombre',
    label: 'Nombre'
  },
  {
    key: 'apellido',
    label: 'Apellido'
  },
  {
    key: 'pais',
    label: 'País'
  },
  {
    key: 'usuario',
    label: 'Usuario'
  }
]

export type Consultores = typeof consultores.infer
