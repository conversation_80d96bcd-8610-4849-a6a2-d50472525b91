import type { Tarea } from '@d/admin/models/tarea'
import type { Consultores } from '@d/admin/models/consultores'
import type { Proyecto } from '@d/admin/models/proyecto'

const route = '/admin/consultor-tareas'

export const getConsultores = (id = 4) =>
  $fetch<Consultores[]>(`${route}/consultores`, {
    params: { id }
  })

export const getProyectosConsultory = (consultorId: number, id = 4) =>
  $fetch<Proyecto[]>(`${route}/proyectos-consultory`, {
    params: { id, consultorId }
  })

export const getListaTareasConsultorCorta = (proyectoCorta: string) =>
  $fetch<Tarea[]>(`${route}/lista-tareas-consultor-corta`, {
    params: { proyectoCorta }
  })

export const getListaTareasConsultor = (
  consultorId: number,
  proyectoId: number
) =>
  $fetch<Tarea[]>(`${route}/lista-tareas-consultor`, {
    params: { consultorId, proyectoId }
  })

export const getTareasProyecto = (proyectoCorta: string) =>
  $fetch<Tarea[]>(`${route}/tarea-proyecto`, {
    params: { proyectoCorta }
  })

export const getConsultoresPT = (proyectoId: number, tareaId: number) =>
  $fetch<Consultores[]>(`${route}/lista-consutores-pt`, {
    params: { proyectoId, tareaId }
  })

export const insertarTarea = (
  consultor: number,
  tarea: number,
  proyecto: number
) =>
  $fetch(`${route}/insertar-tareas`, {
    method: 'PUT',
    body: { consultor, tarea, proyecto }
  })

export const borrarTarea = (consultor: number, tarea: number) =>
  $fetch(`${route}/borrar-tarea`, {
    method: 'PUT',
    body: { consultor, tarea }
  })
