import type { HorasVac } from '@d/admin/models/horas-vac'

const route = '/admin/aprob-vac'

export const getDataHorasAprobar = (id = 4) =>
  $fetch<HorasVac[]>(`${route}/horas-aprobar`, {
    params: { id }
  })

export const putAprobarVac = (id: number) =>
  $fetch(`${route}/aprobar-vac`, {
    method: 'PUT',
    params: { id }
  })

export const putRechazarVac = (id: number) =>
  $fetch(`${route}/rechazar-vac`, {
    method: 'PUT',
    params: { id }
  })

export const putPendienteVac = (id: number) =>
  $fetch(`${route}/pendiente-vac`, {
    method: 'PUT',
    params: { id }
  })

export const getDataHorasAprobadas = (id = 4) =>
  $fetch<HorasVac[]>(`${route}/horas-aprobadas`, {
    params: { id }
  })

export const getDataHorasRechazadas = (id = 4) =>
  $fetch<HorasVac[]>(`${route}/horas-rechazadas`, {
    params: { id }
  })
