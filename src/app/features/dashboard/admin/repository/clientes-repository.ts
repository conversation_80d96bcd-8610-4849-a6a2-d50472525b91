import type { Cliente } from '@d/admin/models/clientes'

const route = '/admin/clientes/clientes'

export const getClientes = () => $fetch<Cliente[]>(route)

export const createCliente = (data: Omit<Cliente, 'idcliente'>) =>
  $fetch(route, { method: 'POST', body: data })

export const updateCliente = (id: number, data: Partial<Cliente>) =>
  $fetch(route, { method: 'PUT', body: data, params: { id } })

export const deleteCliente = (id: number) =>
  $fetch(route, { method: 'DELETE', params: { id } })
