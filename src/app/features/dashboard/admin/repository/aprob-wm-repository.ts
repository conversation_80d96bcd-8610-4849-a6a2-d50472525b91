import type { Horas } from '@d/admin/models/horas'

const route = '/admin/aprob-wm'

export const getDataHorasAprobar = (id = 4) =>
  $fetch<Horas[]>(`${route}/horas-aprobar`, {
    params: { id }
  })

export const putAprobarHoras = (id: number) =>
  $fetch(`${route}/aprobar-hora`, {
    method: 'PUT',
    params: { id }
  })

export const putPendienteHoras = (id: number) =>
  $fetch(`${route}/pendiente-hora`, {
    method: 'PUT',
    params: { id }
  })

export const putRechazarHoras = (id: number, comentario: string) =>
  $fetch(`${route}/rechazar-hora`, {
    method: 'PUT',
    params: { id, comentario }
  })

export const getDataHorasAprobadas = (id = 4) =>
  $fetch<Horas[]>(`${route}/horas-aprobadas`, {
    params: { id }
  })

export const getDataHorasRechazadas = (id = 4) =>
  $fetch<Horas[]>(`${route}/horas-rechazadas`, {
    params: { id }
  })
