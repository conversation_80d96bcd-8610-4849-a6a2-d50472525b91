import type { Proyecto } from '@d/admin/models/proyecto'

const route = '/admin/proyectos/proyectos'

export const getProyectos = () => $fetch<Proyecto[]>(route)

export const createProyecto = (data: Omit<Proyecto, 'idproyecto'>) =>
  $fetch(route, { method: 'POST', body: data })

export const updateProyecto = (id: number, data: Partial<Proyecto>) =>
  $fetch(route, { method: 'PUT', body: data, params: { id } })

export const deleteProyecto = (id: number) =>
  $fetch(route, { method: 'DELETE', params: { id } })
