import type { Consultores } from '@d/admin/models/consultores'

const route = '/admin/consultores/consultores'
const initRoute = '/admin/consultores/consultor-init'
const paisRoute = '/admin/consultores/paises'
const jefeFuncRoute = '/admin/consultores/jefe-func'

export const getConsultores = () => $fetch<Consultores[]>(route)

export const getPaises = () =>
  $fetch<
    {
      idpais: number
      codpaissgt: number
      nombrepaissgt: string
      codpaiscorep: string
    }[]
  >(paisRoute)

export const consultorJefesFunc = () => $fetch<Consultores[]>(jefeFuncRoute)

export const createConsultor = (data: Omit<Consultores, 'id'>) =>
  $fetch(route, { method: 'POST', body: data })

export const updateConsultor = (id: number, data: Partial<Consultores>) =>
  $fetch(route, { method: 'PUT', body: data, params: { id } })

export const consultorInit = (id: number) =>
  $fetch(initRoute, { method: 'PUT', params: { id } })

export const deleteConsultor = (id: number) =>
  $fetch(route, { method: 'DELETE', params: { id } })
