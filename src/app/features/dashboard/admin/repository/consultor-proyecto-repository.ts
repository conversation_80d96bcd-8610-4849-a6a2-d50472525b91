import type { Consultores } from '@d/admin/models/consultores'
import type { ConsultoresProyecto } from '@d/admin/models/consultores-proyecto'

const route = '/admin/consultor-proyecto'

export const getDataConsultores = () =>
  $fetch<Consultores[]>(`${route}/consultores`)

export const getProyectosByConsultor = (id: number) =>
  $fetch<ConsultoresProyecto[]>(`${route}/proyectos-consultor`, {
    params: { id }
  })

export const getConsultorByProyecto = (id: number) =>
  $fetch<Consultores[]>(`${route}/consultor-proyecto`, {
    params: { id }
  })

export const insertarProyecto = (consultor: number, proyecto: number) =>
  $fetch(`${route}/insertar-proyecto`, {
    method: 'PUT',
    body: { consultor, proyecto }
  })

export const borrarProyecto = (
  consultor: number,
  proyecto: number,
  salida: string
) =>
  $fetch(`${route}/borrar-proyecto`, {
    method: 'PUT',
    body: { consultor, proyecto, salida }
  })
