import type { Tarea } from '@d/admin/models/tarea'
import type { Proyecto } from '@d/admin/models/proyecto'

const route = '/admin/tareas'

export const getProyectos = () => $fetch<Proyecto[]>(`${route}/proyectos`)

export const getTareaByProyecto = (proyecto: string) =>
  $fetch<Tarea[]>(`${route}/tareas-proyecto`, {
    params: { proyecto }
  })

export const insertTarea = (
  estatus: string,
  horas: number,
  fechainicio: string,
  fechafin: string,
  clavecorta: string
) =>
  $fetch(`${route}/insert-tarea`, {
    method: 'POST',
    body: {
      estatus,
      horas,
      fechainicio,
      fechafin,
      clavecorta
    }
  })

export const updateTarea = (
  estatus: string,
  horas: number,
  fechainicio: string,
  fechafin: string,
  clavecorta: string
) =>
  $fetch(`${route}/update-tarea`, {
    method: 'PUT',
    body: {
      estatus,
      horas,
      fechainicio,
      fechafin,
      clavecorta
    }
  })

export const getTareasProyecto = (proyecto: string) =>
  $fetch<Tarea[]>(`${route}/tareas-proyecto`, {
    params: { proyecto }
  })

export const getTareasConsultor = (proyecto: string, tarea: string) =>
  $fetch<Tarea[]>(`${route}/tareas-consultor`, {
    params: { proyecto, tarea }
  })
