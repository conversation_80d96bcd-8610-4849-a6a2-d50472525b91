import type { Legislab } from '@d/admin/models/legislab'

const legislabRoute = '/admin/conf-vacaciones/legislab'
const confVacRoute = '/admin/conf-vacaciones/conf-vacaciones'

export const getConfVacaciones = (id: number) =>
  $fetch<
    {
      consultorid: number
      vacinic: number
      legislacion: string
      diaspermisoadic: number
      vacnogastadas: number
    }[]
  >(confVacRoute, { params: { id } })

export const updateConfVacaciones = (data: {
  consultorid: number
  vacinic: number
  legislacion: string
  diaspermisoadic: number
  vacnogastadas: number
}) => $fetch(confVacRoute, { method: 'PUT', body: data })

export const getLegislab = () => $fetch<Legislab[]>(legislabRoute)

export const createLegislab = (data: Legislab) =>
  $fetch(legislabRoute, { method: 'POST', body: data })

export const updateLegislab = (legislacion: string, data: Partial<Legislab>) =>
  $fetch(legislabRoute, {
    method: 'PUT',
    body: data,
    params: { legislacion }
  })

export const deleteLegislab = (legislacion: string) =>
  $fetch(legislabRoute, { method: 'DELETE', params: { legislacion } })
