import type { CalendarioFestivos } from '@d/admin/models/calendario-festivos'

const route = '/admin/calendario-festivos/calendario-festivos'

export const getCalendarioFestivos = () => $fetch<CalendarioFestivos[]>(route)

export const createCalendarioFestivos = (
  data: Omit<CalendarioFestivos, 'id'>
) => $fetch(route, { method: 'POST', body: data })

export const updateCalendarioFestivos = (
  id: number,
  data: Partial<CalendarioFestivos>
) => $fetch(route, { method: 'PUT', body: data, params: { id } })

export const deleteCalendarioFestivos = (id: number) =>
  $fetch(route, { method: 'DELETE', params: { id } })
