<!-- eslint-disable @typescript-eslint/no-redundant-type-constituents -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import FormInput from '@c/components/form/FormInput.vue'
import { useAlertStore } from '@c/stores/alert-store'
import { consultorCambioContrasenaMetadata } from '@d/admin/models/consultores'
import Table from '@d/common/components/table/Table.vue'
import TBody from '@d/common/components/table/Tbody.vue'
import Td from '@d/common/components/table/Td.vue'
import Th from '@d/common/components/table/Th.vue'
import Thead from '@d/common/components/table/Thead.vue'
import Tr from '@d/common/components/table/Tr.vue'
import {
  getConsultor,
  updateContrasena
} from '@d/home/<USER>/cambio-contrasena-repository'
import { useMutation, useQuery } from '@tanstack/vue-query'

import Loading from '@/common/components/loading.vue'
import { useLoginStore } from '@/common/stores/login-store'

const loginStore = useLoginStore()
const alertStore = useAlertStore()

const userModel = ref('')
const passwordModel = ref('')

const { data: dataConsultor, isFetching: isConsultorPending } = useQuery({
  queryFn: () =>
    getConsultor(loginStore.user?.id ?? 4).then(res => {
      userModel.value = res?.[0]?.usuario ?? ''
      return res
    }),
  queryKey: ['consultor-cambio-contrasena']
})

const {
  isPending: isMutateCambioContrasenaPending,
  mutate: mutateCambioContrasena
} = useMutation({
  mutationFn: (passwd: string) =>
    updateContrasena(passwd, loginStore.user?.id ?? 4),
  onError: () => alertStore.showErrorAlert(),
  onSuccess: () => alertStore.showSuccessAlert()
})
</script>

<template>
  <div>
    <div class="flex items-center mt-8 mb-3 intro-y">
      <h2 class="mr-auto text-2xl font-medium">Cambio de Contraseña</h2>
    </div>
    <div class="intro-y box h-full flex flex-col">
      <div
        v-if="isConsultorPending"
        class="flex flex-col items-center m-5 justify-end col-span-6 sm:col-span-3 xl:col-span-2"
      >
        <Loading icon="three-dots" />
      </div>
      <div v-else class="p-5">
        <div class="overflow-auto max-h-[360px]">
          <Table hover sm>
            <Thead variant="dark">
              <Tr>
                <Th
                  v-for="row in consultorCambioContrasenaMetadata"
                  :key="row.key"
                  class="whitespace-nowrap font-bold"
                >
                  {{ row.label }}
                </Th>
              </Tr>
            </Thead>
            <TBody>
              <Tr
                v-for="(el, index) in dataConsultor"
                :key="`${el.nombre} - ${index}`"
                class="dark:hover:bg-[#303761] hover:bg-[#dadef7]"
              >
                <Td>{{ el.id }}</Td>
                <Td>{{ el.nombre }}</Td>
                <Td>{{ el.apellido }}</Td>
                <Td>{{ el.pais }}</Td>
                <Td>{{ el.usuario }}</Td>
              </Tr>
            </TBody>
          </Table>
        </div>
        <div class="flex flex-row w-full justify-center">
          <div class="mt-6 flex flex-col gap-6 items-center">
            <div class="flex flex-row gap-8 mx-24">
              <p>Usuario:</p>
              <FormInput
                v-model="userModel"
                class="pr-10"
                readonly
                type="text"
              />
            </div>
            <div class="flex flex-row gap-8 mx-24">
              <p>Nueva Clave:</p>
              <FormInput
                v-model="passwordModel"
                placeholder="Ingrese nueva clave"
                class="pr-10"
                type="password"
              />
            </div>
            <Button
              class="w-36"
              variant="primary"
              :disabled="passwordModel === ''"
            >
              <LucideSave class="w-4 h-4" /> Guardar Clave
            </Button>
            <div class="flex flex-col gap-2">
              <p class="text-danger">* Formato de la Clave:</p>
              <p>
                De 8 a 15 caracteres. Al menos una minúscula, una mayúscula y un
                número. No se aceptan blancos
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
