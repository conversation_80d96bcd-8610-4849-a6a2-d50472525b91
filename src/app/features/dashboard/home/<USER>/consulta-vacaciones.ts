import { type } from 'arktype'

export const consultaVacaciones = type({
  fechainforme: 'string',
  consultorid: 'number',
  pais: 'string',
  fechaingreso: 'string',
  derechoconsolidado: 'number',
  diasvacdisfmesant: 'number',
  diasvacdisfmesact: 'number',
  diaspermisoadic: 'number',
  diasdisponibles: 'number',
  diasper: 'number',
  diasdevengados: 'number',
  vacnogastadas: 'number'
})

export type ConsultaVacaciones = typeof consultaVacaciones.infer
