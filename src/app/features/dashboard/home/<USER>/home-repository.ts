import type {
  HorasTrabajadas,
  InsertHorasTrabajadas,
  UpdateHorasTrabajadasOnly,
  UpdateHorasTrabajadas
} from '@d/home/<USER>/horas-trabajadas'
import type { Etapas } from '@d/home/<USER>/etapas'
import type { TareasConsultorAprob } from '@d/home/<USER>/tareas-consultor-aprob'

const route = '/home/<USER>'

export const getEtapas = () => $fetch<Etapas[]>(`${route}/etapas`)

export const getProyectosConsultor = (id: number) =>
  $fetch<{ clavecorta: string; nombreproyecto: string }[]>(
    `${route}/proyectos-consultor`,
    {
      params: { id }
    }
  )

export const getTareasConsultorAprob = (
  consultorId: number,
  projClaveCorta: string
) =>
  $fetch<TareasConsultorAprob[]>(`${route}/tareas-consultor-aprob`, {
    params: { consultorId, projClaveCorta }
  })

export const getHorasTrabajadas = (fecha: string, id: number) =>
  $fetch<HorasTrabajadas[]>(`${route}/horas-trabajadas`, {
    params: { fecha, id }
  })

export const getHorasTrabajadasCheck = (
  fechaInicio: string,
  fechaFin: string,
  id: number,
  proyecto: string,
  etapa: string,
  tarea: string
) =>
  $fetch<UpdateHorasTrabajadasOnly[]>(`${route}/horas-trabajadas-check`, {
    params: { fechaInicio, fechaFin, id, proyecto, etapa, tarea }
  })

export const insertHorasTrabajadas = (data: InsertHorasTrabajadas) =>
  $fetch(`${route}/horas-trabajadas`, { method: 'POST', body: data })

export const updateHorasTrabajadasOnly = (data: UpdateHorasTrabajadasOnly) =>
  $fetch(`${route}/horas-trabajadas-only`, { method: 'PUT', body: data })

export const updateHorasTrabajadas = (data: UpdateHorasTrabajadas) =>
  $fetch(`${route}/horas-trabajadas`, { method: 'PUT', body: data })

export const deleteHorasTrabajadas = ({
  id1,
  id2,
  id3,
  id4,
  id5
}: {
  id1?: number
  id2?: number
  id3?: number
  id4?: number
  id5?: number
}) =>
  $fetch(`${route}/horas-trabajadas`, {
    method: 'DELETE',
    params: { id1, id2, id3, id4, id5 }
  })
