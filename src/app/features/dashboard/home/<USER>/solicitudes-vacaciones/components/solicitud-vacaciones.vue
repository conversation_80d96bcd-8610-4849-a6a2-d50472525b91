<!-- eslint-disable sonarjs/no-selector-parameter -->
<!-- eslint-disable @typescript-eslint/no-unnecessary-condition -->
<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import VButton from '@c/components/button/button.vue'
import { horasVac, vacMetadata } from '@d/admin/models/horas-vac'
import VDialog from '@d/common/components/dialog/Dialog.vue'
import DialogPanel from '@d/common/components/dialog/Panel.vue'
import GenericTable from '@d/common/components/generic-table/generic-table.vue'
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'
import Fetching from '@d/common/components/reuse/fetching.vue'
import NewRefresh from '@d/common/components/reuse/new-refresh.vue'
import Slide from '@d/common/components/reuse/slide.vue'
import { useAlertMutate } from '@d/common/composables/use-alert-mutate'
import {
  deleteSolicitudVacaciones,
  getSolicitudVacaciones,
  insertSolicitudVacaciones,
  updateSolicitudVacaciones
} from '@d/home/<USER>/solicitudes-vacaciones-repository'
import { useQuery } from '@tanstack/vue-query'

import { useLoginStore } from '@/common/stores/login-store'

const { $dayjs } = useNuxtApp()
const loginStore = useLoginStore()
const openDialog = ref(false)
const selectedRow = ref<{ fechaFin: string; fechaInicio: string } | null>(null)
const fechaInicio = ref('')
const fechaFin = ref('')

const inicioDateToday = computed(() =>
  fechaInicio.value === '' ? $dayjs().format('YYYY-MM-DD') : fechaInicio.value
)
const finDateToday = computed(() =>
  fechaFin.value === '' ? $dayjs().format('YYYY-MM-DD') : fechaFin.value
)

const dateOverflowModal = ref(false)

const {
  data: dataSolicitudVacaciones,
  isFetching: isSolicitudVacacionesPending,
  refetch: refetchSolicitudVacaciones
} = useQuery({
  queryFn: () => getSolicitudVacaciones(loginStore.user?.id ?? 4),
  queryKey: ['solicitud-vacaciones']
})

const { isPending: isInsertVacacionPending, mutate: mutateInsertVacacion } =
  useAlertMutate({
    mutationFn: (data: { fechaFin: string; fechaInicio: string }) =>
      insertSolicitudVacaciones({
        consultorId: loginStore.user?.id ?? 4,
        fechaFin: data.fechaFin,
        fechaInicio: data.fechaInicio
      }),
    refetchSecondary: refetchSolicitudVacaciones
  })

const { isPending: isUpdateVacPending, mutate: mutateUpdateVac } =
  useAlertMutate({
    mutationFn: (data: { fechaFin: string; fechaInicio: string }) =>
      updateSolicitudVacaciones({
        consultorId: loginStore.user?.id ?? 4,
        fechaFin: data.fechaFin,
        fechaInicio: data.fechaInicio
      }),
    refetchSecondary: refetchSolicitudVacaciones
  })

const { isPending: isDeleteVacPending, mutate: mutateDeleteVac } =
  useAlertMutate({
    mutationFn: () => deleteSolicitudVacaciones(loginStore.user?.id ?? 4),
    refetchSecondary: refetchSolicitudVacaciones
  })

const onSubmit = (isSubmit: boolean) => {
  if (!isSubmit) {
    openDialog.value = false
    return
  }
  if (
    $dayjs(inicioDateToday.value).isAfter($dayjs(finDateToday.value)) ||
    $dayjs(inicioDateToday.value).isSame($dayjs(finDateToday.value))
  ) {
    dateOverflowModal.value = true
  } else {
    openDialog.value = false
    selectedRow.value == null
      ? mutateInsertVacacion({
          fechaFin: finDateToday.value,
          fechaInicio: inicioDateToday.value
        })
      : mutateUpdateVac({
          fechaFin: finDateToday.value,
          fechaInicio: inicioDateToday.value
        })
  }
}

watch(selectedRow, value => {
  if (value != null) {
    fechaInicio.value = value.fechaInicio
    fechaFin.value = value.fechaFin
  }
})
</script>

<template>
  <div class="p-5 box">
    <div
      class="flex flex-col items-center border-b pb-5 sm:flex-row border-slate-200/60 justify-between"
    >
      <h3 class="text-lg font-medium">Solicitud de Vacaciones</h3>
      <new-refresh
        is-new
        disabled-text="Solicitud en curso"
        :disabled="(dataSolicitudVacaciones?.length ?? 0) > 0"
        :disabled-fresh="isSolicitudVacacionesPending"
        @new="
          () => {
            selectedRow = null
            openDialog = true
          }
        "
        @refresh="refetchSolicitudVacaciones"
      />
    </div>
    <fetching
      :is-fetching="
        isSolicitudVacacionesPending ||
        isInsertVacacionPending ||
        isUpdateVacPending ||
        isDeleteVacPending
      "
      :data="dataSolicitudVacaciones"
      class=""
      empty-title="No hay una solicitud en curso, por favor agregue una."
      is-contained
      :display-data="dataSolicitudVacaciones ?? []"
    >
      <generic-table
        :data="dataSolicitudVacaciones ?? []"
        :entries="horasVac"
        :metadata="vacMetadata"
        is-delete-action
        is-edit-action
        identity-key="idvac"
        @delete-click="mutateDeleteVac"
        @edit-click="
          ({ diainicio: fechaInicio, diafin: fechaFin }) => {
            selectedRow = { fechaInicio, fechaFin }
            openDialog = true
          }
        "
      />
    </fetching>
  </div>
  <slide
    :open="openDialog"
    :item="selectedRow"
    title="Vacaciones"
    @submit="onSubmit"
  >
    <div class="flex flex-row p-5 gap-5 justify-between">
      <div class="flex flex-col">
        <p class="mb-3">Fecha de Inicio:</p>
        <litepicker v-model="fechaInicio" />
      </div>
      <div class="flex flex-col">
        <p class="mb-3">Fecha de Fin:</p>
        <litepicker v-model="fechaFin" />
      </div>
    </div>
  </slide>
  <v-dialog
    :open="dateOverflowModal"
    @close="() => (dateOverflowModal = false)"
  >
    <dialog-panel>
      <div class="p-5 text-center">
        <lucide-x-circle class="w-16 h-16 mx-auto mt-3 text-danger" />
        <div class="mt-5 text-3xl">¡Ups!</div>
        <div class="mt-2 text-slate-500">
          El rango de fechas no es válido, la fecha de inicio debe ser anterior
          a la fecha de fin.
        </div>
      </div>
      <div class="px-5 pb-8 text-center">
        <v-button
          type="button"
          variant="primary"
          class="w-24"
          @click="() => (dateOverflowModal = false)"
        >
          Vale
        </v-button>
      </div>
    </dialog-panel>
  </v-dialog>
</template>
