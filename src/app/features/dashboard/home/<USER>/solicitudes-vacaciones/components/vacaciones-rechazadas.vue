<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { horasVac, vacMetadata } from '@d/admin/models/horas-vac'
import GenericTable from '@d/common/components/generic-table/generic-table.vue'
import Fetching from '@d/common/components/reuse/fetching.vue'
import NewRefresh from '@d/common/components/reuse/new-refresh.vue'
import { useQuery } from '@tanstack/vue-query'

import { useLoginStore } from '@/common/stores/login-store'
import { getVacacionesRechazadas } from '@/dashboard/home/<USER>/solicitudes-vacaciones-repository'

const loginStore = useLoginStore()

const {
  data: dataVacacionesRechazadas,
  isFetching: isVacacionesRechazadasPending,
  refetch: refetchVacacionesRechazadas
} = useQuery({
  queryFn: () => getVacacionesRechazadas(loginStore.user?.id ?? 4),
  queryKey: ['vacaciones-rechazadas']
})
</script>

<template>
  <div class="p-5 box">
    <div
      class="flex flex-col items-center border-b pb-5 sm:flex-row border-slate-200/60 justify-between"
    >
      <h3 class="text-lg ml-1 font-medium">Vacaciones Rechazadas</h3>
      <new-refresh
        :disabled-fresh="isVacacionesRechazadasPending"
        @refresh="refetchVacacionesRechazadas"
      />
    </div>
    <fetching
      :is-fetching="isVacacionesRechazadasPending"
      :data="dataVacacionesRechazadas"
      class=""
      is-contained
      :display-data="dataVacacionesRechazadas ?? []"
    >
      <generic-table
        :data="dataVacacionesRechazadas ?? []"
        :entries="horasVac"
        :metadata="vacMetadata"
        identity-key="idvac"
      />
    </fetching>
  </div>
</template>
