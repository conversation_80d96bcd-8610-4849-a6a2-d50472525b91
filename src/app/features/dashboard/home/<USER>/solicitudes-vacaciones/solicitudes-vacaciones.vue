<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script>
import SolicitudVacaciones from './components/solicitud-vacaciones.vue'
import VacacionesAceptadas from './components/vacaciones-aceptadas.vue'
import VacacionesRechazadas from './components/vacaciones-rechazadas.vue'

export default {
  components: { SolicitudVacaciones, VacacionesAceptadas, VacacionesRechazadas }
}
</script>

<template>
  <div>
    <div class="flex items-center mt-8 intro-y">
      <h2 class="mr-auto text-2xl font-medium">Solicitudes Vacaciones</h2>
    </div>
    <div class="grid grid-cols-12 gap-6 mt-5 intro-y">
      <div class="col-span-full">
        <solicitud-vacaciones />
        <div class="h-px my-6 bg-slate-200/60 dark:bg-darkmode-400" />
        <vacaciones-aceptadas />
        <div class="h-px my-6 bg-slate-200/60 dark:bg-darkmode-400" />
        <vacaciones-rechazadas />
      </div>
    </div>
  </div>
</template>
