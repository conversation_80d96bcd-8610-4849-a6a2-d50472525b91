<!-- eslint-disable @typescript-eslint/no-confusing-void-expression -->
<script setup lang="ts">
import VButton from '@c/components/button/button.vue'
import VDialog from '@d/common/components/dialog/Dialog.vue'
import DialogPanel from '@d/common/components/dialog/Panel.vue'
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'
import Fetching from '@d/common/components/reuse/fetching.vue'
import SInput from '@d/common/components/reuse/input.vue'
import NewRefresh from '@d/common/components/reuse/new-refresh.vue'
import SSelect from '@d/common/components/reuse/select.vue'
import Slide from '@d/common/components/reuse/slide.vue'
import VTable from '@d/common/components/table/Table.vue'
import VTbody from '@d/common/components/table/Tbody.vue'
import VTd from '@d/common/components/table/Td.vue'
import VTh from '@d/common/components/table/Th.vue'
import VThead from '@d/common/components/table/Thead.vue'
import VTr from '@d/common/components/table/Tr.vue'
import Tippy from '@d/common/components/tippy/tippy.vue'
import { nanoid } from 'nanoid'

import Loading from '@/common/components/loading.vue'

import { useHorasTrabajadas } from './horas-trabajadas'

const {
  calcStatusStyle,
  calcTextStyle,
  dataEtapas,
  dataHorasTrabajadas,
  dataProyecto,
  dataTareas,
  dateModel,
  days,
  isDeletingHoras,
  isEmpty,
  isEtapasPending,
  isExceedHours,
  isExceedHoursWithForm,
  isHorasTrabajadasPending,
  isInsertingHoras,
  isProyectoPending,
  isTareasPending,
  isUpdatingHoras,
  noDescCalc,
  onChange,
  onEdit,
  onErase,
  onSubmit,
  resetForm,
  totalHoursDoneComputed,
  totalHoursDoneWithFormComputed,
  weekDaysTable,
  weekRange
} = useHorasTrabajadas()
</script>

<template>
  <div class="flex items-center mt-8 intro-y">
    <h2 class="mr-auto text-2xl font-medium">Horas Trabajadas</h2>
  </div>
  <div class="grid grid-cols-12 gap-6 mt-5">
    <div class="col-span-full intro-y box">
      <div
        class="flex flex-col items-center p-5 border-b sm:flex-row border-slate-200/60 justify-between"
      >
        <div class="flex flex-row gap-2">
          <litepicker
            v-model="dateModel"
            isBeginWeekMonday
            @change="() => onChange()"
          />
          <p class="mt-2 ml-3 italic">{{ weekRange }}</p>
        </div>
        <div class="flex flex-row gap-2">
          <p
            :class="`mt-2 mr-5 ${isExceedHours ? 'text-orange-500 bold' : 'italic'}`"
          >
            Horas pendientes por insertar:
            {{ totalHoursDoneComputed }} de 40
          </p>
          <new-refresh
            isNew
            :disabled="isExceedHours"
            @refresh="refetchHorasTrabajadas"
            @new="
              () => {
                selectedRow = null
                openDialog = true
              }
            "
          />
        </div>
      </div>
      <fetching
        :is-fetching="
          isHorasTrabajadasPending ||
          isInsertingHoras ||
          isUpdatingHoras ||
          isDeletingHoras ||
          checkLoading
        "
        :data="dataHorasTrabajadas"
        empty-title="No hay horas registradas en esta semana"
        :display-data="
          (dataHorasTrabajadas?.length ?? 0) > 1
            ? (dataHorasTrabajadas ?? [])
            : []
        "
      >
        <v-table hover sm>
          <v-thead variant="dark">
            <v-tr>
              <v-th
                v-for="(row, index) in [
                  'Proyecto',
                  'Tarea',
                  'Etapa',
                  ...weekDaysTable
                ]"
                :key="row"
                :class="`whitespace-nowrap font-bold ${index > 2 ? 'text-center' : ''}`"
              >
                {{ row }}
              </v-th>
              <v-th class="whitespace-nowrap font-bold text-center">
                Total Horas
              </v-th>
              <v-th class="whitespace-nowrap font-bold text-center">
                Acciones
              </v-th>
            </v-tr>
          </v-thead>
          <v-tbody>
            <v-tr
              v-for="(row, indexRow) in dataHorasTrabajadas"
              :key="`${indexRow} ${nanoid()}`"
              :class="`${indexRow === (dataHorasTrabajadas?.length ?? 0) - 1 ? 'dark:bg-[#303761] bg-[#dadef7]' : 'dark:hover:bg-[#303761] hover:bg-[#dadef7]'}`"
            >
              <v-td>{{ row.proyecto }}</v-td>
              <v-td>{{ row.tarea }}</v-td>
              <v-td>{{ row.etapa }}</v-td>
              <v-td
                :class="`mx-auto text-center ${calcStatusStyle(row.estado1)}`"
              >
                <tippy variant="primary" :content="noDescCalc(row.desc1)">
                  <p
                    :class="`w-full h-full ${calcTextStyle(row.estado1, row.dia1)}`"
                  >
                    {{ row.dia1 ?? 0 }}
                  </p>
                </tippy>
              </v-td>
              <v-td
                :class="`mx-auto text-center ${calcStatusStyle(row.estado2)}`"
              >
                <tippy variant="primary" :content="noDescCalc(row.desc2)">
                  <p
                    :class="`w-full h-full ${calcTextStyle(row.estado2, row.dia2)}`"
                  >
                    {{ row.dia2 ?? 0 }}
                  </p>
                </tippy>
              </v-td>
              <v-td
                :class="`mx-auto text-center ${calcStatusStyle(row.estado3)}`"
              >
                <tippy variant="primary" :content="noDescCalc(row.desc3)">
                  <p
                    :class="`w-full h-full ${calcTextStyle(row.estado3, row.dia3)}`"
                  >
                    {{ row.dia3 ?? 0 }}
                  </p>
                </tippy>
              </v-td>
              <v-td
                :class="`mx-auto text-center ${calcStatusStyle(row.estado4)}`"
              >
                <tippy variant="primary" :content="noDescCalc(row.desc4)">
                  <p
                    :class="`w-full h-full ${calcTextStyle(row.estado4, row.dia4)}`"
                  >
                    {{ row.dia4 ?? 0 }}
                  </p>
                </tippy>
              </v-td>
              <v-td
                :class="`mx-auto text-center ${calcStatusStyle(row.estado5)}`"
              >
                <tippy variant="primary" :content="noDescCalc(row.desc5)">
                  <p
                    :class="`w-full h-full ${calcTextStyle(row.estado5, row.dia5)}`"
                  >
                    {{ row.dia5 ?? 0 }}
                  </p>
                </tippy>
              </v-td>
              <v-td class="mx-auto text-center">
                <p class="w-full h-full">
                  {{ row.totalsemtar ?? 0 }}
                </p>
              </v-td>
              <v-td class="flex flex-row gap-3 h-[70px] justify-center w-full">
                <v-button
                  v-if="indexRow !== (dataHorasTrabajadas?.length ?? 0) - 1"
                  variant="dark"
                  @click="() => onEdit(row)"
                >
                  <LucidePencil class="h-4 w-4" />
                </v-button>
                <v-button
                  v-if="indexRow !== (dataHorasTrabajadas?.length ?? 0) - 1"
                  variant="dark"
                  @click="() => onErase(row)"
                >
                  <LucideEraser class="h-4 w-4" />
                </v-button>
              </v-td>
            </v-tr>
          </v-tbody>
        </v-table>
      </fetching>
    </div>
  </div>
  <slide
    :open="openDialog"
    :item="selectedRow"
    :is-not-box="true"
    title="Horas"
    @submit="onSubmit"
  >
    <template v-if="!isEmpty" #header>
      <p class="mt-2 mr-4">Horas:</p>
      <s-input
        v-model="standardHour"
        class="w-9/12 h-9"
        min="0"
        max="8"
        type="number"
      />
      <v-button variant="dark" @click="resetForm">
        <lucide-refresh-ccw class="h-4 w-4" />
      </v-button>
    </template>
    <div class="intro-y w-full">
      <div
        v-if="isEtapasPending || isProyectoPending"
        class="flex flex-warp p-5 gap-3 w-full justify-center"
      >
        <loading icon="puff" class="mx-auto" />
      </div>
      <div v-else class="flex flex-col p-5 gap-5">
        <s-select
          v-model="formReactive.proyecto"
          default-text="Seleccionar Proyecto"
          identity-key="clavecorta"
          class="w-full"
          value-key="clavecorta"
          :display-text="row => row.nombreproyecto"
          :data="dataProyecto ?? []"
        />
        <div
          v-if="isTareasPending"
          class="flex flex-warp p-5 gap-3 w-full justify-center"
        >
          <loading icon="puff" class="mx-auto" />
        </div>
        <s-select
          v-else-if="
            formReactive.proyecto !== '' && !isTareasPending && dataTareas
          "
          v-model="formReactive.tarea"
          default-text="Seleccionar Tarea"
          identity-key="clavecorta"
          class="w-full"
          value-key="clavecorta"
          :display-text="row => row.nombretarea"
          :data="dataTareas ?? []"
        />
        <s-select
          v-if="formReactive.tarea !== ''"
          v-model="formReactive.etapa"
          default-text="Seleccionar Etapa"
          identity-key="idetapa"
          class="w-full"
          value-key="etapa"
          :display-text="row => row.etapa"
          :data="dataEtapas ?? []"
        />
        <p
          v-if="!isEmpty"
          :class="isExceedHoursWithForm ? 'text-orange-500 bold' : 'italic'"
        >
          Horas pendientes por insertar:
          {{ totalHoursDoneWithFormComputed }} de 40
        </p>
        <div
          v-if="!isEmpty"
          class="h-px mb-2 -mx-2 bg-slate-200/60 dark:bg-darkmode-400"
        />
        <div v-if="!isEmpty" class="flex flex-wrap overflow-auto max-h-[600px]">
          <div
            v-for="(day, index) in days"
            :key="index"
            class="flex flex-col px-2 mb-4 gap-4 w-full"
          >
            <div class="flex flex-row gap-4 w-full">
              <s-input :value="day" class="w-full text-center" readonly />
              <s-input
                v-model="formReactive.horas[index]"
                class="w-full text-center"
                placeholder="Horas ..."
                type="number"
                min="0"
                max="8"
              />
            </div>
            <s-input
              v-model="formReactive.descripciones[index]"
              class="w-full"
              placeholder="Descripción ..."
              type="text"
            />
            <div v-if="index !== 4" class="border-b border-slate-200/60" />
          </div>
        </div>
      </div>
    </div>
  </slide>
  <v-dialog :open="errorModal" @close="() => (errorModal = false)">
    <dialog-panel>
      <div class="p-5 text-center">
        <lucide-x-circle class="w-16 h-16 mx-auto mt-3 text-danger" />
        <div class="mt-5 text-3xl">Error!</div>
        <div class="mt-2 text-slate-500">
          {{ checkMessage }}
        </div>
      </div>
      <div class="px-5 pb-8 text-center">
        <v-button
          type="button"
          variant="primary"
          class="w-24"
          @click="
            () => {
              checkLoading = false
              errorModal = false
            }
          "
        >
          Vale
        </v-button>
      </div>
    </dialog-panel>
  </v-dialog>
</template>
