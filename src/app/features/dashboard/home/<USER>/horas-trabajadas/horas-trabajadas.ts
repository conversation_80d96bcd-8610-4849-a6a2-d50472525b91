import { useAlertMutate } from '@d/common/composables/use-alert-mutate'
import type { HorasTrabajadas } from '@d/home/<USER>/horas-trabajadas'
import {
  deleteHorasTrabajadas,
  getEtapas,
  getHorasTrabajadas,
  getHorasTrabajadasCheck,
  getProyectosConsultor,
  getTareasConsultorAprob,
  insertHorasTrabajadas,
  updateHorasTrabajadas
} from '@d/home/<USER>/home-repository'
import { useQuery } from '@tanstack/vue-query'
import { nth, piped } from 'rambdax'

import { useLoginStore } from '@/common/stores/login-store'
import { useNuxtApp } from '#app'

export const useHorasTrabajadas = () => {
  const loginStore = useLoginStore()
  const { $dayjs } = useNuxtApp()

  const errorModal = ref(false)
  const dateModel = ref('')
  const checkMessage = ref('')
  const checkLoading = ref(false)
  const openDialog = ref(false)
  const selectedRow = ref<HorasTrabajadas | null>(null)
  const standardHour = ref(8)
  const formReactive = reactive({
    descripciones: ['', '', '', '', ''],
    etapa: '',
    horas: [0, 0, 0, 0, 0],
    proyecto: '',
    tarea: ''
  })

  const dateFormated = computed(() =>
    dateModel.value === '' ? $dayjs().format('YYYY-MM-DD') : dateModel.value
  )

  const { data: dataProyecto, isFetching: isProyectoPending } = useQuery({
    queryFn: () => getProyectosConsultor(loginStore.user?.id ?? 0),
    queryKey: ['proyectoWorkHours']
  })

  const { data: dataEtapas, isFetching: isEtapasPending } = useQuery({
    queryFn: getEtapas,
    queryKey: ['etapasWorkHours']
  })

  const {
    data: dataHorasTrabajadas,
    isFetching: isHorasTrabajadasPending,
    refetch: refetchHorasTrabajadas
  } = useQuery({
    queryFn: () =>
      getHorasTrabajadas(dateFormated.value, loginStore.user?.id ?? 0),
    queryKey: ['horas-trabajadas']
  })

  const { isPending: isInsertingHoras, mutate: mutateInsertHorasTrabajadas } =
    useAlertMutate({
      alertErrorMessage:
        'No se pudo insertar, verifique que las horas no superen las horas semanales',
      mutationFn: insertHorasTrabajadas,
      refetchSecondary: refetchHorasTrabajadas
    })

  const { isPending: isUpdatingHoras, mutate: mutateUpdateHorasTrabajadas } =
    useAlertMutate({
      alertErrorMessage:
        'No se pudo actualizar, verifique que las horas no superen las horas semanales',
      alertSuccessMessage: 'Hora actualizada correctamente',
      mutationFn: updateHorasTrabajadas,
      refetchSecondary: refetchHorasTrabajadas
    })

  const { isPending: isDeletingHoras, mutate: mutateDeleteHorasTrabajadas } =
    useAlertMutate({
      alertErrorMessage:
        'No se pudo borrar, verifique que las horas no superen las horas semanales',
      mutationFn: deleteHorasTrabajadas,
      refetchSecondary: refetchHorasTrabajadas
    })

  const {
    data: dataTareas,
    isFetching: isTareasPending,
    refetch: refetchTareas
  } = useQuery({
    enabled: computed(() => formReactive.proyecto !== ''),
    queryFn: () =>
      getTareasConsultorAprob(loginStore.user?.id ?? 4, formReactive.proyecto),
    queryKey: ['tareasConsultorHours', formReactive.proyecto]
  })

  const truncate = (num: number, decimales = 1) => {
    const factor = 10 ** decimales
    return Math.trunc(num * factor) / factor
  }

  const onChange = () => setTimeout(() => refetchHorasTrabajadas(), 200)

  const totalHoursDone = computed(() => {
    let acc = 0
    const table = dataHorasTrabajadas.value ?? []

    for (let i = 0; i < table.length; i++) {
      if (i === table.length - 1) break
      acc += Number(nth(i, table)?.dia1) ?? 0
      acc += Number(nth(i, table)?.dia2) ?? 0
      acc += Number(nth(i, table)?.dia3) ?? 0
      acc += Number(nth(i, table)?.dia4) ?? 0
      acc += Number(nth(i, table)?.dia5) ?? 0
    }
    return acc
  })

  const totalHoursDoneComputed = computed(() =>
    truncate(Math.max(0, 40 - totalHoursDone.value))
  )

  const totalHoursDoneWithForm = computed(() => {
    let acc = totalHoursDone.value
    acc += Number(nth(0, formReactive.horas)) ?? 0
    acc += Number(nth(1, formReactive.horas)) ?? 0
    acc += Number(nth(2, formReactive.horas)) ?? 0
    acc += Number(nth(3, formReactive.horas)) ?? 0
    acc += Number(nth(4, formReactive.horas)) ?? 0
    return acc
  })

  const totalHoursDoneWithFormComputed = computed(() =>
    truncate(Math.max(0, 40 - totalHoursDoneWithForm.value))
  )

  const isExceedHours = computed(() => totalHoursDone.value >= 40)
  const isExceedHoursWithForm = computed(
    () => totalHoursDoneWithForm.value > 40
  )

  const weekDays = computed(() =>
    Array.from({ length: 5 }, (_, i) =>
      $dayjs(dateFormated.value)
        .startOf('isoWeek')
        .add(i, 'day')
        .format('YYYY-MM-DD')
    )
  )

  const localeDays = (day: string) => {
    switch (day) {
      case 'Fri': {
        return 'Vie'
      }
      case 'Mon': {
        return 'Lun'
      }
      case 'Thu': {
        return 'Jue'
      }
      case 'Tue': {
        return 'Mar'
      }
      case 'Wed': {
        return 'Mié'
      }
      default: {
        return day
      }
    }
  }

  const weekDaysTable = computed(() =>
    weekDays.value.map(
      day =>
        `${localeDays($dayjs(day).format('ddd'))} ${$dayjs(day).format('DD')}`
    )
  )

  const days = computed(() =>
    weekDays.value.map(day => `${localeDays($dayjs(day).format('ddd'))} ${day}`)
  )

  const weekRange = computed(() =>
    piped(
      {
        end: $dayjs(dateFormated.value).endOf('isoWeek'),
        start: $dayjs(dateFormated.value).startOf('isoWeek')
      },
      range =>
        range.start.format('MMM') === range.end.format('MMM')
          ? `${range.start.format('DD')} - ${range.end.format('DD')} de ${range.start.format('MMM')} de ${range.start.format('YYYY')}`
          : `${range.start.format('DD')} de ${range.start.format('MMM')} - ${range.end.format('DD')} de ${range.end.format('MMM')} de ${range.start.format('YYYY')}`
    )
  )

  const isEmpty = computed(
    () =>
      formReactive.proyecto === '' ||
      formReactive.etapa === '' ||
      formReactive.tarea === ''
  )

  const noDescCalc = (desc?: string | null) =>
    desc === undefined || desc === null || desc === ''
      ? 'Sin descripción'
      : desc

  const resetForm = () => {
    formReactive.horas = [
      standardHour.value,
      standardHour.value,
      standardHour.value,
      standardHour.value,
      standardHour.value
    ]
    formReactive.descripciones = ['', '', '', '', '']
  }

  const calcStatusStyle = (estado?: string | null) => {
    if (estado === undefined || estado === null) return ''
    if (estado.trimEnd() === 'A') return 'bg-lime-900'
    if (estado.trimEnd() === 'P') return 'bg-yellow-900'
    if (estado.trimEnd() === 'R') return 'bg-red-900'
    return ''
  }

  const calcTextStyle = (estado?: string | null, horas?: number | null) => {
    if (estado === undefined || estado === null) return ''
    if (horas === undefined || horas === null || horas === 0) return ''
    if (estado.trimEnd() === 'A') return 'text-white'
    if (estado.trimEnd() === 'P') return 'text-white'
    if (estado.trimEnd() === 'R') return 'text-white'
    return ''
  }

  const verifBase = () =>
    getHorasTrabajadasCheck(
      nth(0, weekDays.value) ?? '',
      nth(4, weekDays.value) ?? '',
      loginStore.user?.id ?? 0,
      formReactive.proyecto,
      formReactive.etapa,
      formReactive.tarea
    )

  const resetForBadEditing = () => {
    errorModal.value = true
    if (selectedRow.value !== null) {
      formReactive.proyecto = selectedRow.value.proyecto
      formReactive.etapa = selectedRow.value.etapa
      formReactive.tarea = selectedRow.value.tarea
    }
  }

  const onEdit = (row: HorasTrabajadas) => {
    selectedRow.value = row
    setTimeout(() => (openDialog.value = true), 250)
  }

  const onErase = (row: HorasTrabajadas) =>
    mutateDeleteHorasTrabajadas({
      id1: row.id1,
      id2: row.id2,
      id3: row.id3,
      id4: row.id4,
      id5: row.id5
    })

  const onSubmit = async (isSubmit: boolean) => {
    openDialog.value = false
    if (!isSubmit) return
    checkLoading.value = true
    if (selectedRow.value == null && (await verifBase()).length > 0) {
      checkMessage.value =
        'La tarea que desea insertar ya existe. Por favor, edite la tarea.'
      errorModal.value = true
      return
    }

    if (selectedRow.value != null) {
      const verif = await verifBase()
      if (verif.length > 0) {
        console.log(verif)
        checkMessage.value =
          'La tarea que desea editar ya existe anteriormente. En cuyo caso, por favor, edite la tarea existente.'
        if (
          (nth(0, verif)?.id ?? 0) !== Number(selectedRow.value?.id1) &&
          (nth(0, verif)?.fecha ?? '') === nth(0, weekDays.value) &&
          (nth(0, verif)?.horastrabajadas ?? 0) !== 0 &&
          (nth(0, verif)?.proyecto ?? '') === formReactive.proyecto &&
          (nth(0, verif)?.etapa ?? '') === formReactive.etapa &&
          (nth(0, verif)?.tarea ?? '') === formReactive.tarea &&
          formReactive.horas[0] !== 0
        ) {
          console.log('enter 0')
          resetForBadEditing()
          return
        }
        if (
          (nth(1, verif)?.id ?? 0) !== Number(selectedRow.value?.id2) &&
          (nth(1, verif)?.fecha ?? '') === nth(1, weekDays.value) &&
          (nth(1, verif)?.horastrabajadas ?? 0) !== 0 &&
          (nth(1, verif)?.proyecto ?? '') === formReactive.proyecto &&
          (nth(1, verif)?.etapa ?? '') === formReactive.etapa &&
          (nth(1, verif)?.tarea ?? '') === formReactive.tarea &&
          formReactive.horas[1] !== 0
        ) {
          console.log('enter 1')
          resetForBadEditing()
          return
        }
        if (
          (nth(2, verif)?.id ?? 0) !== Number(selectedRow.value?.id3) &&
          (nth(2, verif)?.fecha ?? '') === nth(2, weekDays.value) &&
          (nth(2, verif)?.horastrabajadas ?? 0) !== 0 &&
          (nth(2, verif)?.proyecto ?? '') === formReactive.proyecto &&
          (nth(2, verif)?.etapa ?? '') === formReactive.etapa &&
          (nth(2, verif)?.tarea ?? '') === formReactive.tarea &&
          formReactive.horas[2] !== 0
        ) {
          console.log('enter 2')
          resetForBadEditing()
          return
        }
        if (
          (nth(3, verif)?.id ?? 0) !== Number(selectedRow.value?.id4) &&
          (nth(3, verif)?.fecha ?? '') === nth(3, weekDays.value) &&
          (nth(3, verif)?.horastrabajadas ?? 0) !== 0 &&
          (nth(3, verif)?.proyecto ?? '') === formReactive.proyecto &&
          (nth(3, verif)?.etapa ?? '') === formReactive.etapa &&
          (nth(3, verif)?.tarea ?? '') === formReactive.tarea &&
          formReactive.horas[3] !== 0
        ) {
          console.log('enter 3')
          resetForBadEditing()
          return
        }
        if (
          (nth(4, verif)?.id ?? 0) !== Number(selectedRow.value?.id5) &&
          (nth(4, verif)?.fecha ?? '') === nth(4, weekDays.value) &&
          (nth(4, verif)?.horastrabajadas ?? 0) !== 0 &&
          (nth(4, verif)?.proyecto ?? '') === formReactive.proyecto &&
          (nth(4, verif)?.etapa ?? '') === formReactive.etapa &&
          (nth(4, verif)?.tarea ?? '') === formReactive.tarea &&
          formReactive.horas[4] !== 0
        ) {
          console.log('enter 4')
          resetForBadEditing()
          return
        }
      }
    }

    checkLoading.value = false
    piped(
      {
        consultor: loginStore.user?.id ?? 0,
        descripcion1: nth(0, formReactive.descripciones) ?? '',
        descripcion2: nth(1, formReactive.descripciones) ?? '',
        descripcion3: nth(2, formReactive.descripciones) ?? '',
        descripcion4: nth(3, formReactive.descripciones) ?? '',
        descripcion5: nth(4, formReactive.descripciones) ?? '',
        etapa: formReactive.etapa,
        fecha1: nth(0, weekDays.value) ?? '',
        fecha2: nth(1, weekDays.value) ?? '',
        fecha3: nth(2, weekDays.value) ?? '',
        fecha4: nth(3, weekDays.value) ?? '',
        fecha5: nth(4, weekDays.value) ?? '',
        horasTrabajadas1: nth(0, formReactive.horas) ?? 0,
        horasTrabajadas2: nth(1, formReactive.horas) ?? 0,
        horasTrabajadas3: nth(2, formReactive.horas) ?? 0,
        horasTrabajadas4: nth(3, formReactive.horas) ?? 0,
        horasTrabajadas5: nth(4, formReactive.horas) ?? 0,
        proyecto: formReactive.proyecto,
        tarea: formReactive.tarea
      },
      res =>
        selectedRow.value == null
          ? mutateInsertHorasTrabajadas(res)
          : mutateUpdateHorasTrabajadas({
              ...res,
              id1: selectedRow.value.id1,
              id2: selectedRow.value.id2,
              id3: selectedRow.value.id3,
              id4: selectedRow.value.id4,
              id5: selectedRow.value.id5
            })
    )
  }

  watch(selectedRow, value => {
    if (value != null) {
      formReactive.proyecto = value.proyecto
      formReactive.etapa = value.etapa
      formReactive.tarea = value.tarea
      formReactive.horas = [
        value.dia1 ?? 0,
        value.dia2 ?? 0,
        value.dia3 ?? 0,
        value.dia4 ?? 0,
        value.dia5 ?? 0
      ]
      formReactive.descripciones = [
        value.desc1 ?? '',
        value.desc2 ?? '',
        value.desc3 ?? '',
        value.desc4 ?? '',
        value.desc5 ?? ''
      ]
      void refetchTareas()
    }
  })

  watch(
    () => formReactive.proyecto,
    value => {
      if (value !== '' && openDialog.value) {
        formReactive.tarea = ''
        void refetchTareas()
      }
    }
  )

  return {
    dataProyecto,
    dataEtapas,
    dataHorasTrabajadas,
    dataTareas,
    dateModel,
    isProyectoPending,
    isEtapasPending,
    isInsertingHoras,
    isUpdatingHoras,
    isDeletingHoras,
    isHorasTrabajadasPending,
    isTareasPending,
    onChange,
    onEdit,
    onErase,
    onSubmit,
    calcStatusStyle,
    isExceedHours,
    isExceedHoursWithForm,
    totalHoursDoneComputed,
    weekDaysTable,
    days,
    isEmpty,
    weekRange,
    calcTextStyle,
    noDescCalc,
    totalHoursDoneWithFormComputed,
    resetForm
  }
}
