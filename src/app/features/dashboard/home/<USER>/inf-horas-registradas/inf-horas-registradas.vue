<!-- eslint-disable sonarjs/no-nested-assignment -->
<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import VButton from '@c/components/button/button.vue'
import VDialog from '@d/common/components/dialog/Dialog.vue'
import DialogPanel from '@d/common/components/dialog/Panel.vue'
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'
import { getInformeHorasRegistradas } from '@d/home/<USER>/inf-horas-registradas'
import { useQuery } from '@tanstack/vue-query'
import { piped, tap } from 'rambdax'

import Loading from '@/common/components/loading.vue'
import { useLoginStore } from '@/common/stores/login-store'
import { useNuxtApp } from '#app'

const { $dayjs } = useNuxtApp()
const inicioDate = ref('')
const finDate = ref('')

const inicioDateToday = computed(() =>
  inicioDate.value === '' ? $dayjs().format('YYYY-MM-DD') : inicioDate.value
)
const finDateToday = computed(() =>
  finDate.value === '' ? $dayjs().format('YYYY-MM-DD') : finDate.value
)

const dateOverflowModal = ref(false)
const doVisualize = ref(false)
const loginStore = useLoginStore()

const {
  isFetching: isHorasRegistradasPending,
  refetch: refetchHorasRegistradas
} = useQuery({
  enabled: computed(() => doVisualize.value),
  queryFn: () =>
    getInformeHorasRegistradas(
      loginStore.user?.id ?? 0,
      inicioDateToday.value,
      finDateToday.value
    ).then(url =>
      piped(
        document.createElement('a'),
        tap(a => (a.href = url)),
        tap(a => (a.download = 'horas-registradas.xlsx')),
        tap(a => document.body.append(a)),
        tap(a => a.click()),
        tap(a => a.remove()),
        tap(() => URL.revokeObjectURL(url))
      )
    ),
  queryKey: ['informes-horas-registradas-consul']
})

const calcHorasRegistradas = () =>
  $dayjs(inicioDateToday.value).isAfter($dayjs(finDateToday.value))
    ? (dateOverflowModal.value = true)
    : void refetchHorasRegistradas()

watch(isHorasRegistradasPending, value => (doVisualize.value = value))
</script>

<template>
  <div>
    <div class="flex items-center mt-8 intro-y">
      <h2 class="mr-auto text-2xl font-medium">Informe Horas Registradas</h2>
    </div>
    <div class="grid grid-cols-12 gap-6 mt-5 intro-y">
      <div class="col-span-full">
        <div class="intro-y box">
          <div
            class="flex flex-col items-center p-5 border-b sm:flex-row border-slate-200/60 justify-evenly"
          >
            <div class="flex flex-col gap-2">
              Fecha Inicio:
              <litepicker v-model="inicioDate" />
            </div>
            <div class="flex flex-col gap-2">
              Fecha Fin:
              <litepicker v-model="finDate" />
            </div>
          </div>
          <div class="flex flex-row p-5 w-full justify-center">
            <v-button
              variant="success"
              class="w-64"
              :disabled="isHorasRegistradasPending"
              @click="calcHorasRegistradas"
            >
              <template v-if="isHorasRegistradasPending">
                <loading icon="three-dots" class="mr-3 invert" />
              </template>
              <lucide-sheet v-else class="w-4 h-4 mr-2" /> Informe Horas
              Registradas
            </v-button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <v-dialog
    :open="dateOverflowModal"
    @close="() => (dateOverflowModal = false)"
  >
    <dialog-panel>
      <div class="p-5 text-center">
        <lucide-x-circle class="w-16 h-16 mx-auto mt-3 text-danger" />
        <div class="mt-5 text-3xl">¡Ups!</div>
        <div class="mt-2 text-slate-500">El rango de fechas no es válido.</div>
      </div>
      <div class="px-5 pb-8 text-center">
        <v-button
          type="button"
          variant="primary"
          class="w-24"
          @click="() => (dateOverflowModal = false)"
        >
          Vale
        </v-button>
      </div>
    </dialog-panel>
  </v-dialog>
</template>
