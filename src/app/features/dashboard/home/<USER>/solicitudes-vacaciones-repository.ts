import type { HorasVac } from '@d/admin/models/horas-vac'

const route = '/home/<USER>'

export const getSolicitudVacaciones = (consultorId = 4) =>
  $fetch<HorasVac[]>(`${route}/solicitud-vacaciones`, {
    params: { consultorId }
  })

export const insertSolicitudVacaciones = ({
  consultorId,
  fechaInicio,
  fechaFin
}: {
  consultorId: number
  fechaInicio: string
  fechaFin: string
}) =>
  $fetch(`${route}/insert-vacacion`, {
    method: 'POST',
    body: {
      consultorId,
      fechaInicio,
      fechaFin
    }
  })

export const updateSolicitudVacaciones = ({
  consultorId,
  fechaInicio,
  fechaFin
}: {
  consultorId: number
  fechaInicio: string
  fechaFin: string
}) =>
  $fetch(`${route}/update-vacacion`, {
    method: 'PUT',
    body: {
      consultorId,
      fechaInicio,
      fechaFin
    }
  })

export const deleteSolicitudVacaciones = (consultorId: number) =>
  $fetch(`${route}/delete-vacacion`, {
    method: 'DELETE',
    params: { consultorId }
  })

export const getVacacionesAceptadas = (consultorId = 4) =>
  $fetch<HorasVac[]>(`${route}/vacaciones-aceptadas`, {
    params: { consultorId }
  })

export const getVacacionesRechazadas = (consultorId = 4) =>
  $fetch<HorasVac[]>(`${route}/vacaciones-rechazadas`, {
    params: { consultorId }
  })
