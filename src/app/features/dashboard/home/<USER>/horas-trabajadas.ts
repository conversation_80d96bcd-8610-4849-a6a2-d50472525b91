import { type } from 'arktype'

export const horasTrabajadas = type({
  tarea: 'string',
  proyecto: 'string',
  etapa: 'string',
  dia1: 'number?',
  id1: 'number?',
  totalsemtar: 'number?',
  estado1: 'string?',
  desc1: 'string?',
  dia2: 'number?',
  id2: 'number?',
  estado2: 'string?',
  desc2: 'string?',
  dia3: 'number?',
  id3: 'number?',
  estado3: 'string?',
  desc3: 'string?',
  dia4: 'number?',
  estado4: 'string?',
  desc4: 'string?',
  id4: 'number?',
  dia5: 'number?',
  estado5: 'string?',
  desc5: 'string?',
  id5: 'number?'
})

export type HorasTrabajadas = typeof horasTrabajadas.infer

export type InsertHorasTrabajadas = {
  fecha1: string
  fecha2: string
  fecha3: string
  fecha4: string
  fecha5: string
  consultor: number
  horasTrabajadas1: number
  horasTrabajadas2: number
  horasTrabajadas3: number
  horasTrabajadas4: number
  horasTrabajadas5: number
  descripcion1: string
  descripcion2: string
  descripcion3: string
  descripcion4: string
  descripcion5: string
  proyecto: string
  tarea: string
  etapa: string
}

export type UpdateHorasTrabajadasOnly = {
  id: number
  fecha: string
  consultor: number
  horastrabajadas: number
  descripcion: string
  proyecto: string
  tarea: string
  etapa: string
}

export type UpdateHorasTrabajadas = InsertHorasTrabajadas & {
  id1?: number
  id2?: number
  id3?: number
  id4?: number
  id5?: number
  fecha1: string
  fecha2: string
  fecha3: string
  fecha4: string
  fecha5: string
  consultor: number
  horasTrabajadas1: number
  horasTrabajadas2: number
  horasTrabajadas3: number
  horasTrabajadas4: number
  horasTrabajadas5: number
  descripcion1: string
  descripcion2: string
  descripcion3: string
  descripcion4: string
  descripcion5: string
  proyecto: string
  tarea: string
  etapa: string
}
