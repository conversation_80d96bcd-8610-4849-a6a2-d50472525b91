export const accesosRoutes = [
  {
    path: '/dash/accesos/accesos',
    component: () => import('@d/accesos/pages/accesos/accesos.vue'),
    meta: { breadcrumb: 'Accesos' }
  },
  {
    path: '/dash/accesos/proveedores',
    component: () => import('@d/accesos/pages/proveedores/proveedores.vue'),
    meta: { breadcrumb: 'Proveedores' }
  },
  {
    path: '/dash/accesos/sistemas',
    component: () => import('@d/accesos/pages/sistemas/sistemas.vue'),
    meta: { breadcrumb: 'Sistemas' }
  },
  {
    path: '/dash/accesos/sistemas-proyectos',
    component: () =>
      import('@d/accesos/pages/sistemas-proyectos/sistemas-proyectos.vue'),
    meta: { breadcrumb: 'Sistemas Proyectos' }
  }
]
