import { type } from 'arktype'

export const proveedores = type({
  idprov: 'number',
  nombreproveedor: 'string',
  pais: 'number'
})

export type Proveedores = typeof proveedores.infer

export const proveedoresMetadata: {
  key: keyof Proveedores
  label: string
}[] = [
  {
    key: 'idprov',
    label: 'ID Proveedor'
  },
  {
    key: 'nombreproveedor',
    label: 'Nombre Proveedor'
  },
  {
    key: 'pais',
    label: 'País'
  }
]
