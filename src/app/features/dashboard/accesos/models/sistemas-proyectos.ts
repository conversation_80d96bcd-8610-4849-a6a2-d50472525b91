import { type } from 'arktype'

export const sistemasProyectos = type({
  idrelacion: 'number',
  idsistema: 'number',
  idproyecto: 'number',
  sistemanombre: 'string',
  componente: 'string',
  ambiente: 'string',
  clavecorta: 'string',
  nombrecliente: 'string',
  nombreproyecto: 'string'
})

export type SistemaProyectos = typeof sistemasProyectos.infer

export const sistemasProyectosMetadata: {
  key: keyof SistemaProyectos
  label: string
}[] = [
  {
    key: 'sistemanombre',
    label: 'Sistema'
  },
  {
    key: 'nombrecliente',
    label: 'Cliente'
  },
  {
    key: 'componente',
    label: 'Componente'
  },
  {
    key: 'ambiente',
    label: 'Ambiente'
  },
  {
    key: 'clavecorta',
    label: 'Clave Corta'
  },
  {
    key: 'nombreproyecto',
    label: 'Proyecto'
  }
]
