import { type } from 'arktype'

export const permisos = type({
  idacceso: 'number',
  idconsultor: 'number',
  nombrecompleto: 'string',
  idsistema: 'number',
  sistemanombre: 'string',
  idpermiso: 'number',
  etiqpermiso: 'string'
})

export type Permisos = typeof permisos.infer

export const permisosMetadata: {
  key: keyof Permisos
  label: string
}[] = [
  {
    key: 'nombrecompleto',
    label: 'Nombre'
  },
  {
    key: 'etiqpermiso',
    label: 'Permiso'
  }
]
