import { type } from 'arktype'

export const sistemas = type({
  idsistema: 'number',
  sistemanombre: 'string',
  idcliente: 'number',
  idproveedor: 'number',
  componente: 'string',
  ambiente: 'string | null',
  nombrecliente: 'string?',
  nombreproveedor: 'string?',
  idrespseguridad: 'number',
  comunicaciones: 'string',
  caractpasswd: 'string | null',
  duracpasswd: 'number | null',
  mfa: 'boolean | null',
  bup: 'boolean | null',
  logs: 'string | null',
  monitoreo: 'string | null',
  sla: 'boolean | null',
  cifradocomuc: 'string | null'
})

export type Sistemas = typeof sistemas.infer

export const sistemasMetadata: {
  key: keyof Sistemas
  label: string
}[] = [
  {
    key: 'sistemanombre',
    label: 'Sistema Nombre'
  },
  {
    key: 'componente',
    label: 'Componente'
  },
  {
    key: 'ambiente',
    label: 'Ambiente'
  },
  {
    key: 'idcliente',
    label: 'Cliente'
  },
  {
    key: 'idproveedor',
    label: 'Proveedor'
  },
  {
    key: 'comunicaciones',
    label: 'Comunicar'
  }
]

export const sistemasAccesosMetadata: {
  key: keyof Sistemas
  label: string
}[] = [
  {
    key: 'sistemanombre',
    label: 'Sistema Nombre'
  },
  {
    key: 'idcliente',
    label: 'Cliente'
  },
  {
    key: 'componente',
    label: 'Componente'
  },
  {
    key: 'ambiente',
    label: 'Ambiente'
  }
]
