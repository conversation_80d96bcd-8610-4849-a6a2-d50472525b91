export const useAccesosStore = defineStore('accesos', () => {
  const idProyecto = ref<number | null>(null)
  const idSistema = ref<number | null>(null)

  const isProyecto = computed(() => idProyecto.value !== null)
  const isSistema = computed(() => idSistema.value !== null)

  const setProyectoId = (value: number | null) => {
    idProyecto.value = value ?? 0
  }
  const setSistemaId = (value: number | null) => {
    idSistema.value = value ?? 0
  }

  return {
    isProyecto,
    isSistema,
    idProyecto,
    idSistema,
    setProyectoId,
    setSistemaId
  }
})
