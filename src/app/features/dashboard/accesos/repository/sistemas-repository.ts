import type { Siste<PERSON> } from '@d/accesos/models/sistemas'

const route = '/accesos/sistemas/sistemas'

export const getSistemas = () => $fetch<Sistemas[]>(route)

export const createSistema = (data: Omit<Sistemas, 'idsistema'>) =>
  $fetch<Sistemas>(route, { method: 'POST', body: data })

export const updateSistema = (idSistema: number, data: Partial<Sistemas>) =>
  $fetch<Sistemas>(route, { method: 'PUT', body: data, params: { idSistema } })

export const deleteSistema = (idSistema: number) =>
  $fetch<Sistemas>(route, { method: 'DELETE', params: { idSistema } })
