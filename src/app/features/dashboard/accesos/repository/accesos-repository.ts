import type { Proyecto } from '@d/admin/models/proyecto'
import type { <PERSON><PERSON><PERSON> } from '@d/accesos/models/sistemas'
import type { Permisos } from '@d/accesos/models/permisos'

const route = '/accesos/accesos'

export const getProyectosPorPm = (consultorId: number) =>
  $fetch<Proyecto[]>(`${route}/proyectos-pm`, {
    params: { consultorId }
  })

export const getSistemaPorProyecto = (idProyecto: number) =>
  $fetch<Sistemas[]>(`${route}/sistema-proyecto`, {
    params: { idProyecto }
  })

export const getPermisos = () =>
  $fetch<{ idpermiso: number; etiqpermiso: string }[]>(`${route}/permisos`)

export const getPermisosPorSistema = (idSistema: number) =>
  $fetch<Permisos[]>(`${route}/permisos-sistemas`, {
    params: { idSistema }
  })

export const getConsultoresApellido = () =>
  $fetch<{ id: number; etiqueta: string }[]>(`${route}/consultores-apellido`)

export const insertAcceso = (
  idConsultor: number,
  idSistema: number,
  idPermiso: number
) =>
  $fetch(`${route}/insert-acceso`, {
    method: 'POST',
    body: { idConsultor, idSistema, idPermiso }
  })

export const deleteAcceso = (idacceso: number) =>
  $fetch(`${route}/delete-acceso`, {
    method: 'DELETE',
    body: { idacceso }
  })
