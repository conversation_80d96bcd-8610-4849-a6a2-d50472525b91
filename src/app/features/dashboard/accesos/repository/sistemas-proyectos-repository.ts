import type { SistemaProyectos } from '@d/accesos/models/sistemas-proyectos'

const route = '/accesos/sistemas-proyectos/sistemas-proyectos'

export const getSistemaProyectos = () => $fetch<SistemaProyectos[]>(route)

export const createSistemaProyecto = (data: {
  idsistema: number
  idproyecto: number
}) => $fetch(route, { method: 'POST', body: data })

export const updateSistemaProyecto = (
  id: number,
  data: { idsistema: number; idproyecto: number }
) => $fetch(route, { method: 'PUT', body: data, params: { id } })

export const deleteSistemaProyecto = (id: number) =>
  $fetch(route, { method: 'DELETE', params: { id } })
