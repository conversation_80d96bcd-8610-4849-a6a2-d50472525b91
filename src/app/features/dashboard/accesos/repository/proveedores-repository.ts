import type { Proveedores } from '@d/accesos/models/proveedores'

const route = '/accesos/proveedores/proveedores'

export const getProveedores = () => $fetch<Proveedores[]>(route)

export const createProveedor = (data: Omit<Proveedores, 'idprov'>) =>
  $fetch<Proveedores>(route, { method: 'POST', body: data })

export const updateProveedor = (idProv: number, data: Partial<Proveedores>) =>
  $fetch<Proveedores>(route, { method: 'PUT', body: data, params: { idProv } })

export const deleteProveedor = (idProv: number) =>
  $fetch<Proveedores>(route, { method: 'DELETE', params: { idProv } })
