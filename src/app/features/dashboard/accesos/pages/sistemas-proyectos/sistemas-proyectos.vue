<template>
  <div class="flex flex-row w-full justify-between">
    <h2 class="mr-auto text-2xl my-6 ml-1 font-medium">Sistemas/Proyectos</h2>
    <new-refresh
      :is-new="
        !isSistemaProyectosPending &&
        dataSistemaProyectos &&
        dataSistemaProyectos.length > 0
      "
      @new="
        () => {
          selectedRow = null
          openDialog = true
        }
      "
      @refresh="refetchSistemaProyectos"
    />
  </div>
  <div class="intro-y box">
    <fetching
      :is-fetching="isSistemaProyectosPending"
      :data="dataSistemaProyectos"
      class="px-2 py-1"
      :display-data="filteredData"
    >
      <div
        class="flex flex-col items-center p-5 border-b sm:flex-row border-slate-200/60 justify-between"
      >
        <filter-table
          :data="dataSistemaProyectos ?? []"
          :metadata="sistemasProyectosMetadata"
          @filtered-data="data => (filteredData = data)"
        />
      </div>
      <generic-table
        :data="filteredData"
        :entries="sistemasProyectos"
        :metadata="sistemasProyectosMetadata"
        identity-key="idrelacion"
        is-delete-action
        is-edit-action
        @edit-click="
          el => {
            selectedRow = el
            openDialog = true
          }
        "
        @delete-click="el => mutateDeleteSistemaProyectos(el.idrelacion)"
      />
    </fetching>
  </div>
  <slide
    :open="openDialog"
    :item="selectedRow"
    title=" Sistema/Proyecto:"
    @submit="onSubmit"
  >
    <div
      v-if="isProyectoPending || isSistemaPending"
      class="flex flex-warp p-5 gap-3 w-full justify-center"
    >
      <loading icon="puff" class="mx-auto" />
    </div>
    <div v-else class="flex flex-col p-5 gap-5">
      <s-select
        v-model="selectedSistema"
        label="Sistemas:"
        default-text="Seleccionar Sistemas"
        identity-key="idsistema"
        value-key="idsistema"
        :display-text="row => row.sistemanombre"
        :data="distinctSistemas"
      />
      <s-select
        v-model="selectedProyecto"
        label="Proyectos:"
        default-text="Seleccionar Proyectos"
        identity-key="idproyecto"
        value-key="idproyecto"
        :display-text="row => row.nombreproyecto"
        :data="dataProyecto ?? []"
      />
    </div>
  </slide>
</template>

<!-- eslint-disable @typescript-eslint/no-unnecessary-condition -->
<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-redundant-type-constituents -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import {
  type SistemaProyectos,
  sistemasProyectos,
  sistemasProyectosMetadata
} from '@d/accesos/models/sistemas-proyectos'
import {
  createSistemaProyecto,
  deleteSistemaProyecto,
  getSistemaProyectos,
  updateSistemaProyecto
} from '@d/accesos/repository/sistemas-proyectos-repository'
import { getSistemas } from '@d/accesos/repository/sistemas-repository'
import { getProyectos } from '@d/admin/repository/tareas-repository'
import GenericTable from '@d/common/components/generic-table/generic-table.vue'
import Fetching from '@d/common/components/reuse/fetching.vue'
import NewRefresh from '@d/common/components/reuse/new-refresh.vue'
import SSelect from '@d/common/components/reuse/select.vue'
import Slide from '@d/common/components/reuse/slide.vue'
import { useAlertMutate } from '@d/common/composables/use-alert-mutate'
import { useQuery } from '@tanstack/vue-query'
import { piped, uniqBy } from 'rambdax'

import Loading from '@/common/components/loading.vue'
import FilterTable from '@/dashboard/common/components/reuse/filter-table.vue'

const openDialog = ref(false)
const selectedRow = ref<SistemaProyectos | null>(null)
const selectedProyecto = ref('')
const selectedSistema = ref('')

const {
  data: dataSistemaProyectos,
  isFetching: isSistemaProyectosPending,
  refetch: refetchSistemaProyectos
} = useQuery({
  queryFn: getSistemaProyectos,
  queryKey: ['accesos-sistemas-proyectos']
})

const { data: dataSistema, isFetching: isSistemaPending } = useQuery({
  queryFn: getSistemas,
  queryKey: ['accesos-sistemas-proyectos-sistema']
})

const { data: dataProyecto, isFetching: isProyectoPending } = useQuery({
  queryFn: getProyectos,
  queryKey: ['accesos-sistemas-proyectos-proyecto']
})

const filteredData = ref(dataSistemaProyectos.value ?? [])

const {
  isPending: isCreatingSistemaProyectos,
  mutate: mutateCreateSistemaProyectos
} = useAlertMutate({
  mutationFn: (sis: { proyecto: number; sistema: number }) =>
    createSistemaProyecto({
      idproyecto: sis.proyecto,
      idsistema: sis.sistema
    })
})

const {
  isPending: isUpdatingSistemaProyectos,
  mutate: mutateUpdateSistemaProyectos
} = useAlertMutate({
  mutationFn: (sis: { proyecto: number; sistema: number }) =>
    updateSistemaProyecto(selectedRow.value?.idrelacion ?? 0, {
      idproyecto: sis.proyecto,
      idsistema: sis.sistema
    })
})

const {
  isPending: isDeletingSistemaProyectos,
  mutate: mutateDeleteSistemaProyectos
} = useAlertMutate({
  mutationFn: deleteSistemaProyecto,
  refetchSecondary: refetchSistemaProyectos
})

const _ = computed(
  () =>
    isSistemaProyectosPending ||
    (dataSistemaProyectos.value && isCreatingSistemaProyectos) ||
    (dataSistemaProyectos.value && isUpdatingSistemaProyectos) ||
    (dataSistemaProyectos.value && isDeletingSistemaProyectos)
)

const onSubmit = (isSubmit: boolean) => {
  openDialog.value = false
  if (!isSubmit) return
  piped(
    {
      proyecto: Number(selectedProyecto.value),
      sistema: Number(selectedSistema.value)
    },
    res =>
      selectedRow.value
        ? mutateUpdateSistemaProyectos(res)
        : mutateCreateSistemaProyectos(res)
  )
}

watch(selectedRow, value => {
  if (value != null) {
    selectedProyecto.value = value.idproyecto.toString()
    selectedSistema.value = value.idsistema.toString()
  }
})

watch(isSistemaPending, () => {
  if (dataSistema.value && selectedRow.value != null) {
    selectedSistema.value =
      dataSistema.value?.find(
        sistema => sistema.idsistema === selectedRow.value?.idsistema
      )?.sistemanombre ?? ''
  }
})

watch(isProyectoPending, () => {
  if (dataProyecto.value && selectedRow.value != null) {
    selectedProyecto.value =
      dataProyecto.value?.find(
        proyecto => proyecto.idproyecto === selectedRow.value?.idproyecto
      )?.nombreproyecto ?? ''
  }
})

const distinctSistemas = computed(() =>
  dataSistema.value ? uniqBy(x => x.idsistema, dataSistema.value) : []
)
</script>
