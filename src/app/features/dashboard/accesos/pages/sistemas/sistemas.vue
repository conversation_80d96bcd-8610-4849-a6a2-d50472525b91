<template>
  <div class="flex flex-row w-full justify-between">
    <h2 class="mr-auto text-2xl my-6 ml-1 font-medium">Sistemas</h2>
    <new-refresh
      :is-new="!isSistemaPending && dataSistema && dataSistema.length > 0"
      @new="
        () => {
          selectedRow = null
          openDialog = true
        }
      "
      @refresh="refetchSistema"
    />
  </div>
  <div class="intro-y box">
    <fetching
      :is-fetching="isSistemaPending"
      :data="dataSistema"
      class="px-2 py-1"
      :display-data="filteredData"
    >
      <div
        class="flex flex-col items-center p-5 border-b sm:flex-row border-slate-200/60 justify-between"
      >
        <filter-table
          :data="dataSistema ?? []"
          :metadata="sistemasMetadata"
          @filtered-data="data => (filteredData = data)"
        />
      </div>
      <generic-table
        :data="filteredData"
        :entries="sistemas"
        :metadata="sistemasMetadata"
        identity-key="idsistema"
        is-delete-action
        is-edit-action
        @edit-click="
          el => {
            selectedRow = el
            openDialog = true
          }
        "
        @delete-click="el => mutateDeleteSistema(el.idsistema)"
      />
    </fetching>
  </div>
  <slide
    :open="openDialog"
    :item="selectedRow"
    title="sistema"
    @submit="onSubmit"
  >
    <div
      v-if="isClientePending || isProveedorPending"
      class="flex flex-warp p-5 gap-3 w-full justify-center"
    >
      <loading icon="puff" class="mx-auto" />
    </div>
    <div v-else class="flex flex-col p-5 gap-5">
      <s-input
        v-model="formReactive.sistemaNombre"
        label=" Sistema Nombre:"
        placeholder="Nombre del Sistema"
      />
      <s-select
        v-model="selectedCliente"
        label="Clientes:"
        default-text="Seleccionar Clientes"
        identity-key="idcliente"
        value-key="idcliente"
        :display-text="row => row.nombrecliente"
        :data="dataCliente ?? []"
      />
      <s-select
        v-model="selectedProveedor"
        label="Proveedores:"
        default-text="Seleccionar Proveedores"
        identity-key="idprov"
        value-key="idprov"
        :display-text="row => row.nombreproveedor"
        :data="dataProveedor ?? []"
      />
      <s-select
        v-model="formReactive.componente"
        label="Componente:"
        default-text="Seleccionar Componente"
        identity-key="componente"
        value-key="componente"
        :display-text="row => row.componente"
        :data="
          ['APP', 'BBDD', 'SO', 'VPN', 'VDI', 'RDP', 'WEBEX'].map(row => ({
            componente: row
          }))
        "
      />
      <s-select
        v-model="formReactive.ambiente"
        label="Ambiente:"
        default-text="Seleccionar Ambiente"
        identity-key="ambiente"
        value-key="ambiente"
        :display-text="row => row.ambiente"
        :data="['PROD', 'DEV', 'UAT'].map(row => ({ ambiente: row }))"
      />
      <s-input
        v-model="formReactive.comunicaciones"
        label="Comunicaciones:"
        placeholder="Comunicaciones"
      />
      <s-input
        v-model="formReactive.caractpasswd"
        label="Caract Passwd:"
        placeholder="Caract Passwd"
      />
      <s-check v-model="formReactive.mfa" label="Mfa:" />
      <s-check v-model="formReactive.bup" label="Bup:" />
      <s-input v-model="formReactive.logs" label="Logs:" placeholder="Logs" />
      <s-input
        v-model="formReactive.monitoreo"
        label="Monitoreo:"
        placeholder="Monitoreo"
      />
      <s-check
        :value="formReactive.sla === 'Si'"
        label="Sla:"
        @input="
          e =>
            (formReactive.sla = (e.target as HTMLInputElement).checked
              ? 'Si'
              : 'No')
        "
      />
      <s-input
        v-model="formReactive.cifradocomuc"
        label="Cifrado Comuc:"
        placeholder="Cifrado Comuc"
      />
    </div>
  </slide>
</template>

<!-- eslint-disable @typescript-eslint/no-unnecessary-condition -->
<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-redundant-type-constituents -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import {
  type Sistemas,
  sistemas,
  sistemasMetadata
} from '@d/accesos/models/sistemas'
import { getClientes } from '@d/accesos/repository/clientes-repository'
import { getProveedores } from '@d/accesos/repository/proveedores-repository'
import {
  createSistema,
  deleteSistema,
  getSistemas,
  updateSistema
} from '@d/accesos/repository/sistemas-repository'
import GenericTable from '@d/common/components/generic-table/generic-table.vue'
import SCheck from '@d/common/components/reuse/check.vue'
import Fetching from '@d/common/components/reuse/fetching.vue'
import SInput from '@d/common/components/reuse/input.vue'
import NewRefresh from '@d/common/components/reuse/new-refresh.vue'
import SSelect from '@d/common/components/reuse/select.vue'
import Slide from '@d/common/components/reuse/slide.vue'
import { useAlertMutate } from '@d/common/composables/use-alert-mutate'
import { useQuery } from '@tanstack/vue-query'
import { piped } from 'rambdax'

import Loading from '@/common/components/loading.vue'
import { useLoginStore } from '@/common/stores/login-store'
import FilterTable from '@/dashboard/common/components/reuse/filter-table.vue'

const openDialog = ref(false)
const loginStore = useLoginStore()
const selectedRow = ref<Sistemas | null>(null)
const selectedCliente = ref('')
const selectedProveedor = ref('')
const formReactive = reactive({
  ambiente: 'PROD',
  bup: false,
  caractpasswd: '',
  cifradocomuc: '',
  componente: 'APP',
  comunicaciones: '',
  duracpasswd: '',
  logs: 'No',
  mfa: false,
  monitoreo: 'No',
  sistemaNombre: '',
  sla: ''
})

const {
  data: dataSistema,
  isFetching: isSistemaPending,
  refetch: refetchSistema
} = useQuery({
  queryFn: getSistemas,
  queryKey: ['accesos-sistemas']
})

const { data: dataCliente, isFetching: isClientePending } = useQuery({
  queryFn: getClientes,
  queryKey: ['accesos-sistemas-clientes']
})

const { data: dataProveedor, isFetching: isProveedorPending } = useQuery({
  queryFn: getProveedores,
  queryKey: ['accesos-sistemas-proveedores']
})

const filteredData = ref(dataSistema.value ?? [])

const { isPending: isCreatingSistema, mutate: mutateCreateSistema } =
  useAlertMutate({
    mutationFn: (sis: Omit<Sistemas, 'idsistema'>) => createSistema(sis)
  })

const { isPending: isUpdatingSistema, mutate: mutateUpdateSistema } =
  useAlertMutate({
    mutationFn: (sis: Omit<Sistemas, 'idsistema'>) =>
      updateSistema(selectedRow.value?.idsistema ?? 0, sis)
  })

const { isPending: isDeletingSistema, mutate: mutateDeleteSistema } =
  useAlertMutate({
    mutationFn: deleteSistema,
    refetchSecondary: refetchSistema
  })

const _ = computed(
  () =>
    isSistemaPending ||
    isCreatingSistema ||
    isUpdatingSistema ||
    isDeletingSistema
)

const onSubmit = (isSubmit: boolean) => {
  openDialog.value = false
  if (!isSubmit) return
  piped(
    {
      ambiente: formReactive.ambiente,
      bup: formReactive.bup,
      caractpasswd: formReactive.caractpasswd,
      cifradocomuc: formReactive.cifradocomuc,
      componente: formReactive.componente,
      comunicaciones: formReactive.comunicaciones,
      duracpasswd: Number(formReactive.duracpasswd),
      idcliente: Number(selectedCliente.value),
      idproveedor: Number(selectedProveedor.value),
      idrespseguridad: loginStore.user?.id ?? 4,
      logs: formReactive.logs,
      mfa: formReactive.mfa,
      monitoreo: formReactive.monitoreo,
      sistemanombre: formReactive.sistemaNombre,
      sla: formReactive.sla === 'Si'
    },
    res =>
      selectedRow.value ? mutateUpdateSistema(res) : mutateCreateSistema(res)
  )
}

watch(selectedRow, value => {
  if (value != null) {
    formReactive.sistemaNombre = value.sistemanombre
    formReactive.componente = value.componente
    formReactive.ambiente = value.ambiente ?? 'PROD'
    formReactive.comunicaciones = value.comunicaciones
    formReactive.caractpasswd = value.caractpasswd ?? ''
    formReactive.duracpasswd = value.duracpasswd?.toString() ?? ''
    formReactive.mfa = value.mfa ?? false
    formReactive.bup = value.bup ?? false
    formReactive.logs = value.logs ?? 'No'
    formReactive.monitoreo = value.monitoreo ?? 'No'
    formReactive.sla = value.sla ? 'Si' : 'No'
    formReactive.cifradocomuc = value.cifradocomuc ?? ''
    selectedCliente.value = value.nombrecliente ?? ''
    selectedProveedor.value = value.nombreproveedor ?? ''
  }
})
</script>
