<!-- eslint-disable @typescript-eslint/no-unnecessary-condition -->
<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-redundant-type-constituents -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import {
  type Proveedores,
  proveedores,
  proveedoresMetadata
} from '@d/accesos/models/proveedores'
import {
  createProveedor,
  deleteProveedor,
  getProveedores,
  updateProveedor
} from '@d/accesos/repository/proveedores-repository'
import GenericTable from '@d/common/components/generic-table/generic-table.vue'
import Fetching from '@d/common/components/reuse/fetching.vue'
import SInput from '@d/common/components/reuse/input.vue'
import NewRefresh from '@d/common/components/reuse/new-refresh.vue'
import Slide from '@d/common/components/reuse/slide.vue'
import { useAlertMutate } from '@d/common/composables/use-alert-mutate'
import { useQuery } from '@tanstack/vue-query'
import { piped } from 'rambdax'

import FilterTable from '@/dashboard/common/components/reuse/filter-table.vue'

const openDialog = ref(false)
const selectedRow = ref<Proveedores | null>(null)
const formReactive = reactive({ nombreproveedor: '', pais: 0 })

const {
  data: dataProveedor,
  isFetching: isProveedorPending,
  refetch: refetchProveedor
} = useQuery({
  queryFn: getProveedores,
  queryKey: ['accesos-proveedores']
})

const filteredData = ref(dataProveedor.value ?? [])

const { isPending: isCreatingProveedor, mutate: mutateCreateProveedor } =
  useAlertMutate({
    mutationFn: (sis: Omit<Proveedores, 'idprov'>) => createProveedor(sis)
  })

const { isPending: isUpdatingProveedor, mutate: mutateUpdateProveedor } =
  useAlertMutate({
    mutationFn: (sis: Omit<Proveedores, 'idprov'>) =>
      updateProveedor(selectedRow.value?.idprov ?? 0, sis)
  })

const { isPending: isDeletingProveedor, mutate: mutateDeleteProveedor } =
  useAlertMutate({
    mutationFn: deleteProveedor,
    refetchSecondary: refetchProveedor
  })

const _ = computed(
  () =>
    isProveedorPending ||
    isCreatingProveedor ||
    isUpdatingProveedor ||
    isDeletingProveedor
)

const onSubmit = (isSubmit: boolean) => {
  openDialog.value = false
  if (!isSubmit) return
  piped(
    {
      nombreproveedor: formReactive.nombreproveedor,
      pais: formReactive.pais
    },
    res =>
      selectedRow.value
        ? mutateUpdateProveedor(res)
        : mutateCreateProveedor(res)
  )
}

watch(selectedRow, value => {
  if (value != null) {
    formReactive.nombreproveedor = value.nombreproveedor
    formReactive.pais = value.pais
  }
})
</script>

<template>
  <div class="flex flex-row w-full justify-between">
    <h2 class="mr-auto text-2xl my-6 ml-1 font-medium">Proveedores</h2>
    <new-refresh
      :is-new="!isProveedorPending && dataProveedor && dataProveedor.length > 0"
      @new="
        () => {
          selectedRow = null
          openDialog = true
        }
      "
      @refresh="refetchProveedor"
    />
  </div>
  <div class="intro-y box">
    <fetching
      :is-fetching="isProveedorPending"
      :data="dataProveedor"
      class="px-2 py-1"
      :display-data="filteredData"
    >
      <div
        class="flex flex-col items-center p-5 border-b sm:flex-row border-slate-200/60 justify-between"
      >
        <filter-table
          :data="dataProveedor ?? []"
          :metadata="proveedoresMetadata"
          @filtered-data="data => (filteredData = data)"
        />
      </div>
      <generic-table
        :data="filteredData"
        :entries="proveedores"
        :metadata="proveedoresMetadata"
        identity-key="idprov"
        is-delete-action
        is-edit-action
        @edit-click="
          el => {
            selectedRow = el
            openDialog = true
          }
        "
        @delete-click="el => mutateDeleteProveedor(el.idprov)"
      />
    </fetching>
  </div>
  <slide
    :open="openDialog"
    :item="selectedRow"
    title="Proveedor"
    @submit="onSubmit"
  >
    <div class="flex flex-col p-5 gap-5">
      <s-input
        v-model="formReactive.nombreproveedor"
        label="Proveedor"
        placeholder="Nombre del Proveedor"
      />
      <s-input v-model="formReactive.pais" label="Pais" placeholder="Pais" />
    </div>
  </slide>
</template>
