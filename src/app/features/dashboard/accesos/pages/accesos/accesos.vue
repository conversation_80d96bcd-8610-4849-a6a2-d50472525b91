<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup>
import Accesos from './components/accesos.vue'
import Proyectos from './components/proyectos.vue'
import Sistemas from './components/sistemas.vue'
</script>

<template>
  <div>
    <div class="flex items-center mt-8 intro-y">
      <h2 class="mr-auto text-2xl font-medium">Accesos</h2>
    </div>
    <div class="grid grid-cols-12 gap-6 mt-5 intro-y">
      <div class="col-span-full">
        <div class="justify-center flex flex-row gap-3">
          <Proyectos />
          <Sistemas />
          <Accesos />
        </div>
      </div>
    </div>
  </div>
</template>
