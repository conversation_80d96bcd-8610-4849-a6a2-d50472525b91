<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { getProyectosPorPm } from '@d/accesos/repository/accesos-repository'
import { useAccesosStore } from '@d/accesos/stores/accesos-store'
import { accesosProyectoMetadata, proyecto } from '@d/admin/models/proyecto'
import GenericTable from '@d/common/components/generic-table/generic-table.vue'
import Fetching from '@d/common/components/reuse/fetching.vue'
import { useQuery } from '@tanstack/vue-query'

import { useLoginStore } from '@/common/stores/login-store'
import FilterTable from '@/dashboard/common/components/reuse/filter-table.vue'

const store = useAccesosStore()
const loginStore = useLoginStore()

const {
  data: dataProyecto,
  isFetching: isProyectoPending,
  refetch: refetchProyecto
} = useQuery({
  queryFn: () => getProyectosPorPm(loginStore.user?.consultorid ?? 0),
  queryKey: ['accesos-accesos-proyectos']
})

const lengthCalc = computed(() => {
  if (store.isProyecto) return 'w-1/2'
  if (store.isProyecto && store.isSistema) return 'w-1/3'
  return 'w-full'
})

const filteredData = ref(dataProyecto.value ?? [])
</script>

<template>
  <div :class="`intro-y box ${lengthCalc}`">
    <div
      class="flex flex-wrap p-5 gap-3 border-b border-slate-200/60 justify-between"
    >
      <h2 class="text-lg font-medium">Proyectos que Gestiono</h2>
      <div class="flex gap-3">
        <filter-table
          :data="dataProyecto ?? []"
          :metadata="accesosProyectoMetadata"
          @filtered-data="data => (filteredData = data)"
        />
        <new-refresh
          @refresh="
            () => {
              refetchProyecto()
              store.setProyectoId(null)
              store.setSistemaId(null)
            }
          "
        />
      </div>
    </div>
    <fetching
      :is-fetching="isProyectoPending"
      :data="dataProyecto"
      :display-data="filteredData"
    >
      <generic-table
        :data="filteredData"
        :entries="proyecto"
        :metadata="accesosProyectoMetadata"
        identity-key="idproyecto"
        is-selected
        @row-click="el => store.setProyectoId(el.idproyecto)"
      />
    </fetching>
  </div>
</template>
