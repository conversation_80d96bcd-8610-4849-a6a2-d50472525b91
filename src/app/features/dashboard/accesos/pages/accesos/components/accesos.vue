<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { permisos, permisosMetadata } from '@d/accesos/models/permisos'
import {
  deleteAcceso,
  getPermisosPorSistema
} from '@d/accesos/repository/accesos-repository'
import { useAccesosStore } from '@d/accesos/stores/accesos-store'
import GenericTable from '@d/common/components/generic-table/generic-table.vue'
import Fetching from '@d/common/components/reuse/fetching.vue'
import NewRefresh from '@d/common/components/reuse/new-refresh.vue'
import { useAlertMutate } from '@d/common/composables/use-alert-mutate'
import { useQuery } from '@tanstack/vue-query'

const accesosStore = useAccesosStore()

const {
  data: dataAccesos,
  isFetching: isAccesosPending,
  refetch: refetchAccesos
} = useQuery({
  enabled: computed(() => accesosStore.isSistema),
  queryFn: () => getPermisosPorSistema(accesosStore.idSistema ?? 0),
  queryKey: ['accesos-accesos-accesos', accesosStore.idSistema]
})

const { isPending: _, mutate: mutateDeleteAccesos } = useAlertMutate({
  mutationFn: deleteAcceso
})

watch(
  () => accesosStore.idSistema,
  sistema => {
    if (sistema !== null) void refetchAccesos()
  }
)
</script>

<template>
  <div v-if="accesosStore.isSistema" class="intro-y box w-1/3">
    <div
      class="flex flex-wrap p-5 gap-3 border-b border-slate-200/60 justify-between"
    >
      <h2 class="text-lg font-medium">Accesos</h2>
      <new-refresh @refresh="refetchAccesos" />
    </div>
    <fetching
      :is-fetching="isAccesosPending"
      :data="dataAccesos"
      :display-data="dataAccesos ?? []"
    >
      <generic-table
        :data="dataAccesos ?? []"
        :entries="permisos"
        is-delete-action
        :metadata="permisosMetadata"
        identity-key="idacceso"
        @delete-click="el => mutateDeleteAccesos(el.idacceso)"
      />
    </fetching>
  </div>
</template>
