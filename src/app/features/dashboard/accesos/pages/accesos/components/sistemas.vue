<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import VButton from '@c/components/button/button.vue'
import { sistemas, sistemasAccesosMetadata } from '@d/accesos/models/sistemas'
import {
  getConsultoresApellido,
  getPermisos,
  getSistemaPorProyecto,
  insertAcceso
} from '@d/accesos/repository/accesos-repository'
import { useAccesosStore } from '@d/accesos/stores/accesos-store'
import GenericTable from '@d/common/components/generic-table/generic-table.vue'
import Fetching from '@d/common/components/reuse/fetching.vue'
import SInput from '@d/common/components/reuse/input.vue'
import NewRefresh from '@d/common/components/reuse/new-refresh.vue'
import SSelect from '@d/common/components/reuse/select.vue'
import Slide from '@d/common/components/reuse/slide.vue'
import VTable from '@d/common/components/table/Table.vue'
import VTbody from '@d/common/components/table/Tbody.vue'
import VTd from '@d/common/components/table/Td.vue'
import VTr from '@d/common/components/table/Tr.vue'
import { useAlertMutate } from '@d/common/composables/use-alert-mutate'
import { useQuery } from '@tanstack/vue-query'

import Loading from '@/common/components/loading.vue'

const openDialog = ref(false)
const selectedConsultor = ref(0)
const selectedPermiso = ref(0)
const accesosStore = useAccesosStore()
const sisnombre = ref('')
const sisid = ref<number | null>(null)

const {
  data: dataSistemas,
  isFetching: isSistemasPending,
  refetch: refetchSistemas
} = useQuery({
  enabled: computed(() => accesosStore.isProyecto),
  queryFn: () => getSistemaPorProyecto(accesosStore.idProyecto ?? 0),
  queryKey: ['accesos-accesos-sistemas', accesosStore.idProyecto]
})

const { data: dataConsultor, isFetching: isConsultorPending } = useQuery({
  queryFn: getConsultoresApellido,
  queryKey: ['create-edit-consultor-consultores']
})

const { data: dataPermiso, isFetching: isPermisoPending } = useQuery({
  queryFn: getPermisos,
  queryKey: ['create-edit-consultor-permisos']
})

const { isPending: __, mutate: _ } = useAlertMutate({
  mutationFn: () =>
    insertAcceso(
      selectedConsultor.value,
      sisid.value ?? 0,
      selectedPermiso.value
    )
})

watch(
  () => accesosStore.idProyecto,
  proyecto => {
    if (proyecto !== null) {
      void refetchSistemas()
      sisid.value = null
    }
  }
)
</script>

<template>
  <div
    v-if="accesosStore.isProyecto"
    :class="`intro-y box ${accesosStore.isSistema ? 'w-1/3' : 'w-1/2'} `"
  >
    <div
      class="flex flex-wrap p-5 gap-3 border-b border-slate-200/60 justify-between"
    >
      <h2 class="text-lg font-medium">Sistemas del Proyecto</h2>
      <new-refresh
        :is-new="!isSistemasPending && dataSistemas && dataSistemas.length > 0"
        @new="
          () => {
            openDialog = true
            sisid = null
          }
        "
        @refresh="
          () => {
            accesosStore.setSistemaId(null)
            refetchSistemas()
          }
        "
      />
    </div>
    <fetching
      :is-fetching="isSistemasPending"
      :data="dataSistemas"
      :display-data="dataSistemas ?? []"
    >
      <generic-table
        :data="dataSistemas ?? []"
        :entries="sistemas"
        :metadata="sistemasAccesosMetadata"
        identity-key="idsistema"
        is-selected
        @row-click="
          el => {
            sisid = el.idsistema
            sisnombre = el.sistemanombre
            accesosStore.setSistemaId(el?.idsistema)
          }
        "
      >
        <v-button
          variant="primary"
          class="w-36 mr-1"
          :disabled="!sisid"
          @click="() => (openDialog = true)"
        >
          Editar Consultor
        </v-button>
      </generic-table>
    </fetching>
  </div>
  <slide
    :open="openDialog"
    :item="sisid == null ? null : { idsistema: sisid }"
    title="Consultor"
    @submit="() => (openDialog = false)"
  >
    <div
      v-if="isConsultorPending || isPermisoPending"
      class="flex flex-warp p-5 gap-3 w-full justify-center"
    >
      <loading icon="puff" class="mx-auto" />
    </div>
    <div v-else class="flex flex-col p-5 gap-5">
      <s-input
        v-model="sisnombre"
        label="Sistema:"
        placeholder="Sistema"
        readonly
      />
      <div>Consultores:</div>
      <div
        class="flex flex-row justify-between gap-3 overflow-auto max-h-[300px]"
      >
        <v-table hover sm>
          <v-tbody>
            <v-tr
              v-for="el in dataConsultor"
              :key="el.id"
              :class="`dark:hover:bg-[#303761] hover:bg-[#dadef7] ${
                selectedConsultor === el.id
                  ? 'dark:bg-[#303761] bg-[#dadef7]'
                  : ''
              }`"
              @click="() => (selectedConsultor = el.id)"
            >
              <v-td>{{ el.etiqueta }}</v-td>
            </v-tr>
          </v-tbody>
        </v-table>
      </div>
      <s-select
        v-model:number="selectedPermiso"
        label="Permiso:"
        default-text="Seleccionar Permiso"
        identity-key="idpermiso"
        value-key="idpermiso"
        :display-text="row => row.etiqpermiso"
        :data="dataPermiso ?? []"
      />
    </div>
  </slide>
</template>
