import type { Tareas } from '@d/consultas/models/tareas'
import type { Consulta } from '@d/consultas/models/consulta'
import type { Consultor } from '@d/consultas/models/consultor'

const route = '/consultas'

export const getConsultor = (consultorId = 4) =>
  $fetch<Consultor[]>(`${route}/consultor`, {
    params: { consultorId }
  })

export const getTareas = (consultorId = 4) =>
  $fetch<Tareas[]>(`${route}/tareas`, {
    params: { consultorId }
  })

export const getConsulta = (
  fecha1: string,
  fecha2: string,
  consultorId: string,
  tarea: string,
  jefeId = 4
) =>
  $fetch<Consulta[]>(`${route}/consulta`, {
    params: { fecha1, fecha2, consultorId, tarea, jefeId }
  })
