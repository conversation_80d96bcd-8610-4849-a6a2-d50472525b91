import { type } from 'arktype'

export const consulta = type({
  fecha: 'string',
  nombre: 'string',
  horastrabajadas: 'number?',
  descripcion: 'string?',
  proyecto: 'string?',
  nombretarea: 'string?'
})

export const consultaMetadata = [
  {
    key: 'fecha',
    label: 'Fecha'
  },
  {
    key: 'nombre',
    label: 'Nombre'
  },
  {
    key: 'horastrabajadas',
    label: 'Horas Trabajadas'
  },
  {
    key: 'descripcion',
    label: 'Descripción'
  },
  {
    key: 'nombretarea',
    label: 'Nombre Tarea'
  },
  {
    key: 'proyecto',
    label: 'Proyecto'
  }
]

export type Consulta = typeof consulta.infer
