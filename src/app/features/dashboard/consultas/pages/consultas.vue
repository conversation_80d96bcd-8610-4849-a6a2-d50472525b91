<!-- eslint-disable @typescript-eslint/no-redundant-type-constituents -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'
import Table from '@d/common/components/table/Table.vue'
import TBody from '@d/common/components/table/Tbody.vue'
import Td from '@d/common/components/table/Td.vue'
import Th from '@d/common/components/table/Th.vue'
import Thead from '@d/common/components/table/Thead.vue'
import Tr from '@d/common/components/table/Tr.vue'
import { consultaMetadata } from '@d/consultas/models/consulta'
import type { Consultor } from '@d/consultas/models/consultor'
import type { Tareas } from '@d/consultas/models/tareas'
import {
  getConsulta,
  getConsultor,
  getTareas
} from '@d/consultas/repository/consultas-repository'
import { useQuery } from '@tanstack/vue-query'

import FormSelect from '@/common/components/form/FormSelect.vue'
import Loading from '@/common/components/loading.vue'
import { useLoginStore } from '@/common/stores/login-store'

const loginStore = useLoginStore()
const fechaInicio = ref('')
const fechaFin = ref('')

const selectedTarea = reactive<{
  model: string
  tarea: Tareas | null
}>({ model: '', tarea: null })

const selectedConsultor = reactive<{
  consultor: Consultor | null
  model: string
}>({ consultor: null, model: '' })

const { data: dataTarea, isFetching: isTareaPending } = useQuery({
  queryFn: () => getTareas(loginStore.user?.id ?? 4),
  queryKey: ['consultas-tarea']
})

const { data: dataConsultor, isFetching: isConsultorPending } = useQuery({
  queryFn: () => getConsultor(loginStore.user?.id ?? 4),
  queryKey: ['consultas-consultor']
})

const {
  data: dataConsulta,
  isFetching: isConsultaPending,
  refetch: refetchConsulta
} = useQuery({
  enabled: computed(
    () => selectedTarea.tarea !== null && selectedConsultor.consultor !== null
  ),
  queryFn: () =>
    getConsulta(
      fechaInicio.value,
      fechaFin.value,
      selectedConsultor.consultor?.id.toString() ?? '',
      selectedTarea.tarea?.tarea ?? '',
      loginStore.user?.id ?? 0
    ),
  queryKey: ['consultas-consulta']
})

const refetchConsultaClick = () => {
  if (selectedTarea.tarea !== null && selectedConsultor.consultor !== null) {
    void refetchConsulta()
  }
}
</script>

<template>
  <div>
    <div class="flex items-center mt-8 mb-3 intro-y">
      <h2 class="mr-auto text-2xl font-medium">Consultas</h2>
    </div>
    <div class="intro-y box">
      <div
        class="flex flex-col items-center p-5 border-b sm:flex-row border-slate-200/60 justify-between"
      >
        <div
          v-if="isTareaPending && isConsultorPending"
          class="flex flex-col w-full justify-center items-center"
        >
          <Loading icon="three-dots" />
        </div>
        <div v-else class="flex flex-row w-full gap-3 justify-between">
          <div class="flex flex-row gap-5">
            <p class="mt-2">Fecha Inicial:</p>
            <div class="relative w-40">
              <div
                class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
              >
                <LucideCalendar class="w-4 h-4" />
              </div>
              <ClientOnly>
                <Litepicker
                  v-model="fechaInicio"
                  class="pl-12"
                  :options="{
                    autoApply: true,
                    dropdowns: {
                      minYear: 2020,
                      maxYear: 2035,
                      months: true,
                      years: true
                    }
                  }"
                  @update:model-value="refetchConsultaClick"
                />
              </ClientOnly>
            </div>
          </div>
          <div class="flex flex-row gap-5">
            <p class="mt-2">Fecha Final:</p>
            <div class="relative w-40">
              <div
                class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
              >
                <LucideCalendar class="w-4 h-4" />
              </div>
              <ClientOnly>
                <Litepicker
                  v-model="fechaFin"
                  class="pl-12"
                  :options="{
                    autoApply: true,
                    dropdowns: {
                      minYear: 2020,
                      maxYear: 2035,
                      months: true,
                      years: true
                    }
                  }"
                  @update:model-value="refetchConsultaClick"
                />
              </ClientOnly>
            </div>
          </div>
          <FormSelect v-model="selectedTarea.model" class="w-3/12">
            <option value="" @click="() => (selectedTarea.tarea = null)">
              Seleccionar Tarea
            </option>
            <option
              v-for="(rowTarea, index) in dataTarea"
              :key="`${rowTarea.tarea} ${index}`"
              :value="rowTarea.tarea"
              @click="
                () => {
                  selectedTarea.tarea = rowTarea
                  refetchConsultaClick()
                }
              "
            >
              {{ rowTarea.tarea }}
            </option>
          </FormSelect>
          <FormSelect v-model="selectedConsultor.model" class="w-2/12">
            <option
              value=""
              @click="() => (selectedConsultor.consultor = null)"
            >
              Seleccionar Consultor
            </option>
            <option
              v-for="(rowConsultor, index) in dataConsultor"
              :key="`${rowConsultor.id} ${index}`"
              :value="rowConsultor.id"
              @click="
                () => {
                  selectedConsultor.consultor = rowConsultor
                  refetchConsultaClick()
                }
              "
            >
              {{ rowConsultor.nombre }}
            </option>
          </FormSelect>
        </div>
      </div>
      <template
        v-if="
          selectedTarea.tarea !== null && selectedConsultor.consultor !== null
        "
      >
        <div
          v-if="isConsultaPending"
          class="flex flex-col items-center m-5 justify-end col-span-6 sm:col-span-3 xl:col-span-2"
        >
          <Loading icon="puff" />
        </div>
        <div
          v-else-if="
            !isConsultaPending && dataConsulta && dataConsulta.length > 0
          "
          class="p-5"
        >
          <div class="overflow-auto max-h-[360px]">
            <Table hover sm>
              <Thead variant="dark">
                <Tr>
                  <Th
                    v-for="row in consultaMetadata"
                    :key="row.key"
                    class="whitespace-nowrap font-bold"
                  >
                    {{ row.label }}
                  </Th>
                </Tr>
              </Thead>
              <TBody>
                <Tr
                  v-for="(el, index) in dataConsulta"
                  :key="`${el.nombre} - ${index}`"
                  class="dark:hover:bg-[#303761] hover:bg-[#dadef7]"
                >
                  <Td>{{ el.fecha }}</Td>
                  <Td>{{ el.nombre }}</Td>
                  <Td
                    :class="
                      el.horastrabajadas === null ? 'text-slate-500 italic' : ''
                    "
                  >
                    {{
                      el.horastrabajadas === null ? 'Vacío' : el.horastrabajadas
                    }}
                  </Td>
                  <Td
                    :class="
                      el.descripcion === null ? 'text-slate-500 italic' : ''
                    "
                  >
                    {{ el.descripcion === null ? 'Vacío' : el.descripcion }}
                  </Td>
                  <Td
                    :class="
                      el.nombretarea === null ? 'text-slate-500 italic' : ''
                    "
                  >
                    {{ el.nombretarea === null ? 'Vacío' : el.nombretarea }}
                  </Td>
                  <Td
                    :class="el.proyecto === null ? 'text-slate-500 italic' : ''"
                  >
                    {{ el.proyecto === null ? 'Vacío' : el.proyecto }}
                  </Td>
                </Tr>
              </TBody>
            </Table>
          </div>
          <div class="flex flex-row w-full justify-end">
            <div class="mt-6 flex flex-row gap-3">
              <Button variant="success" :disabled="dataConsulta.length === 0">
                <LucideTable class="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
        <div
          v-else-if="
            !isConsultaPending && dataConsulta && dataConsulta.length === 0
          "
          class="p-10"
        >
          <h3 class="text-center font-bold text-xl">
            No hay datos disponibles para mostrar
          </h3>
        </div>
      </template>
    </div>
  </div>
</template>
