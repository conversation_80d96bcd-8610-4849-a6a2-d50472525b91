export const informeRoutes = [
  {
    path: '/dash/informes/alertas',
    component: () => import('@d/informes/pages/alertas/alertas.vue'),
    meta: { breadcrumb: 'Alertas' }
  },
  {
    path: '/dash/informes/completitud',
    component: () => import('@d/informes/pages/completitud/completitud.vue'),
    meta: { breadcrumb: 'Completitud' }
  },
  {
    path: '/dash/informes/control-recursos',
    component: () =>
      import('@d/informes/pages/control-recursos/control-recursos.vue'),
    meta: { breadcrumb: 'Control Recursos' }
  },
  {
    path: '/dash/informes/horas',
    component: () => import('@d/informes/pages/horas/horas.vue'),
    meta: { breadcrumb: 'Horas' }
  },
  {
    path: '/dash/informes/vacaciones',
    component: () => import('@d/informes/pages/vacaciones/vacaciones.vue'),
    meta: { breadcrumb: 'Vacaciones' }
  },
  {
    path: '/dash/informes/varios',
    component: () => import('@d/informes/pages/varios/varios.vue'),
    meta: { breadcrumb: 'Validaciones' }
  }
]
