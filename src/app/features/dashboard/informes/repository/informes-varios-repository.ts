const route = '/informes/varios'

export const getInformeListadoConsultores = (isExcel: boolean) =>
  $fetch<BlobPart>(`${route}/listado-consultores`, {
    responseType: 'blob',
    params: { isExcel }
  })
    .then(
      res =>
        new Blob([res], {
          type: isExcel
            ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            : 'application/pdf'
        })
    )
    .then(URL.createObjectURL)

export const getInformeConsultoresProyecto = (isExcel: boolean) =>
  $fetch<BlobPart>(`${route}/consultores-proyecto`, {
    responseType: 'blob',
    params: { isExcel }
  })
    .then(
      res =>
        new Blob([res], {
          type: isExcel
            ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            : 'application/pdf'
        })
    )
    .then(URL.createObjectURL)
