import type { HorasRegistradasAlert } from '@d/informes/models/horas-registradas-alert'
import type { HorasPendientesAlert } from '@d/informes/models/horas-pendientes-alert'
import type { ConsultoresExcluidos } from '@d/informes/models/consultores-excluidos'

const route = '/informes/alertas'

export const getHorasRegistradasAlert = (
  fechainicio: string,
  fechafin: string,
  horasmin: number,
  isExcel = false
) =>
  $fetch<BlobPart | HorasRegistradasAlert[]>(`${route}/horas-registradas`, {
    responseType: isExcel ? 'blob' : 'json',
    params: { fechainicio, fechafin, horasmin, isExcel }
  })
    .then(res => {
      if (isExcel) {
        new Blob([res as BlobPart], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
      }

      return res
    })
    .then(res => {
      if (isExcel) return URL.createObjectURL(res as Blob)
      return res
    })

export const getHorasPendientesAlert = (
  fechainicio: string,
  fechafin: string,
  horasmin: number
) =>
  $fetch<HorasPendientesAlert[]>(`${route}/horas-pendientes`, {
    params: { fechainicio, fechafin, horasmin }
  })

export const sendMail = (mails: string[], subject: string, text: string) =>
  $fetch(`${route}/mail`, { method: 'POST', body: { mails, subject, text } })

export const getConsultoresExcluidos = () =>
  $fetch<ConsultoresExcluidos[]>(`${route}/consultores-excluidos`)
