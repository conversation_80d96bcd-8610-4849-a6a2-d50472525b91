import type { Proyecto } from '@d/admin/models/proyecto'

const route = '/informes/horas'

export const getConsultores = () =>
  $fetch<{ id: number; etiqueta: string }[]>(`${route}/consultores`)

export const getProyectos = (nombrecliente: string) =>
  $fetch<Proyecto[]>(`${route}/proyecto-cliente`, {
    params: { nombrecliente }
  })

export const getInformePorConsultor = (
  idconsultor: number,
  fechainicio: string,
  fechafin: string,
  isExcel: boolean
) =>
  $fetch<BlobPart>(`${route}/por-consultor`, {
    responseType: 'blob',
    params: { idconsultor, fechainicio, fechafin, isExcel }
  })
    .then(
      res =>
        new Blob([res], {
          type: isExcel
            ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            : 'application/pdf'
        })
    )
    .then(URL.createObjectURL)

export const getInformePorProyecto = (
  nombreproyecto: string,
  fechainicio: string,
  fechafin: string,
  isExcel: boolean
) =>
  $fetch<BlobPart>(`${route}/por-proyecto`, {
    responseType: 'blob',
    params: { nombreproyecto, fechainicio, fechafin, isExcel }
  })
    .then(
      res =>
        new Blob([res], {
          type: isExcel
            ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            : 'application/pdf'
        })
    )
    .then(URL.createObjectURL)

export const getInformePorCliente = (
  nombrecliente: string,
  fechainicio: string,
  fechafin: string,
  isExcel: boolean
) =>
  $fetch<BlobPart>(`${route}/por-cliente`, {
    responseType: 'blob',
    params: { nombrecliente, fechainicio, fechafin, isExcel }
  })
    .then(
      res =>
        new Blob([res], {
          type: isExcel
            ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            : 'application/pdf'
        })
    )
    .then(URL.createObjectURL)

export const getInformeEntreFechas = (
  fechainicio: string,
  fechafin: string,
  isExcel: boolean
) =>
  $fetch<BlobPart>(`${route}/entre-fechas`, {
    responseType: 'blob',
    params: { fechainicio, fechafin, isExcel }
  })
    .then(
      res =>
        new Blob([res], {
          type: isExcel
            ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            : 'application/pdf'
        })
    )
    .then(URL.createObjectURL)
