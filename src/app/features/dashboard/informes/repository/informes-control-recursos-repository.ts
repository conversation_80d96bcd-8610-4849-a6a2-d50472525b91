import type { HorasConsultor } from '@d/informes/models/horas-consultor'
import type { ProyectoInterno } from '@d/informes/models/proyecto-interno'

const route = '/informes/control-recursos'

export const getInformeProyectoInterno = (
  fechainicio: string,
  fechafin: string,
  isExcel = false
) =>
  $fetch<BlobPart | ProyectoInterno[]>(`${route}/proyecto-interno`, {
    responseType: isExcel ? 'blob' : 'json',
    params: { fechainicio, fechafin, isExcel }
  })
    .then(res => {
      if (isExcel) {
        new Blob([res as BlobPart], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
      }

      return res
    })
    .then(res => {
      if (isExcel) return URL.createObjectURL(res as Blob)
      return res
    })

export const getInformeHorasConsultor = (
  fechainicio: string,
  fechafin: string,
  consultorid: number,
  isExcel = false
) =>
  $fetch<BlobPart | HorasConsultor[]>(`${route}/horas-consultor`, {
    responseType: isExcel ? 'blob' : 'json',
    params: { fechainicio, fechafin, consultorid, isExcel }
  })
    .then(res => {
      if (isExcel) {
        new Blob([res as BlobPart], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
      }

      return res
    })
    .then(res => {
      if (isExcel) return URL.createObjectURL(res as Blob)
      return res
    })
