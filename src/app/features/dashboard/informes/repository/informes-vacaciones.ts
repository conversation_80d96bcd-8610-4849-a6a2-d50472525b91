const route = '/informes/vacaciones'

export const getInformeVacacionesSolicitadas = (
  fechainicio: string,
  fechafin: string
) =>
  $fetch<BlobPart>(`${route}/vacaciones-solicitadas`, {
    responseType: 'blob',
    params: { fechainicio, fechafin }
  })
    .then(
      res =>
        new Blob([res], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
    )
    .then(URL.createObjectURL)

export const getInformeVacacionesConsumidas = () =>
  $fetch<BlobPart>(`${route}/vacaciones-consumidas`, {
    responseType: 'blob'
  })
    .then(
      res =>
        new Blob([res], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
    )
    .then(URL.createObjectURL)

export const refreshVacacionesConsumidas = () =>
  $fetch(`${route}/refresh-vac-consumidas`, {
    method: 'POST'
  })
