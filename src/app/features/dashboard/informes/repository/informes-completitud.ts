const route = '/informes/completitud'

export const getInformeHorasInsuficientes = (
  fechainicio: string,
  fechafin: string,
  limite: number
) =>
  $fetch<BlobPart>(`${route}/horas-insuficientes`, {
    responseType: 'blob',
    params: { fechainicio, fechafin, limite }
  })
    .then(
      res =>
        new Blob([res], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
    )
    .then(URL.createObjectURL)

export const getInformeHorasInsuficientesJF = (
  fechainicio: string,
  fechafin: string,
  jf: number
) =>
  $fetch<BlobPart>(`${route}/horas-insuficientes-jf`, {
    responseType: 'blob',
    params: { fechainicio, fechafin, jf }
  })
    .then(
      res =>
        new Blob([res], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
    )
    .then(URL.createObjectURL)

export const getInformeHorasProyecto = (
  fechainicio: string,
  fechafin: string,
  consultorid: number
) =>
  $fetch<BlobPart>(`${route}/horas-proyecto`, {
    responseType: 'blob',
    params: { fechainicio, fechafin, consultorid }
  })
    .then(
      res =>
        new Blob([res], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
    )
    .then(URL.createObjectURL)

export const getInformeHorasPendientes = (
  fechainicio: string,
  fechafin: string
) =>
  $fetch<BlobPart>(`${route}/horas-pendientes`, {
    responseType: 'blob',
    params: { fechainicio, fechafin }
  })
    .then(
      res =>
        new Blob([res], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
    )
    .then(URL.createObjectURL)

export const getInformeHorasRegistradas = (
  fechainicio: string,
  fechafin: string
) =>
  $fetch<BlobPart>(`${route}/horas-registradas`, {
    responseType: 'blob',
    params: { fechainicio, fechafin }
  })
    .then(
      res =>
        new Blob([res], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
    )
    .then(URL.createObjectURL)
