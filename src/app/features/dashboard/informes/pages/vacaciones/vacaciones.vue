<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import { useAlertStore } from '@c/stores/alert-store'
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'
import {
  getInformeVacacionesConsumidas,
  getInformeVacacionesSolicitadas,
  refreshVacacionesConsumidas
} from '@d/informes/repository/informes-vacaciones'
import { useMutation, useQuery } from '@tanstack/vue-query'
import { piped, tap } from 'rambdax'

import Loading from '@/common/components/loading.vue'

const reactiveData = reactive({ fechafin: '', fechainicio: '' })
const doDownload1 = ref(false)
const doDownload2 = ref(false)
const alertStore = useAlertStore()

const downloadDoc = (url: string) =>
  piped(
    document.createElement('a'),
    tap(a => (a.href = url)),
    tap(a => (a.download = 'vacaciones-solicitadas.xlsx')),
    tap(a => document.body.append(a)),
    tap(a => a.click()),
    tap(a => a.remove()),
    tap(() => URL.revokeObjectURL(url))
  )

const {
  isFetching: isVacacionesSolicitadasPending,
  refetch: refetchVacacionesSolicitadas
} = useQuery({
  enabled: computed(() => doDownload1.value),
  queryFn: () =>
    getInformeVacacionesSolicitadas(
      reactiveData.fechainicio,
      reactiveData.fechafin
    ).then(downloadDoc),
  queryKey: ['informe-vacaciones-solicitadas-xlsx']
})

const {
  isFetching: isVacacionesConsumidasPending,
  refetch: refetchVacacionesConsumidas
} = useQuery({
  enabled: computed(() => doDownload2.value),
  queryFn: () => getInformeVacacionesConsumidas().then(downloadDoc),
  queryKey: ['informe-vacaciones-consumidas-xlsx']
})

const {
  isPending: isRefreshingVacacionesConsumidas,
  mutate: mutateRefreshVacacionesConsumidas
} = useMutation({
  mutationFn: () => refreshVacacionesConsumidas(),
  onError: () => alertStore.showErrorAlert(),
  onSuccess: () => alertStore.showSuccessAlert()
})

watch(isVacacionesSolicitadasPending, value => (doDownload1.value = value))
watch(isVacacionesConsumidasPending, value => (doDownload2.value = value))
</script>

<template>
  <div>
    <div class="flex items-center mt-8 mb-3 intro-y">
      <h2 class="mr-auto text-2xl font-medium">Vacaciones</h2>
    </div>
    <div class="intro-y box py-5">
      <div class="flex flex-row gap-3 justify-evenly">
        <div class="flex flex-col">
          <label> Fecha Inicio: </label>
          <div class="relative">
            <div
              class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
            >
              <LucideCalendar class="w-4 h-4" />
            </div>
            <Litepicker
              v-model="reactiveData.fechainicio"
              class="pl-12"
              :options="{
                autoApply: false,
                showWeekNumbers: true,
                dropdowns: {
                  minYear: 2020,
                  maxYear: 2035,
                  months: true,
                  years: true
                }
              }"
            />
          </div>
        </div>
        <div class="flex flex-col">
          <label> Fecha Fin: </label>
          <div class="relative">
            <div
              class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
            >
              <LucideCalendar class="w-4 h-4" />
            </div>
            <Litepicker
              v-model="reactiveData.fechafin"
              class="pl-12"
              :options="{
                autoApply: false,
                showWeekNumbers: true,
                dropdowns: {
                  minYear: 2020,
                  maxYear: 2035,
                  months: true,
                  years: true
                }
              }"
            />
          </div>
        </div>
      </div>
      <div class="flex flex-row gap-3 justify-evenly w-full mt-9 mb-6">
        <Button
          variant="success"
          class="w-64"
          :disabled="isVacacionesSolicitadasPending"
          @click="() => refetchVacacionesSolicitadas()"
        >
          <Loading
            v-if="isVacacionesSolicitadasPending"
            icon="three-dots"
            color="#000"
            class="mr-2 w-4 h-4"
          />
          <LucideSheet v-else class="w-4 h-4 mr-2" /> Informe Vac. Solicitadas
        </Button>
        <Button
          variant="success"
          class="w-64"
          :disabled="isVacacionesConsumidasPending"
          @click="() => refetchVacacionesConsumidas()"
        >
          <Loading
            v-if="isVacacionesConsumidasPending"
            icon="three-dots"
            color="#000"
            class="mr-2 w-4 h-4"
          />
          <LucideSheet v-else class="w-4 h-4 mr-2" /> Informe Vac. Consumidas
        </Button>
        <Button
          variant="primary"
          class="w-64"
          :disabled="isRefreshingVacacionesConsumidas"
          @click="() => mutateRefreshVacacionesConsumidas()"
        >
          <Loading
            v-if="isRefreshingVacacionesConsumidas"
            icon="three-dots"
            color="#fff"
            class="mr-2 w-4 h-4"
          />
          <span v-else>Refrescar Vac. Consumidas</span>
        </Button>
      </div>
    </div>
  </div>
</template>
