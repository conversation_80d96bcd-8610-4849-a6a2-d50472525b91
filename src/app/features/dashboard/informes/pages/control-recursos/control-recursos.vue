<script setup lang="ts">
import TabButton from '@d/common/components/tabs/Button.vue'
import TabGroup from '@d/common/components/tabs/Group.vue'
import TabList from '@d/common/components/tabs/List.vue'
import TabPanel from '@d/common/components/tabs/Panel.vue'
import TabPanels from '@d/common/components/tabs/Panels.vue'
import Tab from '@d/common/components/tabs/Tab/Tab.vue'

import HorasConsultor from './components/horas-consultor.vue'
import ProyectoInterno from './components/proyecto-interno.vue'
</script>

<template>
  <div>
    <div class="flex items-center mt-8 mb-3 intro-y">
      <h2 class="mr-auto text-2xl font-medium">Control Recursos</h2>
    </div>
    <div class="intro-y box">
      <TabGroup class="m-5">
        <TabList variant="boxed-tabs">
          <Tab>
            <TabButton class="w-full py-2" as="button">
              Horas Dedicadas Proyecto Interno
            </TabButton>
          </Tab>
          <Tab>
            <TabButton class="w-full py-2" as="button">
              Horas de un Consultor
            </TabButton>
          </Tab>
        </TabList>
        <TabPanels class="border-b border-l border-r">
          <TabPanel class="p-5 leading-relaxed">
            <ProyectoInterno />
          </TabPanel>
          <TabPanel class="p-5 leading-relaxed">
            <HorasConsultor />
          </TabPanel>
        </TabPanels>
      </TabGroup>
    </div>
  </div>
</template>
