<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'
import Table from '@d/common/components/table/Table.vue'
import TBody from '@d/common/components/table/Tbody.vue'
import Td from '@d/common/components/table/Td.vue'
import Th from '@d/common/components/table/Th.vue'
import Thead from '@d/common/components/table/Thead.vue'
import Tr from '@d/common/components/table/Tr.vue'
import {
  type ProyectoInterno,
  proyectoInternoMetadata
} from '@d/informes/models/proyecto-interno'
import { getInformeProyectoInterno } from '@d/informes/repository/informes-control-recursos-repository'
import { useQuery } from '@tanstack/vue-query'
import { piped, tap } from 'rambdax'

import Loading from '@/common/components/loading.vue'

const reactiveData = reactive({ fechafin: '', fechainicio: '' })
const doDownload = ref(false)
const { $dayjs } = useNuxtApp()
const {
  data: dataProyectoInternoInforme,
  isFetching: isProyectoInternoInformePending,
  refetch: refetchProyectoInternoInforme
} = useQuery({
  queryFn: () =>
    getInformeProyectoInterno(
      reactiveData.fechainicio === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechainicio,
      reactiveData.fechafin === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechafin
    ).then(data => {
      console.log(data)
      return data as ProyectoInterno[]
    }),
  queryKey: ['informes-proyecto-interno']
})

const {
  isFetching: isProyectoInternoInformeXlsxPending,
  refetch: refetchProyectoInternoInformeXlsx
} = useQuery({
  enabled: computed(() => doDownload.value),
  queryFn: () =>
    getInformeProyectoInterno(
      reactiveData.fechainicio === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechainicio,
      reactiveData.fechafin === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechafin,
      true
    ).then(url =>
      piped(
        document.createElement('a'),
        tap(a => (a.href = url as string)),
        tap(a => (a.download = 'informe-horas-proyecto-interno.xlsx')),
        tap(a => document.body.append(a)),
        tap(a => a.click()),
        tap(a => a.remove()),
        tap(() => URL.revokeObjectURL(url as string))
      )
    ),
  queryKey: ['informe-proyecto-interno-xlsx']
})

watch(isProyectoInternoInformeXlsxPending, value => (doDownload.value = value))
</script>

<template>
  <div class="intro-y box py-5">
    <div class="flex flex-row gap-3 justify-evenly">
      <div class="flex flex-col">
        <label> Fecha Inicio: </label>
        <div class="relative">
          <div
            class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
          >
            <LucideCalendar class="w-4 h-4" />
          </div>
          <Litepicker
            v-model="reactiveData.fechainicio"
            class="pl-12"
            :options="{
              autoApply: false,
              showWeekNumbers: true,
              dropdowns: {
                minYear: 2020,
                maxYear: 2035,
                months: true,
                years: true
              }
            }"
            @change="() => refetchProyectoInternoInforme()"
          />
        </div>
      </div>
      <div class="flex flex-col">
        <label> Fecha Fin: </label>
        <div class="relative">
          <div
            class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
          >
            <LucideCalendar class="w-4 h-4" />
          </div>
          <Litepicker
            v-model="reactiveData.fechafin"
            class="pl-12"
            :options="{
              autoApply: false,
              showWeekNumbers: true,
              dropdowns: {
                minYear: 2020,
                maxYear: 2035,
                months: true,
                years: true
              }
            }"
            @change="() => refetchProyectoInternoInforme()"
          />
        </div>
      </div>
    </div>
    <div
      v-if="isProyectoInternoInformePending"
      class="flex flex-col items-center m-5 justify-end col-span-6 sm:col-span-3 xl:col-span-2"
    >
      <Loading icon="three-dots" />
    </div>
    <div
      v-else-if="
        !isProyectoInternoInformePending &&
        dataProyectoInternoInforme &&
        dataProyectoInternoInforme.length > 0
      "
      class="p-5"
    >
      <div class="overflow-auto max-h-[360px]">
        <Table hover sm>
          <Thead variant="dark">
            <Tr>
              <Th
                v-for="row in proyectoInternoMetadata"
                :key="row.key"
                class="whitespace-nowrap font-bold"
              >
                {{ row.label }}
              </Th>
            </Tr>
          </Thead>
          <TBody>
            <Tr
              v-for="el in dataProyectoInternoInforme as ProyectoInterno[]"
              :key="el.id"
            >
              <Td>{{ el.id }}</Td>
              <Td>{{ el.nombreconsultor }}</Td>
              <Td>{{ el.tarea }}</Td>
              <Td>{{ el.horas }}</Td>
              <Td>{{ el.htotal }}</Td>
              <Td>{{ el.email }}</Td>
              <Td>{{ el.emailjefefuncional }}</Td>
              <Td>{{ el.fechaingreso }}</Td>
            </Tr>
          </TBody>
        </Table>
      </div>
    </div>
    <div
      v-else-if="
        !isProyectoInternoInformePending &&
        dataProyectoInternoInforme &&
        dataProyectoInternoInforme.length === 0
      "
      class="p-10"
    >
      <h3 class="text-center font-bold text-xl">
        No hay datos disponibles para mostrar
      </h3>
    </div>
    <div
      v-if="
        !isProyectoInternoInformePending &&
        dataProyectoInternoInforme &&
        dataProyectoInternoInforme.length > 0
      "
      class="mt-6 flex flex-row w-full justify-end"
    >
      <Button
        :disabled="isProyectoInternoInformeXlsxPending"
        variant="success"
        class="w-72 mr-1"
        @click="() => refetchProyectoInternoInformeXlsx()"
      >
        <Loading
          v-if="isProyectoInternoInformeXlsxPending"
          icon="three-dots"
          color="#000"
          class="mr-2 w-4 h-4"
        />
        <LucideSheet v-else class="w-4 h-4 mr-2" /> Informe Horas Registradas
      </Button>
    </div>
  </div>
</template>
