<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import { getConsultores } from '@d/admin/repository/consultores-repository'
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'
import Table from '@d/common/components/table/Table.vue'
import TBody from '@d/common/components/table/Tbody.vue'
import Td from '@d/common/components/table/Td.vue'
import Th from '@d/common/components/table/Th.vue'
import Thead from '@d/common/components/table/Thead.vue'
import Tr from '@d/common/components/table/Tr.vue'
import {
  type HorasConsultor,
  horasConsultorMetadata
} from '@d/informes/models/horas-consultor'
import { getInformeHorasConsultor } from '@d/informes/repository/informes-control-recursos-repository'
import { useQuery } from '@tanstack/vue-query'
import { piped, tap } from 'rambdax'

import FormInput from '@/common/components/form/FormInput.vue'
import FormSelect from '@/common/components/form/FormSelect.vue'
import Loading from '@/common/components/loading.vue'
import { useNuxtApp } from '#app'

const doDownload = ref(false)
const { $dayjs } = useNuxtApp()

const selectedConsultor = reactive<{
  id: number | null
  model: string
}>({ id: null, model: '' })

const { data: dataConsultor, isFetching: isConsultorPending } = useQuery({
  queryFn: getConsultores,
  queryKey: ['informes-horas-consultor-ctrl']
})

const reactiveData = reactive({
  fechafin: '',
  fechainicio: '',
  idconsultor: ''
})

const {
  data: dataHorasConsultorInforme,
  isFetching: isHorasConsultorInformePending,
  refetch: refetchHorasConsultorInforme
} = useQuery({
  enabled: computed(() => selectedConsultor.model !== ''),
  queryFn: () =>
    getInformeHorasConsultor(
      reactiveData.fechainicio === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechainicio,
      reactiveData.fechafin === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechafin,
      selectedConsultor.id ?? 0
    ).then(data => {
      console.log(data)
      return data as HorasConsultor[]
    }),
  queryKey: ['informes-consultor']
})

const {
  isFetching: isHorasConsultorInformeXlsxPending,
  refetch: refetchHorasConsultorInformeXlsx
} = useQuery({
  enabled: computed(() => doDownload.value),
  queryFn: () =>
    getInformeHorasConsultor(
      reactiveData.fechainicio === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechainicio,
      reactiveData.fechafin === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechafin,
      selectedConsultor.id ?? 0,
      true
    ).then(url =>
      piped(
        document.createElement('a'),
        tap(a => (a.href = url as string)),
        tap(a => (a.download = 'informe-horas-consultor.xlsx')),
        tap(a => document.body.append(a)),
        tap(a => a.click()),
        tap(a => a.remove()),
        tap(() => URL.revokeObjectURL(url as string))
      )
    ),
  queryKey: ['informe-consultor-xlsx']
})

watch(isHorasConsultorInformeXlsxPending, value => (doDownload.value = value))
</script>

<template>
  <div class="intro-y box py-5">
    <div
      v-if="isConsultorPending"
      class="flex flex-col items-center m-5 justify-end col-span-6 sm:col-span-3 xl:col-span-2"
    >
      <Loading icon="three-dots" />
    </div>
    <div
      v-else-if="!isConsultorPending && dataConsultor"
      class="flex flex-row gap-3 justify-between"
    >
      <div class="flex flex-col">
        <label> Fecha Inicio: </label>
        <div class="relative">
          <div
            class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
          >
            <LucideCalendar class="w-4 h-4" />
          </div>
          <Litepicker
            v-model="reactiveData.fechainicio"
            class="pl-12"
            :options="{
              autoApply: false,
              showWeekNumbers: true,
              dropdowns: {
                minYear: 2020,
                maxYear: 2035,
                months: true,
                years: true
              }
            }"
            @change="() => refetchHorasConsultorInforme()"
          />
        </div>
      </div>
      <div class="flex flex-col">
        <label> Fecha Fin: </label>
        <div class="relative">
          <div
            class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
          >
            <LucideCalendar class="w-4 h-4" />
          </div>
          <Litepicker
            v-model="reactiveData.fechafin"
            class="pl-12"
            :options="{
              autoApply: false,
              showWeekNumbers: true,
              dropdowns: {
                minYear: 2020,
                maxYear: 2035,
                months: true,
                years: true
              }
            }"
            @change="() => refetchHorasConsultorInforme()"
          />
        </div>
      </div>

      <div class="flex flex-col max-w-[600px]">
        <label class="cursor-pointer select-none mb-1"> Consultor: </label>
        <FormSelect v-model="selectedConsultor.model" class="w-full">
          <option value="" @click="selectedConsultor.model = ''">
            Seleccione ...
          </option>
          <option
            v-for="consultor in dataConsultor ?? []"
            :key="consultor.id"
            :value="consultor.id?.toString()"
            @click="
              () => {
                selectedConsultor.model = consultor?.usuario
                selectedConsultor.id = consultor?.id
                reactiveData.idconsultor = consultor?.id?.toString() ?? ''
                refetchHorasConsultorInforme()
              }
            "
          >
            {{ consultor.apellido }}, {{ consultor.nombre }}
          </option>
        </FormSelect>
      </div>
      <div class="flex flex-col mr-10">
        <p class="w-5/6">ID Consultor:</p>
        <FormInput
          v-model="reactiveData.idconsultor"
          class="block px-4 py-3"
          type="text"
          readonly
        />
      </div>
    </div>
    <div
      v-if="isHorasConsultorInformePending"
      class="flex flex-col items-center mt-8 mx-5 justify-end col-span-6 sm:col-span-3 xl:col-span-2"
    >
      <Loading icon="three-dots" />
    </div>
    <div
      v-else-if="
        !isHorasConsultorInformePending &&
        dataHorasConsultorInforme &&
        dataHorasConsultorInforme.length > 0
      "
      class="p-5"
    >
      <div class="overflow-auto max-h-[360px]">
        <Table hover sm>
          <Thead variant="dark">
            <Tr>
              <Th
                v-for="row in horasConsultorMetadata"
                :key="row.key"
                class="whitespace-nowrap font-bold"
              >
                {{ row.label }}
              </Th>
            </Tr>
          </Thead>
          <TBody>
            <Tr
              v-for="el in dataHorasConsultorInforme as HorasConsultor[]"
              :key="el.id"
            >
              <Td>{{ el.id }}</Td>
              <Td>{{ el.nombreconsultor }}</Td>
              <Td>{{ el.horas }}</Td>
              <Td>{{ el.fecha }}</Td>
              <Td>{{ el.tarea }}</Td>
              <Td>{{ el.descripcion }}</Td>
              <Td>{{ el.etapa }}</Td>
              <Td>{{ el.nombreproyecto }}</Td>
              <Td>{{ el.estado }}</Td>
            </Tr>
          </TBody>
        </Table>
      </div>
    </div>
    <div
      v-else-if="
        !isHorasConsultorInformePending &&
        dataHorasConsultorInforme &&
        dataHorasConsultorInforme.length === 0
      "
      class="pt-10 px-10"
    >
      <h3 class="text-center font-bold text-xl">
        No hay datos disponibles para mostrar
      </h3>
    </div>
    <div
      v-if="
        !isHorasConsultorInformePending &&
        dataHorasConsultorInforme &&
        dataHorasConsultorInforme.length > 0
      "
      class="mt-6 flex flex-row w-full justify-end"
    >
      <Button
        :disabled="isHorasConsultorInformeXlsxPending"
        variant="success"
        class="w-72 mr-1"
        @click="() => refetchHorasConsultorInformeXlsx()"
      >
        <Loading
          v-if="isHorasConsultorInformeXlsxPending"
          icon="three-dots"
          color="#000"
          class="mr-2 w-4 h-4"
        />
        <LucideSheet v-else class="w-4 h-4 mr-2" /> Informe Horas Registradas
      </Button>
    </div>
  </div>
</template>
