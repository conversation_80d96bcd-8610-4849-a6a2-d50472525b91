<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import { getConsultores } from '@d/admin/repository/consultor-tareas-repository'
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'
import {
  getInformeHorasInsuficientes,
  getInformeHorasInsuficientesJF,
  getInformeHorasPendientes,
  getInformeHorasProyecto,
  getInformeHorasRegistradas
} from '@d/informes/repository/informes-completitud'
import { useQuery } from '@tanstack/vue-query'
import { piped, tap } from 'rambdax'

import FormInput from '@/common/components/form/FormInput.vue'
import FormSelect from '@/common/components/form/FormSelect.vue'
import Loading from '@/common/components/loading.vue'
import { useLoginStore } from '@/common/stores/login-store'
import { useNuxtApp } from '#app'

const reactiveData = reactive({ fechafin: '', fechainicio: '' })
const doDownloadIns = ref(false)
const doDownloadJf = ref(false)
const doDownloadHorasProyecto = ref(false)
const doDownloadHorasPendientes = ref(false)
const doDownloadHorasRegistradas = ref(false)
const horasMinimas = ref(0)

const loginStore = useLoginStore()
const { $dayjs } = useNuxtApp()

const selectedConsultor = reactive<{
  id: number | null
  model: string
}>({ id: null, model: '' })

const downloadDoc = (url: string, name: string) =>
  piped(
    document.createElement('a'),
    tap(a => (a.href = url)),
    tap(a => (a.download = `${name}.xlsx`)),
    tap(a => document.body.append(a)),
    tap(a => a.click()),
    tap(a => a.remove()),
    tap(() => URL.revokeObjectURL(url))
  )

const {
  isFetching: isInformeHorasInsuficientesPending,
  refetch: refetchInformeHorasInsuficientes
} = useQuery({
  enabled: computed(() => doDownloadIns.value),
  queryFn: () =>
    getInformeHorasInsuficientes(
      reactiveData.fechainicio === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechainicio,
      reactiveData.fechafin === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechafin,
      horasMinimas.value
    ).then(url => downloadDoc(url, 'horas-insuficientes')),
  queryKey: ['informe-horas-ins-comp-xlsx']
})

const {
  isFetching: isInformeHorasInsuficientesPendingJf,
  refetch: refetchInformeHorasInsuficientesJf
} = useQuery({
  enabled: computed(() => doDownloadJf.value),
  queryFn: () =>
    getInformeHorasInsuficientesJF(
      reactiveData.fechainicio === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechainicio,
      reactiveData.fechafin === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechafin,
      loginStore.user?.consultorid ?? 0
    ).then(url => downloadDoc(url, 'horas-insuficientes-jf')),
  queryKey: ['informe-horas-ins-comp-jf-xlsx']
})

const { isFetching: isHorasProyectoPending, refetch: refetchHorasProyecto } =
  useQuery({
    enabled: computed(() => doDownloadHorasProyecto.value),
    queryFn: () =>
      getInformeHorasProyecto(
        reactiveData.fechainicio === ''
          ? $dayjs().format('YYYY-MM-DD')
          : reactiveData.fechainicio,
        reactiveData.fechafin === ''
          ? $dayjs().format('YYYY-MM-DD')
          : reactiveData.fechafin,

        selectedConsultor.id ?? 0
      )
        .then(url => downloadDoc(url, 'horas-proyecto'))
        .then(() => {
          console.log(reactiveData.fechainicio)
          console.log(reactiveData.fechafin)
        }),
    queryKey: ['informe-horas-proyecto-comp-xlsx']
  })

const {
  isFetching: isHorasPendientesPending,
  refetch: refetchHorasPendientes
} = useQuery({
  enabled: computed(() => doDownloadHorasPendientes.value),
  queryFn: () =>
    getInformeHorasPendientes(
      reactiveData.fechainicio === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechainicio,
      reactiveData.fechafin === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechafin
    ).then(url => downloadDoc(url, 'horas-pendientes')),
  queryKey: ['informe-horas-pendientes-comp-xlsx']
})

const {
  isFetching: isHorasRegistradasPending,
  refetch: refetchHorasRegistradas
} = useQuery({
  enabled: computed(() => doDownloadHorasRegistradas.value),
  queryFn: () =>
    getInformeHorasRegistradas(
      reactiveData.fechainicio === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechainicio,
      reactiveData.fechafin === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechafin
    ).then(url => downloadDoc(url, 'horas-registradas')),
  queryKey: ['informe-horas-registradas-comp-xlsx']
})

const { data: dataConsultor, isFetching: isConsultorPending } = useQuery({
  queryFn: () => getConsultores(),
  queryKey: ['informe-completitud-consultores']
})

watch(
  isInformeHorasInsuficientesPending,
  value => (doDownloadIns.value = value)
)
watch(
  isInformeHorasInsuficientesPendingJf,
  value => (doDownloadJf.value = value)
)
</script>

<template>
  <div>
    <div class="flex items-center mt-8 mb-3 intro-y">
      <h2 class="mr-auto text-2xl font-medium">Completitud</h2>
    </div>
    <div class="intro-y box py-5">
      <div class="flex flex-row gap-3 justify-evenly">
        <div class="flex flex-col">
          <label> Fecha Inicio: </label>
          <div class="relative">
            <div
              class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
            >
              <LucideCalendar class="w-4 h-4" />
            </div>
            <Litepicker
              v-model="reactiveData.fechainicio"
              class="pl-12"
              :options="{
                autoApply: false,
                showWeekNumbers: true,
                dropdowns: {
                  minYear: 2020,
                  maxYear: 2035,
                  months: true,
                  years: true
                }
              }"
            />
          </div>
        </div>
        <div class="flex flex-col">
          <label> Fecha Fin: </label>
          <div class="relative">
            <div
              class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
            >
              <LucideCalendar class="w-4 h-4" />
            </div>
            <Litepicker
              v-model="reactiveData.fechafin"
              class="pl-12"
              :options="{
                autoApply: false,
                showWeekNumbers: true,
                dropdowns: {
                  minYear: 2020,
                  maxYear: 2035,
                  months: true,
                  years: true
                }
              }"
            />
          </div>
        </div>
      </div>
      <div class="pt-4 px-6">
        <p class="font-bold text-lg">Horas Registradas</p>
        <div class="border-b mt-2" />
      </div>
      <div class="flex flex-row gap-3 justify-evenly w-full my-6">
        <Button
          variant="success"
          class="w-64"
          :disabled="isHorasRegistradasPending"
          @click="() => refetchHorasRegistradas()"
        >
          <Loading
            v-if="isHorasRegistradasPending"
            icon="three-dots"
            color="#000"
            class="mr-2 w-4 h-4"
          />
          <LucideSheet v-else class="w-4 h-4 mr-2" /> Informe Horas Registradas
        </Button>
      </div>
      <div class="pt-4 px-6">
        <p class="font-bold text-lg">Horas Pendientes de Aprobar</p>
        <div class="border-b mt-2" />
      </div>
      <div class="flex flex-row gap-3 justify-evenly w-full my-6">
        <Button
          variant="success"
          class="w-80"
          :disabled="isHorasPendientesPending"
          @click="() => refetchHorasPendientes()"
        >
          <Loading
            v-if="isHorasPendientesPending"
            icon="three-dots"
            color="#000"
            class="mr-2 w-4 h-4"
          />
          <LucideSheet v-else class="w-4 h-4 mr-2" /> Informe Horas Pendientes
          de Aprobar
        </Button>
      </div>
      <div class="pt-4 px-6">
        <p class="font-bold text-lg">Horas Insuficientes</p>
        <div class="border-b mt-2" />
      </div>
      <div class="flex flex-row gap-3 justify-evenly w-full my-6">
        <Button
          variant="success"
          class="w-96 h-10 mt-6"
          :disabled="isInformeHorasInsuficientesPending"
          @click="() => refetchInformeHorasInsuficientes()"
        >
          <Loading
            v-if="isInformeHorasInsuficientesPending"
            icon="three-dots"
            color="#000"
            class="mr-2 w-4 h-4"
          />
          <LucideSheet v-else class="w-4 h-4 mr-2" /> Informe Horas
          Insuficientes todos los cons.
        </Button>
        <Button
          variant="success"
          class="w-96 h-10 mt-6"
          :disabled="isInformeHorasInsuficientesPendingJf"
          @click="() => refetchInformeHorasInsuficientesJf()"
        >
          <Loading
            v-if="isInformeHorasInsuficientesPendingJf"
            icon="three-dots"
            color="#000"
            class="mr-2 w-4 h-4"
          />
          <LucideSheet v-else class="w-4 h-4 mr-2" /> Informe Horas
          Insuficientes mis cons.
        </Button>
        <div class="flex flex-col">
          <label class="cursor-pointer select-none mb-1">
            Horas Mínimas:
          </label>
          <FormInput
            v-model="horasMinimas"
            class="block px-4 py-3 w-24"
            placeholder="Horas Mínimas"
            type="number"
          />
        </div>
      </div>
      <div class="pt-4 px-6">
        <p class="font-bold text-lg">Horas Proyecto</p>
        <div class="border-b mt-2" />
      </div>
      <div
        v-if="isConsultorPending"
        class="box flex flex-col items-center py-4"
      >
        <Loading icon="puff" />
      </div>
      <div v-else class="flex flex-row gap-3 justify-evenly w-full my-6">
        <FormSelect v-model="selectedConsultor.model" class="w-2/12">
          <option value="" @click="selectedConsultor.model = ''">
            Seleccione Consultor ...
          </option>
          <option
            v-for="(rowConsultor, index) in dataConsultor"
            :key="`${rowConsultor.consultorid} ${index}`"
            :value="rowConsultor.consultorid"
            @click="
              () => {
                selectedConsultor.model = rowConsultor?.usuario
                selectedConsultor.id = rowConsultor?.consultorid ?? null
              }
            "
          >
            {{ rowConsultor.nombre }}
          </option>
        </FormSelect>
        <Button
          variant="success"
          class="w-64"
          :disabled="isHorasProyectoPending || selectedConsultor.model === ''"
          @click="() => refetchHorasProyecto()"
        >
          <Loading
            v-if="isHorasProyectoPending"
            icon="three-dots"
            color="#000"
            class="mr-2 w-4 h-4"
          />
          <LucideSheet v-else class="w-4 h-4 mr-2" /> Informe Horas Proyecto
        </Button>
      </div>
    </div>
  </div>
</template>
