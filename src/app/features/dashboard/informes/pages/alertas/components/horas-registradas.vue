<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import { useAlertStore } from '@c/stores/alert-store'
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'
import Table from '@d/common/components/table/Table.vue'
import TBody from '@d/common/components/table/Tbody.vue'
import Td from '@d/common/components/table/Td.vue'
import Th from '@d/common/components/table/Th.vue'
import Thead from '@d/common/components/table/Thead.vue'
import Tr from '@d/common/components/table/Tr.vue'
import {
  type HorasRegistradasAlert,
  horasRegistradasAlertMetadata
} from '@d/informes/models/horas-registradas-alert'
import {
  getHorasRegistradasAlert,
  sendMail
} from '@d/informes/repository/informes-alertas'
import { useMutation, useQuery } from '@tanstack/vue-query'
import { piped, tap } from 'rambdax'

import FormCheck from '@/common/components/form/FormCheck/FormCheck.vue'
import FormCheckInput from '@/common/components/form/FormCheck/Input.vue'
import FormInput from '@/common/components/form/FormInput.vue'
//import FormTextarea from '@/common/components/form/FormTextarea.vue'
import Loading from '@/common/components/loading.vue'
import { useNuxtApp } from '#app'

import ConsultoresExcluidos from './consultores-excluidos.vue'

const alertStore = useAlertStore()
const { $dayjs } = useNuxtApp()
const emails = ref<string[]>([])
const selectedRows = ref<HorasRegistradasAlert[]>([])
const emailsOnly = computed(() =>
  selectedRows.value.map(el => el.email).join(',')
)
const emailText = ref('')
const emailForm = reactive({ asunto: '', texto: '' })
const reactiveData = reactive({ fechafin: '', fechainicio: '', minHoras: 0 })
const openDialog = ref(false)
const doDownload = ref(false)

const {
  data: dataHorasRegistradasInforme,
  isFetching: isHorasRegistradasInformePending,
  refetch: refetchHorasRegistradasInforme
} = useQuery({
  queryFn: () =>
    getHorasRegistradasAlert(
      reactiveData.fechainicio === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechainicio,
      reactiveData.fechafin === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechafin,
      reactiveData.minHoras
    ),
  queryKey: ['informes-alert-horas-registradas']
})

const { isPending: isSendingMail, mutate: mutateSendMail } = useMutation({
  mutationFn: () =>
    sendMail(
      selectedRows.value.map(el => el.email ?? ''),
      emailForm.asunto,
      emailForm.texto
    ),
  onError: () => alertStore.showErrorAlert(),
  onSuccess: () => alertStore.showSuccessAlert()
})

const handleCheckboxChange = (event: Event, tarea: HorasRegistradasAlert) => {
  const isChecked = (event.target as HTMLInputElement).checked
  if (isChecked) {
    selectedRows.value.push(tarea)
  } else {
    selectedRows.value = selectedRows.value.filter(item => item.id !== tarea.id)
  }
}

const {
  isFetching: isHorasRegistradasInformeXlsxPending,
  refetch: refetchHorasRegistradasInformeXlsx
} = useQuery({
  enabled: computed(() => doDownload.value),
  queryFn: () =>
    getHorasRegistradasAlert(
      reactiveData.fechainicio === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechainicio,
      reactiveData.fechafin === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechafin,
      reactiveData.minHoras,
      true
    ).then(url =>
      piped(
        document.createElement('a'),
        tap(a => (a.href = url as string)),
        tap(a => (a.download = 'informe-horas-registradas-alert.xlsx')),
        tap(a => document.body.append(a)),
        tap(a => a.click()),
        tap(a => a.remove()),
        tap(() => URL.revokeObjectURL(url as string))
      )
    ),
  queryKey: ['informe-horas-registradas-xlsx']
})

watch(isHorasRegistradasInformeXlsxPending, value => (doDownload.value = value))
</script>

<template>
  <ConsultoresExcluidos :open="openDialog" @close="openDialog = false" />
  <div class="intro-y box px-2 pb-5 flex flex-row">
    <div class="flex flex-col w-1/2">
      <h3 class="text-2xl font-medium mb-4">Consultores</h3>

      <div class="flex flex-row gap-3 justify-evenly">
        <div class="flex flex-col">
          <label> Fecha Inicio: </label>
          <div class="relative">
            <div
              class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
            >
              <LucideCalendar class="w-4 h-4" />
            </div>
            <Litepicker
              v-model="reactiveData.fechainicio"
              class="pl-12"
              :options="{
                autoApply: false,
                showWeekNumbers: true,
                dropdowns: {
                  minYear: 2020,
                  maxYear: 2035,
                  months: true,
                  years: true
                }
              }"
              @change="() => refetchHorasRegistradasInforme()"
            />
          </div>
        </div>
        <div class="flex flex-col">
          <label> Fecha Fin: </label>
          <div class="relative">
            <div
              class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
            >
              <LucideCalendar class="w-4 h-4" />
            </div>
            <Litepicker
              v-model="reactiveData.fechafin"
              class="pl-12"
              :options="{
                autoApply: false,
                showWeekNumbers: true,
                dropdowns: {
                  minYear: 2020,
                  maxYear: 2035,
                  months: true,
                  years: true
                }
              }"
              @change="() => refetchHorasRegistradasInforme()"
            />
          </div>
        </div>
        <div class="flex flex-col mt-1">
          <label class="cursor-pointer select-none">
            Núm. mín. de horas:
          </label>
          <FormInput
            v-model="reactiveData.minHoras"
            class="block px-4 py-3 h-8"
            type="number"
            @change="() => refetchHorasRegistradasInforme()"
          />
        </div>
      </div>
      <div
        v-if="isHorasRegistradasInformePending"
        class="flex flex-col items-center m-5 justify-end col-span-6 sm:col-span-3 xl:col-span-2"
      >
        <Loading icon="three-dots" />
      </div>
      <div
        v-else-if="
          !isHorasRegistradasInformePending &&
          dataHorasRegistradasInforme &&
          (dataHorasRegistradasInforme as HorasRegistradasAlert[]).length > 0
        "
        class="p-5"
      >
        <div class="overflow-auto max-h-[360px]">
          <Table hover sm>
            <Thead variant="dark">
              <Tr>
                <Th class="whitespace-nowrap font-bold">
                  <LucideCheck class="h-3 w-3" />
                </Th>
                <Th
                  v-for="row in horasRegistradasAlertMetadata"
                  :key="row.key"
                  class="whitespace-nowrap font-bold"
                >
                  {{ row.label }}
                </Th>
              </Tr>
            </Thead>
            <TBody>
              <Tr
                v-for="el in dataHorasRegistradasInforme as HorasRegistradasAlert[]"
                :key="el.id"
                class="dark:hover:bg-[#303761] hover:bg-[#dadef7]"
              >
                <Td>
                  <FormCheck>
                    <FormCheckInput
                      type="checkbox"
                      :value="el.id"
                      @change="e => handleCheckboxChange(e, el)"
                    />
                  </FormCheck>
                </Td>
                <Td>{{ el.id }}</Td>
                <Td>{{ el.nombreconsultor }}</Td>
                <Td>{{ el.horas }}</Td>
                <Td>{{ el.fechaingreso }}</Td>
                <Td>{{ el.email }}</Td>
                <Td>{{ el.emailjefefuncional }}</Td>
              </Tr>
            </TBody>
          </Table>
        </div>
      </div>
      <div
        v-else-if="
          !isHorasRegistradasInformePending &&
          dataHorasRegistradasInforme &&
          (dataHorasRegistradasInforme as HorasRegistradasAlert[]).length === 0
        "
        class="p-10"
      >
        <h3 class="text-center font-bold text-xl">
          No hay datos disponibles para mostrar
        </h3>
      </div>
      <div
        v-if="
          !isHorasRegistradasInformePending &&
          dataHorasRegistradasInforme &&
          (dataHorasRegistradasInforme as HorasRegistradasAlert[]).length > 0
        "
        class="mt-6 flex flex-col w-full gap-5"
      >
        <Button
          :disabled="isHorasRegistradasInformeXlsxPending"
          variant="success"
          class="w-full mr-1"
          @click="() => refetchHorasRegistradasInformeXlsx()"
        >
          <Loading
            v-if="isHorasRegistradasInformeXlsxPending"
            icon="three-dots"
            color="#000"
            class="mr-2 w-4 h-4"
          />
          <LucideSheet v-else class="w-4 h-4 mr-2" /> Informe Horas Registradas
        </Button>

        <Button
          :disabled="isHorasRegistradasInformeXlsxPending"
          variant="primary"
          class="w-full mr-1"
          @click="() => (openDialog = true)"
        >
          Consultores Excluidos
        </Button>
      </div>
    </div>
    <div class="flex flex-col w-1/2 gap-4 pl-20">
      <div class="flex flex-row justify-between gap-2">
        <label class="select-none pr-10 mt-2"> Asunto: </label>
        <FormInput
          v-model="emailForm.asunto"
          class="block px-4 py-3 w-full"
          placeholder="Asunto"
        />
      </div>
      <div class="flex flex-row justify-between gap-2">
        <label class="select-none pr-10 mt-3"> Mensaje: </label>
        <FormInput
          v-model="emailForm.texto"
          class="min-h-[120px] w-full"
          placeholder="Mensaje aquí ..."
        />
      </div>
      <div class="flex flex-row justify-between gap-2">
        <label class="select-none pr-10 mt-3"> A: </label>
        <FormInput v-model="emailsOnly" readonly />
      </div>
      <div class="flex flex-row justify-between gap-2">
        <Button
          variant="primary"
          class="w-full mr-1"
          :disabled="selectedRows.length === 0"
          @click="() => (emailText = emailsOnly)"
        >
          Ver Cadena de Envío
        </Button>
        <Button
          variant="success"
          class="w-full mr-1"
          :disabled="
            selectedRows.length === 0 ||
            emails.length === 0 ||
            emailForm.asunto === '' ||
            emailForm.texto === ''
          "
          @click="mutateSendMail"
        >
          <Loading
            v-if="isSendingMail"
            icon="three-dots"
            color="#000"
            class="mr-2 w-4 h-4"
          />
          <LucideSend v-else class="w-4 h-4 mr-2" />
          Enviar
        </Button>
      </div>
    </div>
  </div>
</template>
