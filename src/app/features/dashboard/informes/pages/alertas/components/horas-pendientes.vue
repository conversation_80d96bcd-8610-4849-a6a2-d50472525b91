<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import { useAlertStore } from '@c/stores/alert-store'
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'
import Table from '@d/common/components/table/Table.vue'
import TBody from '@d/common/components/table/Tbody.vue'
import Td from '@d/common/components/table/Td.vue'
import Th from '@d/common/components/table/Th.vue'
import Thead from '@d/common/components/table/Thead.vue'
import Tr from '@d/common/components/table/Tr.vue'
import {
  type HorasPendientesAlert,
  horasPendientesAlertMetadata
} from '@d/informes/models/horas-pendientes-alert'
import {
  getHorasPendientesAlert,
  sendMail
} from '@d/informes/repository/informes-alertas'
import { useMutation, useQuery } from '@tanstack/vue-query'

import FormCheck from '@/common/components/form/FormCheck/FormCheck.vue'
import FormCheckInput from '@/common/components/form/FormCheck/Input.vue'
import FormInput from '@/common/components/form/FormInput.vue'
//import FormTextarea from '@/common/components/form/FormTextarea.vue'
import Loading from '@/common/components/loading.vue'
import { useNuxtApp } from '#app'

const { $dayjs } = useNuxtApp()
const alertStore = useAlertStore()
const emails = ref<string[]>([])
const selectedRows = ref<HorasPendientesAlert[]>([])
const emailsOnly = computed(() =>
  selectedRows.value.map(el => el.email).join(',')
)
const emailText = ref('')
const emailForm = reactive({ asunto: '', texto: '' })
const reactiveData = reactive({ fechafin: '', fechainicio: '', minHoras: 0 })

const {
  data: dataHorasPendientesInforme,
  isFetching: isHorasPendientesInformePending,
  refetch: refetchHorasPendientesInforme
} = useQuery({
  queryFn: () =>
    getHorasPendientesAlert(
      reactiveData.fechainicio === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechainicio,
      reactiveData.fechafin === ''
        ? $dayjs().format('YYYY-MM-DD')
        : reactiveData.fechafin,
      reactiveData.minHoras
    ),
  queryKey: ['informes-alert-horas-pendientes']
})

const { isPending: isSendingMail, mutate: mutateSendMail } = useMutation({
  mutationFn: () =>
    sendMail(
      selectedRows.value.map(el => el.email ?? ''),
      emailForm.asunto,
      emailForm.texto
    ),
  onError: () => alertStore.showErrorAlert(),
  onSuccess: () => alertStore.showSuccessAlert()
})

const handleCheckboxChange = (event: Event, tarea: HorasPendientesAlert) => {
  const isChecked = (event.target as HTMLInputElement).checked
  if (isChecked) {
    selectedRows.value.push(tarea)
  } else {
    selectedRows.value = selectedRows.value.filter(
      item => item.consultoraprobadorid !== tarea.consultoraprobadorid
    )
  }
}
</script>

<template>
  <div class="intro-y box px-2 pb-5 flex flex-row">
    <div class="flex flex-col w-1/2">
      <h3 class="text-2xl font-medium mb-4">Consultores</h3>

      <div class="flex flex-row gap-3 justify-evenly">
        <div class="flex flex-col">
          <label> Fecha Inicio: </label>
          <div class="relative">
            <div
              class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
            >
              <LucideCalendar class="w-4 h-4" />
            </div>
            <Litepicker
              v-model="reactiveData.fechainicio"
              class="pl-12"
              :options="{
                autoApply: false,
                showWeekNumbers: true,
                dropdowns: {
                  minYear: 2020,
                  maxYear: 2035,
                  months: true,
                  years: true
                }
              }"
              @change="() => refetchHorasPendientesInforme()"
            />
          </div>
        </div>
        <div class="flex flex-col">
          <label> Fecha Fin: </label>
          <div class="relative">
            <div
              class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
            >
              <LucideCalendar class="w-4 h-4" />
            </div>
            <Litepicker
              v-model="reactiveData.fechafin"
              class="pl-12"
              :options="{
                autoApply: false,
                showWeekNumbers: true,
                dropdowns: {
                  minYear: 2020,
                  maxYear: 2035,
                  months: true,
                  years: true
                }
              }"
              @change="() => refetchHorasPendientesInforme()"
            />
          </div>
        </div>
        <div class="flex flex-col mt-1">
          <label class="cursor-pointer select-none">
            Núm. mín. de horas:
          </label>
          <FormInput
            v-model="reactiveData.minHoras"
            class="block px-4 py-3 h-8"
            type="number"
            @change="() => refetchHorasPendientesInforme()"
          />
        </div>
      </div>
      <div
        v-if="isHorasPendientesInformePending"
        class="flex flex-col items-center m-5 justify-end col-span-6 sm:col-span-3 xl:col-span-2"
      >
        <Loading icon="three-dots" />
      </div>
      <div
        v-else-if="
          !isHorasPendientesInformePending &&
          dataHorasPendientesInforme &&
          dataHorasPendientesInforme.length > 0
        "
        class="p-5"
      >
        <div class="overflow-auto max-h-[360px]">
          <Table hover sm>
            <Thead variant="dark">
              <Tr>
                <Th class="whitespace-nowrap font-bold">
                  <LucideCheck class="h-3 w-3" />
                </Th>
                <Th
                  v-for="row in horasPendientesAlertMetadata"
                  :key="row.key"
                  class="whitespace-nowrap font-bold"
                >
                  {{ row.label }}
                </Th>
              </Tr>
            </Thead>
            <TBody>
              <Tr
                v-for="el in dataHorasPendientesInforme as HorasPendientesAlert[]"
                :key="el.consultoraprobadorid"
                class="dark:hover:bg-[#303761] hover:bg-[#dadef7]"
              >
                <Td>
                  <FormCheck>
                    <FormCheckInput
                      type="checkbox"
                      :value="el.consultoraprobadorid"
                      @change="e => handleCheckboxChange(e, el)"
                    />
                  </FormCheck>
                </Td>
                <Td>{{ el.consultoraprobadorid }}</Td>
                <Td>{{ el.consultoraprobadornombre }}</Td>
                <Td>{{ el.email }}</Td>
                <Td>{{ el.horas }}</Td>
              </Tr>
            </TBody>
          </Table>
        </div>
      </div>
      <div
        v-else-if="
          !isHorasPendientesInformePending &&
          dataHorasPendientesInforme &&
          dataHorasPendientesInforme.length === 0
        "
        class="p-10"
      >
        <h3 class="text-center font-bold text-xl">
          No hay datos disponibles para mostrar
        </h3>
      </div>
    </div>
    <div class="flex flex-col w-1/2 gap-4 pl-20">
      <div class="flex flex-row justify-between gap-2">
        <label class="select-none pr-10 mt-2"> Asunto: </label>
        <FormInput
          v-model="emailForm.asunto"
          class="block px-4 py-3 w-full"
          placeholder="Asunto"
        />
      </div>
      <div class="flex flex-row justify-between gap-2">
        <label class="select-none pr-10 mt-3"> Mensaje: </label>
        <FormInput
          v-model="emailForm.texto"
          class="min-h-[120px] w-full"
          placeholder="Mensaje aquí ..."
        />
      </div>
      <div class="flex flex-row justify-between gap-2">
        <label class="select-none pr-10 mt-3"> A: </label>
        <FormInput v-model="emailText" readonly />
      </div>
      <div class="flex flex-row justify-between gap-2">
        <Button
          variant="primary"
          class="w-full mr-1"
          :disabled="selectedRows.length === 0"
          @click="() => (emailText = emailsOnly)"
        >
          Ver Cadena de Envío
        </Button>
        <Button
          variant="success"
          class="w-full mr-1"
          :disabled="
            selectedRows.length === 0 ||
            emails.length === 0 ||
            emailForm.asunto === '' ||
            emailForm.texto === ''
          "
          @click="mutateSendMail"
        >
          <Loading
            v-if="isSendingMail"
            icon="three-dots"
            color="#000"
            class="mr-2 w-4 h-4"
          />
          <LucideSend v-else class="w-4 h-4 mr-2" />
          Enviar
        </Button>
      </div>
    </div>
  </div>
</template>
