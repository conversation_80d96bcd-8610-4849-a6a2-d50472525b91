<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-redundant-type-constituents -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import SlideoverDescription from '@c/components/slideover/Description.vue'
import SlideoverPanel from '@c/components/slideover/Panel.vue'
import Slideover from '@c/components/slideover/Slideover.vue'
import Table from '@d/common/components/table/Table.vue'
import TBody from '@d/common/components/table/Tbody.vue'
import Td from '@d/common/components/table/Td.vue'
import Th from '@d/common/components/table/Th.vue'
import Thead from '@d/common/components/table/Thead.vue'
import Tr from '@d/common/components/table/Tr.vue'
import { useQuery } from '@tanstack/vue-query'

import { consultoresExcluidosMetadata } from '@d/informes/models/consultores-excluidos'
import { getConsultoresExcluidos } from '@d/informes/repository/informes-alertas'

const props = defineProps<{ open: boolean }>()

defineEmits<(e: 'close') => void>()

const {
  data: dataConsultoresExcluidosInforme,
  isFetching: isConsultoresExcluidosInformePending
} = useQuery({
  queryFn: () => getConsultoresExcluidos(),
  queryKey: ['informes-alert-consultores-excluidos']
})
</script>

<template>
  <Slideover size="lg" :open="props.open" @close="$emit('close')">
    <SlideoverPanel>
      <SlideoverDescription
        class="px-8 pt-10 pb-8 flex flex-col justify-between"
      >
        <div class="flex flex-col">
          <div class="flex flex-row w-full justify-between">
            <div class="text-xl font-medium">Consultores Excluidos</div>
          </div>
          <div class="intro-y box w-full">
            <div
              v-if="isConsultoresExcluidosInformePending"
              class="flex flex-col items-center m-5 justify-end col-span-6 sm:col-span-3 xl:col-span-2"
            >
              <Loading icon="three-dots" />
            </div>
            <div
              v-else-if="
                !isConsultoresExcluidosInformePending &&
                dataConsultoresExcluidosInforme &&
                dataConsultoresExcluidosInforme.length > 0
              "
              class="p-5"
            >
              <div class="overflow-auto max-h-[800px]">
                <Table hover sm>
                  <Thead variant="dark">
                    <Tr>
                      <Th
                        v-for="row in consultoresExcluidosMetadata"
                        :key="row.key"
                        class="whitespace-nowrap font-bold"
                      >
                        {{ row.label }}
                      </Th>
                    </Tr>
                  </Thead>
                  <TBody>
                    <Tr
                      v-for="el in dataConsultoresExcluidosInforme"
                      :key="el.id"
                      class="dark:hover:bg-[#303761] hover:bg-[#dadef7]"
                    >
                      <Td>{{ el.id }}</Td>
                      <Td>{{ el.nombre }}</Td>
                      <Td>{{ el.apellido }}</Td>
                      <Td>{{ el.role }}</Td>
                    </Tr>
                  </TBody>
                </Table>
              </div>
            </div>
            <div
              v-else-if="
                !isConsultoresExcluidosInformePending &&
                dataConsultoresExcluidosInforme &&
                dataConsultoresExcluidosInforme.length === 0
              "
              class="p-10"
            >
              <h3 class="text-center font-bold text-xl">
                No hay datos disponibles para mostrar
              </h3>
            </div>
          </div>
        </div>

        <div class="flex flex-row gap-3 justify-end">
          <Button
            variant="primary"
            class="w-24 mr-1"
            @click="() => $emit('close')"
          >
            OK
          </Button>
        </div>
      </SlideoverDescription>
    </SlideoverPanel>
  </Slideover>
</template>
