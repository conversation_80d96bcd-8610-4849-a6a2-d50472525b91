<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import { getInformeConsultoresProyecto } from '@d/informes/repository/informes-varios-repository'
import { useQuery } from '@tanstack/vue-query'
import { piped, tap } from 'rambdax'

import Loading from '@/common/components/loading.vue'

const doVisualize = ref(false)
const doDownload = ref(false)

const {
  data: dataEntreFechasInforme,
  isFetching: isEntreFechasInformePending,
  refetch: refetchEntreFechasInforme
} = useQuery({
  enabled: computed(() => doVisualize.value),
  queryFn: () => getInformeConsultoresProyecto(false),
  queryKey: ['informes-consultores-proyecto-pdf']
})

const {
  isFetching: isEntreFechasInformeXlsxPending,
  refetch: refetchEntreFechasInformeXlsx
} = useQuery({
  enabled: computed(() => doDownload.value),
  queryFn: () =>
    getInformeConsultoresProyecto(true).then(url =>
      piped(
        document.createElement('a'),
        tap(a => (a.href = url)),
        tap(a => (a.download = 'consultores-proyecto.xlsx')),
        tap(a => document.body.append(a)),
        tap(a => a.click()),
        tap(a => a.remove()),
        tap(() => URL.revokeObjectURL(url))
      )
    ),
  queryKey: ['informe-consultores-proyecto-xlsx']
})

watch(isEntreFechasInformePending, value => (doVisualize.value = value))
watch(isEntreFechasInformeXlsxPending, value => (doDownload.value = value))
</script>

<template>
  <div class="flex flex-row gap-3 justify-evenly w-full">
    <Button
      variant="primary"
      class="w-24 mr-1"
      :disabled="isEntreFechasInformePending"
      @click="() => refetchEntreFechasInforme()"
    >
      Visualizar
    </Button>
    <Button
      variant="success"
      class="w-36"
      :disabled="isEntreFechasInformeXlsxPending"
      @click="() => refetchEntreFechasInformeXlsx()"
    >
      <LucideSheet class="w-4 h-4 mr-2" /> Descargar
    </Button>
  </div>
  <div
    v-if="isEntreFechasInformePending || isEntreFechasInformeXlsxPending"
    class="box flex flex-col items-center py-4"
  >
    <Loading icon="puff" />
  </div>
  <div
    v-if="
      !isEntreFechasInformePending &&
      dataEntreFechasInforme &&
      dataEntreFechasInforme !== ''
    "
    class="w-full mt-7"
  >
    <iframe :src="dataEntreFechasInforme" width="100%" height="600px" />
  </div>
</template>
