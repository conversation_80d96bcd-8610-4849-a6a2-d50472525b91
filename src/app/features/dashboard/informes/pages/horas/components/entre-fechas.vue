<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'
import { getInformeEntreFechas } from '@d/informes/repository/informes-hora-repository'
import { useQuery } from '@tanstack/vue-query'
import { piped, tap } from 'rambdax'

import Loading from '@/common/components/loading.vue'

const reactiveData = reactive({ fechafin: '', fechainicio: '' })
const doVisualize = ref(false)
const doDownload = ref(false)

const {
  data: dataEntreFechasInforme,
  isFetching: isEntreFechasInformePending,
  refetch: refetchEntreFechasInforme
} = useQuery({
  enabled: computed(() => doVisualize.value),
  queryFn: () =>
    getInformeEntreFechas(
      reactiveData.fechainicio,
      reactiveData.fechafin,
      false
    ),
  queryKey: ['informes-horas-entre-fechas-pdf']
})

const {
  isFetching: isEntreFechasInformeXlsxPending,
  refetch: refetchEntreFechasInformeXlsx
} = useQuery({
  enabled: computed(() => doDownload.value),
  queryFn: () =>
    getInformeEntreFechas(
      reactiveData.fechainicio,
      reactiveData.fechafin,
      true
    ).then(url =>
      piped(
        document.createElement('a'),
        tap(a => (a.href = url)),
        tap(a => (a.download = 'horas-por-entreFechas.xlsx')),
        tap(a => document.body.append(a)),
        tap(a => a.click()),
        tap(a => a.remove()),
        tap(() => URL.revokeObjectURL(url))
      )
    ),
  queryKey: ['informe-horas-por-entreFechas-xlsx']
})

watch(isEntreFechasInformePending, value => (doVisualize.value = value))
watch(isEntreFechasInformeXlsxPending, value => (doDownload.value = value))
</script>

<template>
  <div class="flex flex-row gap-3 justify-evenly">
    <div class="flex flex-col">
      <label> Fecha Inicio: </label>
      <div class="relative">
        <div
          class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
        >
          <LucideCalendar class="w-4 h-4" />
        </div>
        <Litepicker
          v-model="reactiveData.fechainicio"
          class="pl-12"
          :options="{
            autoApply: false,
            showWeekNumbers: true,
            dropdowns: {
              minYear: 2020,
              maxYear: 2035,
              months: true,
              years: true
            }
          }"
        />
      </div>
    </div>
    <div class="flex flex-col">
      <label> Fecha Fin: </label>
      <div class="relative">
        <div
          class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
        >
          <LucideCalendar class="w-4 h-4" />
        </div>
        <Litepicker
          v-model="reactiveData.fechafin"
          class="pl-12"
          :options="{
            autoApply: false,
            showWeekNumbers: true,
            dropdowns: {
              minYear: 2020,
              maxYear: 2035,
              months: true,
              years: true
            }
          }"
        />
      </div>
    </div>
  </div>
  <div class="flex flex-row gap-3 justify-evenly w-full mt-6">
    <Button
      variant="primary"
      class="w-24 mr-1"
      :disabled="isEntreFechasInformePending"
      @click="() => refetchEntreFechasInforme()"
    >
      Visualizar
    </Button>
    <Button
      variant="success"
      class="w-36"
      :disabled="isEntreFechasInformeXlsxPending"
      @click="() => refetchEntreFechasInformeXlsx()"
    >
      <LucideSheet class="w-4 h-4 mr-2" /> Descargar
    </Button>
  </div>
  <div
    v-if="isEntreFechasInformePending || isEntreFechasInformeXlsxPending"
    class="box flex flex-col items-center py-4"
  >
    <Loading icon="puff" />
  </div>
  <div
    v-if="
      !isEntreFechasInformePending &&
      dataEntreFechasInforme &&
      dataEntreFechasInforme !== ''
    "
    class="w-full mt-7"
  >
    <iframe :src="dataEntreFechasInforme" width="100%" height="600px" />
  </div>
</template>
