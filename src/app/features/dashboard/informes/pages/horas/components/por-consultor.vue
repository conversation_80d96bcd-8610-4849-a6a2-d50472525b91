<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'
import {
  getConsultores,
  getInformePorConsultor
} from '@d/informes/repository/informes-hora-repository'
import { useQuery } from '@tanstack/vue-query'
import { piped, tap } from 'rambdax'

import FormInput from '@/common/components/form/FormInput.vue'
import FormSelect from '@/common/components/form/FormSelect.vue'
import Loading from '@/common/components/loading.vue'

const selectedConsultor = reactive<{
  id: number | null
  model: string
}>({ id: null, model: '' })

const { data: dataConsultor, isFetching: isConsultorPending } = useQuery({
  queryFn: getConsultores,
  queryKey: ['informes-data-consultor']
})

const reactiveData = reactive({
  fechafin: '',
  fechainicio: '',
  idconsultor: ''
})

const doVisualize = ref(false)
const doDownload = ref(false)

const {
  data: dataConsultorInforme,
  isFetching: isConsultorInformePending,
  refetch: refetchConsultorInforme
} = useQuery({
  enabled: computed(() => doVisualize.value),
  queryFn: () =>
    getInformePorConsultor(
      selectedConsultor.id ?? 0,
      reactiveData.fechainicio,
      reactiveData.fechafin,
      false
    ),
  queryKey: ['informes-horas-por-consultor-pdf']
})

const {
  isFetching: isConsultorInformeXlsxPending,
  refetch: refetchConsultorInformeXlsx
} = useQuery({
  enabled: computed(() => doDownload.value),
  queryFn: () =>
    getInformePorConsultor(
      selectedConsultor.id ?? 0,
      reactiveData.fechainicio,
      reactiveData.fechafin,
      true
    ).then(url =>
      piped(
        document.createElement('a'),
        tap(a => (a.href = url)),
        tap(a => (a.download = 'horas-por-consultor.xlsx')),
        tap(a => document.body.append(a)),
        tap(a => a.click()),
        tap(a => a.remove()),
        tap(() => URL.revokeObjectURL(url))
      )
    ),
  queryKey: ['informe-horas-por-consultor-xlsx']
})

watch(isConsultorInformePending, value => (doVisualize.value = value))
watch(isConsultorInformeXlsxPending, value => (doDownload.value = value))
</script>

<template>
  <div v-if="isConsultorPending" class="box flex flex-col items-center py-4">
    <Loading icon="three-dots" />
  </div>
  <template v-else>
    <div class="flex flex-row gap-3 justify-between">
      <div class="flex flex-col">
        <label class="cursor-pointer select-none mb-1"> Consultor: </label>
        <FormSelect v-model="selectedConsultor.model" class="w-8/12">
          <option value="" @click="selectedConsultor.model = ''">
            Seleccione ...
          </option>
          <option
            v-for="consultor in dataConsultor ?? []"
            :key="consultor.id"
            :value="consultor.id?.toString()"
            @click="
              () => {
                selectedConsultor.model = consultor?.etiqueta
                selectedConsultor.id = consultor?.id
                reactiveData.idconsultor = consultor?.id?.toString() ?? ''
              }
            "
          >
            {{ consultor.etiqueta }}
          </option>
        </FormSelect>
      </div>
      <div class="flex flex-col mr-10">
        <p class="w-5/6">ID Consultor:</p>
        <FormInput
          v-model="reactiveData.idconsultor"
          class="block px-4 py-3"
          type="text"
          readonly
        />
      </div>
      <div class="flex flex-col">
        <label class="cursor-pointer select-none"> Fecha Inicio: </label>
        <div class="relative">
          <div
            class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
          >
            <LucideCalendar class="w-4 h-4" />
          </div>
          <Litepicker
            v-model="reactiveData.fechainicio"
            class="pl-12"
            :options="{
              autoApply: false,
              showWeekNumbers: true,
              dropdowns: {
                minYear: 2020,
                maxYear: 2035,
                months: true,
                years: true
              }
            }"
          />
        </div>
      </div>
      <div class="flex flex-col">
        <label class="cursor-pointer select-none"> Fecha Fin: </label>
        <div class="relative">
          <div
            class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
          >
            <LucideCalendar class="w-4 h-4" />
          </div>
          <Litepicker
            v-model="reactiveData.fechafin"
            class="pl-12"
            :options="{
              autoApply: false,
              showWeekNumbers: true,
              dropdowns: {
                minYear: 2020,
                maxYear: 2035,
                months: true,
                years: true
              }
            }"
          />
        </div>
      </div>
    </div>
    <div
      v-if="selectedConsultor.id != null"
      class="flex flex-row gap-3 justify-evenly w-full mt-6"
    >
      <Button
        variant="primary"
        class="w-24 mr-1"
        :disabled="isConsultorInformePending"
        @click="() => refetchConsultorInforme()"
      >
        Visualizar
      </Button>
      <Button
        variant="success"
        class="w-36"
        :disabled="isConsultorInformeXlsxPending"
        @click="() => refetchConsultorInformeXlsx()"
      >
        <LucideSheet class="w-4 h-4 mr-2" /> Descargar
      </Button>
    </div>
    <div
      v-if="isConsultorInformePending || isConsultorInformeXlsxPending"
      class="box flex flex-col items-center py-4"
    >
      <Loading icon="puff" />
    </div>
    <div
      v-if="
        !isConsultorInformePending &&
        dataConsultorInforme &&
        dataConsultorInforme !== ''
      "
      class="w-full mt-7"
    >
      <iframe :src="dataConsultorInforme" width="100%" height="600px" />
    </div>
  </template>
</template>
