<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import { getClientes } from '@d/accesos/repository/clientes-repository'
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'
import { getInformePorCliente } from '@d/informes/repository/informes-hora-repository'
import { useQuery } from '@tanstack/vue-query'
import { piped, tap } from 'rambdax'

import FormSelect from '@/common/components/form/FormSelect.vue'
import Loading from '@/common/components/loading.vue'

const selectedCliente = reactive<{
  id: number | null
  model: string
}>({ id: null, model: '' })

const { data: dataClientes, isFetching: isClientesPending } = useQuery({
  queryFn: getClientes,
  queryKey: ['informes-data-cliente']
})

const reactiveData = reactive({
  fechafin: '',
  fechainicio: '',
  nombrecliente: ''
})

const doVisualize = ref(false)
const doDownload = ref(false)

const {
  data: dataClienteInforme,
  isFetching: isClienteInformePending,
  refetch: refetchClienteInforme
} = useQuery({
  enabled: computed(() => doVisualize.value),
  queryFn: () =>
    getInformePorCliente(
      selectedCliente.model,
      reactiveData.fechainicio,
      reactiveData.fechafin,
      false
    ),
  queryKey: ['informes-horas-por-cliente-pdf']
})

const {
  isFetching: isClienteInformeXlsxPending,
  refetch: refetchClienteInformeXlsx
} = useQuery({
  enabled: computed(() => doDownload.value),
  queryFn: () =>
    getInformePorCliente(
      selectedCliente.model,
      reactiveData.fechainicio,
      reactiveData.fechafin,
      true
    ).then(url =>
      piped(
        document.createElement('a'),
        tap(a => (a.href = url)),
        tap(a => (a.download = 'horas-por-cliente.xlsx')),
        tap(a => document.body.append(a)),
        tap(a => a.click()),
        tap(a => a.remove()),
        tap(() => URL.revokeObjectURL(url))
      )
    ),
  queryKey: ['informe-horas-por-cliente-xlsx']
})

watch(isClienteInformePending, value => (doVisualize.value = value))
watch(isClienteInformeXlsxPending, value => (doDownload.value = value))
</script>

<template>
  <div v-if="isClientesPending" class="box flex flex-col items-center py-4">
    <Loading icon="three-dots" />
  </div>
  <template v-else>
    <div class="flex flex-row gap-3 justify-between">
      <div class="flex flex-col">
        <label> Fecha Inicio: </label>
        <div class="relative">
          <div
            class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
          >
            <LucideCalendar class="w-4 h-4" />
          </div>
          <Litepicker
            v-model="reactiveData.fechainicio"
            class="pl-12"
            :options="{
              autoApply: false,
              showWeekNumbers: true,
              dropdowns: {
                minYear: 2020,
                maxYear: 2035,
                months: true,
                years: true
              }
            }"
          />
        </div>
      </div>
      <div class="flex flex-col">
        <label> Fecha Fin: </label>
        <div class="relative">
          <div
            class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
          >
            <LucideCalendar class="w-4 h-4" />
          </div>
          <Litepicker
            v-model="reactiveData.fechafin"
            class="pl-12"
            :options="{
              autoApply: false,
              showWeekNumbers: true,
              dropdowns: {
                minYear: 2020,
                maxYear: 2035,
                months: true,
                years: true
              }
            }"
          />
        </div>
      </div>
      <div class="flex flex-col max-w-1/4">
        <label class="mb-1">Cliente: </label>
        <FormSelect v-model="selectedCliente.model" class="w-full">
          <option value="" @click="selectedCliente.model = ''">
            Seleccione ...
          </option>
          <option
            v-for="cliente in dataClientes ?? []"
            :key="cliente.idcliente"
            :value="cliente.nombrecliente"
            @click="
              () => {
                selectedCliente.model = cliente.nombrecliente
                selectedCliente.id = cliente.idcliente
              }
            "
          >
            {{ cliente.nombrecliente }}
          </option>
        </FormSelect>
      </div>
    </div>
    <div
      v-if="selectedCliente.id != null"
      class="flex flex-row gap-3 justify-evenly w-full mt-6"
    >
      <Button
        variant="primary"
        class="w-24 mr-1"
        :disabled="isClienteInformePending"
        @click="() => refetchClienteInforme()"
      >
        Visualizar
      </Button>
      <Button
        variant="success"
        class="w-36"
        :disabled="isClienteInformeXlsxPending"
        @click="() => refetchClienteInformeXlsx()"
      >
        <LucideSheet class="w-4 h-4 mr-2" /> Descargar
      </Button>
    </div>
    <div
      v-if="isClienteInformePending || isClienteInformeXlsxPending"
      class="box flex flex-col items-center py-4"
    >
      <Loading icon="puff" />
    </div>
    <div
      v-if="
        !isClienteInformePending &&
        dataClienteInforme &&
        dataClienteInforme !== ''
      "
      class="w-full mt-7"
    >
      <iframe :src="dataClienteInforme" width="100%" height="600px" />
    </div>
  </template>
</template>
