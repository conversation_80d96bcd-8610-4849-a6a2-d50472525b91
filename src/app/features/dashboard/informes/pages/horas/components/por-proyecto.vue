<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import Button from '@c/components/button/button.vue'
import Litepicker from '@d/common/components/litepicker/Litepicker.vue'
import {
  getInformePorProyecto,
  getProyectos
} from '@d/informes/repository/informes-hora-repository'
import { useQuery } from '@tanstack/vue-query'
import { piped, tap } from 'rambdax'

import FormSelect from '@/common/components/form/FormSelect.vue'
import Loading from '@/common/components/loading.vue'
import { getClientes } from '@d/accesos/repository/clientes-repository'

const selectedCliente = reactive<{
  id: number | null
  model: string
}>({ id: null, model: '' })

const selectedProyecto = reactive<{
  id: number | null
  model: string
}>({ id: null, model: '' })

const { data: dataClientes, isFetching: isClientesPending } = useQuery({
  queryFn: getClientes,
  queryKey: ['informes-data-proyecto-clientes']
})

const {
  data: dataProyecto,
  isFetching: isProyectoPending,
  refetch: refetchProyecto
} = useQuery({
  enabled: computed(() => selectedCliente.model !== ''),
  queryFn: () => getProyectos(selectedCliente.model),
  queryKey: ['informes-data-proyecto']
})

const reactiveData = reactive({
  fechafin: '',
  fechainicio: '',
  nombreproyecto: ''
})

const doVisualize = ref(false)
const doDownload = ref(false)

const {
  data: dataProyectoInforme,
  isFetching: isProyectoInformePending,
  refetch: refetchProyectoInforme
} = useQuery({
  enabled: computed(() => doVisualize.value),
  queryFn: () =>
    getInformePorProyecto(
      selectedProyecto.model,
      reactiveData.fechainicio,
      reactiveData.fechafin,
      false
    ),
  queryKey: ['informes-horas-por-proyecto-pdf']
})

const {
  isFetching: isProyectoInformeXlsxPending,
  refetch: refetchProyectoInformeXlsx
} = useQuery({
  enabled: computed(() => doDownload.value),
  queryFn: () =>
    getInformePorProyecto(
      selectedProyecto.model,
      reactiveData.fechainicio,
      reactiveData.fechafin,
      true
    ).then(url =>
      piped(
        document.createElement('a'),
        tap(a => (a.href = url)),
        tap(a => (a.download = 'horas-por-proyecto.xlsx')),
        tap(a => document.body.append(a)),
        tap(a => a.click()),
        tap(a => a.remove()),
        tap(() => URL.revokeObjectURL(url))
      )
    ),
  queryKey: ['informe-horas-por-proyecto-xlsx']
})

watch(isProyectoInformePending, value => (doVisualize.value = value))
watch(isProyectoInformeXlsxPending, value => (doDownload.value = value))
</script>

<template>
  <div v-if="isClientesPending" class="box flex flex-col items-center py-4">
    <Loading icon="three-dots" />
  </div>
  <template v-else>
    <div class="flex flex-row gap-3 justify-between">
      <div class="flex flex-col">
        <label> Fecha Inicio: </label>
        <div class="relative">
          <div
            class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
          >
            <LucideCalendar class="w-4 h-4" />
          </div>
          <Litepicker
            v-model="reactiveData.fechainicio"
            class="pl-12"
            :options="{
              autoApply: false,
              showWeekNumbers: true,
              dropdowns: {
                minYear: 2020,
                maxYear: 2035,
                months: true,
                years: true
              }
            }"
          />
        </div>
      </div>
      <div class="flex flex-col">
        <label> Fecha Fin: </label>
        <div class="relative">
          <div
            class="absolute flex items-center justify-center w-10 h-full border rounded-l bg-slate-100 text-slate-500 dark:bg-darkmode-700 dark:border-darkmode-800 dark:text-slate-400"
          >
            <LucideCalendar class="w-4 h-4" />
          </div>
          <Litepicker
            v-model="reactiveData.fechafin"
            class="pl-12"
            :options="{
              autoApply: false,
              showWeekNumbers: true,
              dropdowns: {
                minYear: 2020,
                maxYear: 2035,
                months: true,
                years: true
              }
            }"
          />
        </div>
      </div>
      <div class="flex flex-col max-w-1/4">
        <label class="mb-1">Cliente: </label>
        <FormSelect
          v-model="selectedCliente.model"
          class="w-full"
          :disabled="isProyectoPending"
        >
          <option value="" @click="selectedCliente.model = ''">
            Seleccione ...
          </option>
          <option
            v-for="cliente in dataClientes ?? []"
            :key="cliente.idcliente"
            :value="cliente.nombrecliente"
            @click="
              () => {
                selectedCliente.model = cliente?.nombrecliente
                selectedCliente.id = cliente?.idcliente
                refetchProyecto()
              }
            "
          >
            {{ cliente.nombrecliente }}
          </option>
        </FormSelect>
      </div>
      <div v-if="isProyectoPending" class="flex flex-col mt-7 mr-20">
        <Loading icon="three-dots" />
      </div>
      <div
        v-else-if="!isProyectoPending && selectedCliente.model != ''"
        class="flex flex-col max-w-1/4"
      >
        <label class="mb-1"> Proyecto: </label>
        <FormSelect v-model="selectedProyecto.model" class="w-full">
          <option value="" @click="selectedProyecto.model = ''">
            Seleccione ...
          </option>
          <option
            v-for="proyecto in dataProyecto ?? []"
            :key="proyecto.idproyecto"
            :value="proyecto.nombreproyecto"
            @click="
              () => {
                selectedProyecto.model = proyecto.nombreproyecto
                selectedProyecto.id = proyecto?.idproyecto
                reactiveData.nombreproyecto = proyecto.nombreproyecto
              }
            "
          >
            {{ proyecto.nombreproyecto }}
          </option>
        </FormSelect>
      </div>
    </div>
    <div
      v-if="selectedProyecto.id != null"
      class="flex flex-row gap-3 justify-evenly w-full mt-6"
    >
      <Button
        variant="primary"
        class="w-24 mr-1"
        :disabled="isProyectoInformePending"
        @click="() => refetchProyectoInforme()"
      >
        Visualizar
      </Button>
      <Button
        variant="success"
        class="w-36"
        :disabled="isProyectoInformeXlsxPending"
        @click="() => refetchProyectoInformeXlsx()"
      >
        <LucideSheet class="w-4 h-4 mr-2" /> Descargar
      </Button>
    </div>
    <div
      v-if="isProyectoInformePending || isProyectoInformeXlsxPending"
      class="box flex flex-col items-center py-4"
    >
      <Loading icon="puff" />
    </div>
    <div
      v-if="
        !isProyectoInformePending &&
        dataProyectoInforme &&
        dataProyectoInforme !== ''
      "
      class="w-full mt-7"
    >
      <iframe :src="dataProyectoInforme" width="100%" height="600px" />
    </div>
  </template>
</template>
