<script setup lang="ts">
import TabButton from '@d/common/components/tabs/Button.vue'
import TabGroup from '@d/common/components/tabs/Group.vue'
import TabList from '@d/common/components/tabs/List.vue'
import TabPanel from '@d/common/components/tabs/Panel.vue'
import TabPanels from '@d/common/components/tabs/Panels.vue'
import Tab from '@d/common/components/tabs/Tab/Tab.vue'

import EntreFechas from './components/entre-fechas.vue'
import PorCliente from './components/por-cliente.vue'
import PorConsultor from './components/por-consultor.vue'
import PorProyecto from './components/por-proyecto.vue'
</script>

<template>
  <div>
    <div class="flex items-center mt-8 mb-3 intro-y">
      <h2 class="mr-auto text-2xl font-medium">Informes de Horas</h2>
    </div>
    <div class="intro-y box">
      <TabGroup class="m-5">
        <TabList variant="boxed-tabs">
          <Tab>
            <TabButton class="w-full py-2" as="button">
              Por Consultor
            </TabButton>
          </Tab>
          <Tab>
            <TabButton class="w-full py-2" as="button">
              Por Proyecto
            </TabButton>
          </Tab>
          <Tab>
            <TabButton class="w-full py-2" as="button"> Por Cliente </TabButton>
          </Tab>
          <Tab>
            <TabButton class="w-full py-2" as="button">
              Entre dos fechas
            </TabButton>
          </Tab>
        </TabList>
        <TabPanels class="border-b border-l border-r">
          <TabPanel class="p-5 leading-relaxed">
            <PorConsultor />
          </TabPanel>
          <TabPanel class="p-5 leading-relaxed">
            <PorProyecto />
          </TabPanel>
          <TabPanel class="p-5 leading-relaxed">
            <PorCliente />
          </TabPanel>
          <TabPanel class="p-5 leading-relaxed">
            <EntreFechas />
          </TabPanel>
        </TabPanels>
      </TabGroup>
    </div>
  </div>
</template>
