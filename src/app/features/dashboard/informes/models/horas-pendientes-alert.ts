import { type } from 'arktype'

export const horasPendientesAlert = type({
  consultoraprobadorid: 'number',
  consultoraprobadornombre: 'string',
  email: 'string | null',
  horas: 'number'
})

export type HorasPendientesAlert = typeof horasPendientesAlert.infer

export const horasPendientesAlertMetadata: {
  label: string
  key: keyof HorasPendientesAlert
}[] = [
  {
    label: 'ID',
    key: 'consultoraprobadorid'
  },
  {
    label: 'Nombre',
    key: 'consultoraprobadornombre'
  },
  {
    label: 'Email',
    key: 'email'
  },
  {
    label: 'Horas',
    key: 'horas'
  }
]
