import { type } from 'arktype'

export const consultoresExcluidos = type({
  id: 'number',
  nombre: 'string',
  apellido: 'string',
  role: 'string'
})

export type ConsultoresExcluidos = typeof consultoresExcluidos.infer

export const consultoresExcluidosMetadata: {
  label: string
  key: keyof ConsultoresExcluidos
}[] = [
  {
    label: 'ID',
    key: 'id'
  },
  {
    label: 'Nombre',
    key: 'nombre'
  },
  {
    label: 'Apellido',
    key: 'apellido'
  },
  {
    label: 'Role',
    key: 'role'
  }
]
