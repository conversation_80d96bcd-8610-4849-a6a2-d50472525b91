import { defineStore } from 'pinia'
import { useLocalStorage } from '@vueuse/core'

export const useDarkModeStore = defineStore('darkMode', () => {
  const darkModeValue = useLocalStorage('darkMode', true)

  const iconColor = (color: string) => (darkModeValue.value ? color : '#000000')

  const setDarkMode = (value: boolean) => {
    darkModeValue.value = value
  }

  return { darkModeValue, iconColor, setDarkMode }
})

export const setDarkModeClass = () => {
  const html =
    document.querySelector<HTMLElement>('html') ??
    document.createElement('html')

  useDarkModeStore().darkModeValue
    ? html.classList.add('dark')
    : html.classList.remove('dark')
}

export const switchDarkMode = (darkMode: boolean) => {
  useDarkModeStore().setDarkMode(darkMode)
  setDarkModeClass()
}
