import { defineStore } from 'pinia'
import type { Consultores } from '@d/admin/models/consultores'

/*id: 4,
    usuario: 'jorge',
    nombre: 'Jorge',
    consultorid: 4,
    esjefefunc: 'S',
    es<PERSON><PERSON><PERSON>roy: 'S',
    role: 'admin',
    pais: 'ES',
    apellido: '<PERSON>ab<PERSON>',
    token: 'DEVVV'*/

export const useLoginStore = defineStore('login', () => {
  //const user = ref<(Partial<Consultores> & { token?: string }) | null>(null)
  const user = ref<(Partial<Consultores> & { token?: string }) | null>({
    id: 183,
    usuario: 'victor',
    nombre: 'Victor',
    consultorid: 183,
    esjefefunc: 'S',
    esje<PERSON><PERSON>roy: 'S',
    role: 'admin',
    pais: 'EC',
    apellido: 'Jim<PERSON><PERSON>',
    token: 'DEVVV'
  })

  const login = (userData: Consultores, token: string) => {
    user.value = userData
    user.value.token = token
  }

  const getToken = computed(() => user.value?.token)
  const isNullToken = computed(
    () => user.value?.token === null || user.value?.token === undefined
  )

  const logout = () => (user.value = null)

  return { user, login, getToken, isNullToken, logout }
})
