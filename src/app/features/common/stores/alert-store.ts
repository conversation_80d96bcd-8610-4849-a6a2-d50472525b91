export const useAlertStore = defineStore('alert', () => {
  const displayAlert = ref<{
    open: boolean
    message: string
    variant:
      | 'primary'
      | 'secondary'
      | 'success'
      | 'warning'
      | 'pending'
      | 'danger'
      | 'dark'
  }>({
    open: false,
    message: '',
    variant: 'primary'
  })

  const showSuccessAlert = (
    message = 'Operación Realizada Correctamente',
    variant:
      | 'primary'
      | 'secondary'
      | 'success'
      | 'warning'
      | 'pending'
      | 'danger'
      | 'dark' = 'primary'
  ) => {
    displayAlert.value = { open: true, message, variant }
    setTimeout(() => {
      displayAlert.value = { ...displayAlert.value, open: false }
    }, 3000)
  }

  const showErrorAlert = (
    message = 'Ha ocurrido un error, por favor revisar',
    variant:
      | 'primary'
      | 'secondary'
      | 'success'
      | 'warning'
      | 'pending'
      | 'danger'
      | 'dark' = 'danger'
  ) => {
    displayAlert.value = { open: true, message, variant }
    setTimeout(() => {
      displayAlert.value = { ...displayAlert.value, open: false }
    }, 3000)
  }

  return { displayAlert, showSuccessAlert, showErrorAlert }
})
