import { defineStore } from 'pinia'
import { useLocalStorage } from '@vueuse/core'

import SideMenu from '@d/common/layouts/side/SideMenu.vue'
import TopMenu from '@d/common/layouts/top/TopMenu.vue'
import SimpleMenu from '@d/common/layouts/simple/SimpleMenu.vue'

export const useLayoutStore = defineStore('layout', () => {
  const layout = useLocalStorage<'side-menu' | 'simple-menu' | 'top-menu'>(
    'layout',
    'side-menu'
  )

  const setLayout = (layoutInc: 'side-menu' | 'simple-menu' | 'top-menu') =>
    (layout.value = layoutInc)

  const layoutComponent = computed(() => {
    switch (layout.value) {
      case 'side-menu':
        return SideMenu
      case 'simple-menu':
        return SimpleMenu
      case 'top-menu':
        return TopMenu
    }
  })

  return { layout, setLayout, layoutComponent }
})
