<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import SlideoverDescription from '@c/components/slideover/Description.vue'
import SlideoverPanel from '@c/components/slideover/Panel.vue'
import Slideover from '@c/components/slideover/Slideover.vue'
import { useLayoutStore } from '@c/stores/layout-store'

import {
  setDarkModeClass,
  switchDarkMode,
  useDarkModeStore
} from '@/common/stores/dark-mode-store'
import sideMenuImage from '~/assets/side-menu.png'
import simpleMenuImage from '~/assets/simple-menu.png'
import topMenuImage from '~/assets/top-menu.png'

const themeSwitcherSlideover = ref(false)
const darkModeStore = useDarkModeStore()
const layoutStore = useLayoutStore()

const setThemeSwitcherSlideover = (value: boolean) =>
  (themeSwitcherSlideover.value = value)

if (import.meta.client) setDarkModeClass()

const layouts: {
  image: string
  name: 'side-menu' | 'simple-menu' | 'top-menu'
}[] = [
  {
    image: sideMenuImage,
    name: 'side-menu'
  },
  {
    image: simpleMenuImage,
    name: 'simple-menu'
  },
  {
    image: topMenuImage,
    name: 'top-menu'
  }
]
</script>

<template>
  <div>
    <Slideover
      :open="themeSwitcherSlideover"
      @close="() => setThemeSwitcherSlideover(false)"
    >
      <SlideoverPanel>
        <a
          class="absolute inset-y-0 left-0 right-auto my-auto -ml-[60px] flex h-8 w-8 items-center justify-center rounded-full border border-white/90 bg-white/5 text-white/90 transition-all hover:rotate-180 hover:scale-105 hover:bg-white/10 focus:outline-none sm:-ml-[105px] sm:h-14 sm:w-14"
          @click="
            e => {
              e.preventDefault()
              setThemeSwitcherSlideover(false)
            }
          "
        >
          <LucideX class="h-3 w-3 stroke-[1] sm:h-8 sm:w-8" />
        </a>
        <SlideoverDescription class="p-0">
          <div class="flex flex-col">
            <div class="px-8 pt-6 pb-8">
              <div class="text-base font-medium">Template</div>
              <div class="mt-0.5 text-slate-500">Elije tu template</div>
              <div class="mt-5 grid grid-cols-2 gap-x-5 gap-y-3.5">
                <div v-for="layout in layouts" :key="layout.name">
                  <a
                    :class="[
                      'h-24 cursor-pointer bg-slate-50 box p-1 block',
                      layoutStore.layout == layout.name
                        ? 'border-2 border-theme-1/60'
                        : ''
                    ]"
                    @click="
                      e => {
                        e.preventDefault()
                        layoutStore.setLayout(layout.name)
                      }
                    "
                  >
                    <div class="w-full h-full overflow-hidden rounded-md">
                      <img
                        class="w-full h-full"
                        :src="layout.image"
                        alt="Midone - Admin Dashboard Template"
                      />
                    </div>
                  </a>
                  <div class="mt-2.5 text-center text-xs capitalize">
                    {{ layout.name.replace('-', ' ') }}
                  </div>
                </div>
              </div>
            </div>
            <div class="border-b border-dashed"></div>
            <div class="px-8 pt-6 pb-8">
              <div class="text-base font-medium">Modo</div>
              <div class="mt-0.5 text-slate-500">Elije tu modo</div>
              <div class="mt-5 grid grid-cols-2 gap-3.5">
                <div>
                  <a
                    :class="[
                      'h-12 cursor-pointer bg-slate-50 box p-1 border-slate-300/80 block',
                      '[&.active]:border-2 [&.active]:border-theme-1/60',
                      !darkModeStore.darkModeValue ? 'active' : ''
                    ]"
                    @click="
                      e => {
                        e.preventDefault()
                        switchDarkMode(false)
                      }
                    "
                  >
                    <div
                      class="h-full overflow-hidden rounded-md bg-slate-200"
                    ></div>
                  </a>
                  <div class="mt-2.5 text-center text-xs capitalize">Claro</div>
                </div>
                <div>
                  <a
                    :class="[
                      'h-12 cursor-pointer bg-slate-50 box p-1 border-slate-300/80 block',
                      '[&.active]:border-2 [&.active]:border-theme-1/60',
                      darkModeStore.darkModeValue ? 'active' : ''
                    ]"
                    @click="
                      e => {
                        e.preventDefault()
                        switchDarkMode(true)
                      }
                    "
                  >
                    <div
                      class="h-full overflow-hidden rounded-md bg-slate-900"
                    ></div>
                  </a>
                  <div class="mt-2.5 text-center text-xs capitalize">
                    Oscuro
                  </div>
                </div>
              </div>
            </div>
          </div>
        </SlideoverDescription>
      </SlideoverPanel>
    </Slideover>
    <div
      class="fixed bottom-0 right-0 z-50 flex items-center justify-center mb-5 mr-5 text-white rounded-full shadow-lg cursor-pointer h-14 w-14 bg-theme-1"
      @click="
        e => {
          e.preventDefault()
          setThemeSwitcherSlideover(true)
        }
      "
    >
      <LucideSettings class="w-5 h-5" />
    </div>
  </div>
</template>
