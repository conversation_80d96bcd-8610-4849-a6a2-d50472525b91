<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { type ButtonStyles, styles } from './button-styles'

const {
  class: classProp,
  elevated,
  rounded,
  size,
  variant
} = defineProps<ButtonStyles>()

const attrs = useAttrs()

const compStyles = computed(() =>
  styles({
    class: classProp,
    elevated,
    rounded,
    size,
    variant
  })
)
</script>

<template>
  <button :class="compStyles" v-bind="attrs">
    <slot />
  </button>
</template>
