<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { twMerge } from 'tailwind-merge'

const props = withDefaults(
  defineProps<{
    as?: object | string
    class?: string
  }>(),
  {
    as: 'div',
    class: ''
  }
)

const attrs = useAttrs()
const computedClass = computed(() =>
  twMerge([
    'px-5 py-3 text-right border-t border-slate-200/60 dark:border-darkmode-400',
    props.class
  ])
)
</script>

<template>
  <component :is="as" :class="computedClass" v-bind="attrs">
    <slot />
  </component>
</template>
