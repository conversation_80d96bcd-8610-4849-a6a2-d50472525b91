<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import {
  DialogPanel as HeadlessDialogPanel,
  TransitionChild
} from '@headlessui/vue'
import { twMerge } from 'tailwind-merge'
import type { ExtractPropTypes, Ref, VNodeRef } from 'vue'

const props = withDefaults(
  defineProps<
    /* @vue-ignore */ ExtractPropTypes<typeof HeadlessDialogPanel> & {
      as?: object | string
      class?: string
    }
  >(),
  {
    as: 'div',
    class: ''
  }
)

defineOptions({ inheritAttrs: false })

const slideover = inject<{
  open: boolean
  size?: 'lg' | 'md' | 'sm' | 'xl'
  zoom: Ref<boolean>
}>('slideover')

const closeButtonRef = ref<VNodeRef>()

const attrs = useAttrs()
const computedClass = computed(() =>
  twMerge([
    'w-[90%] ml-auto h-screen flex flex-col bg-white relative shadow-md transition-transform dark:bg-darkmode-600',
    slideover?.size === 'md' && 'sm:w-[460px]',
    slideover?.size === 'sm' && 'sm:w-[300px]',
    slideover?.size === 'lg' && 'sm:w-[600px]',
    slideover?.size === 'xl' && 'sm:w-[600px] lg:w-[900px]',
    (slideover?.zoom.value ?? false) && 'scale-105',
    props.class
  ])
)
</script>

<template>
  <TransitionChild
    as="div"
    enter="ease-in-out duration-500"
    enterFrom="opacity-0"
    enterTo="opacity-100"
    leave="ease-in-out duration-[400ms]"
    leaveFrom="opacity-100"
    leaveTo="opacity-0"
    class="fixed inset-0 bg-gradient-to-b from-theme-1/50 via-theme-2/50 to-black/50 backdrop-blur-sm"
    aria-hidden="true"
  />
  <TransitionChild
    as="div"
    enter="ease-in-out duration-500"
    enterFrom="opacity-0 -mr-[100%]"
    enterTo="opacity-100 mr-0"
    leave="ease-in-out duration-[400ms]"
    leaveFrom="opacity-100 mr-0"
    leaveTo="opacity-0 -mr-[100%]"
    class="fixed inset-y-0 right-0"
  >
    <HeadlessDialogPanel :is="props.as" :class="computedClass" v-bind="attrs">
      <button ref="closeButtonRef" tabindex="-1" class="sr-only">Cerrar</button>
      <slot />
    </HeadlessDialogPanel>
  </TransitionChild>
</template>
