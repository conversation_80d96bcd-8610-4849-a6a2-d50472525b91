<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { DialogDescription as HeadlessDialogDescription } from '@headlessui/vue'
import { twMerge } from 'tailwind-merge'
import type { ExtractPropTypes } from 'vue'

const props = withDefaults(
  defineProps<
    /* @vue-ignore */ ExtractPropTypes<typeof HeadlessDialogDescription> & {
      as?: object | string
      class?: string
    }
  >(),
  {
    as: 'div',
    class: ''
  }
)

const attrs = useAttrs()
const computedClass = computed(() =>
  twMerge(['p-5 overflow-y-auto flex-1', props.class])
)
</script>

<template>
  <HeadlessDialogDescription
    :is="props.as"
    :class="computedClass"
    v-bind="attrs"
  >
    <slot />
  </HeadlessDialogDescription>
</template>
