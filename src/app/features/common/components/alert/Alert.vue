<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { TransitionRoot } from '@headlessui/vue'
import type { HTMLAttributes } from 'vue'

import { type AlertStyles, styles } from './alert-styles'

const props = withDefaults(
  defineProps<
    AlertStyles &
      /* @vue-ignore */ HTMLAttributes & {
        as?: object | string
        show?: boolean
      }
  >(),
  {
    as: 'div',
    class: '',
    dismissible: false,
    show: true,
    variant: 'primary'
  }
)

const attrs = useAttrs()

const computedStyles = computed(() =>
  styles({
    class: props.class,
    dismissible: props.dismissible,
    variant: props.variant
  })
)
</script>

<template>
  <TransitionRoot
    as="template"
    :show="show"
    enter="transition-all ease-linear duration-150"
    enterFrom="invisible opacity-0 translate-y-1"
    enterTo="visible opacity-100 translate-y-0"
    leave="transition-all ease-linear duration-150"
    leaveFrom="visible opacity-100 translate-y-0"
    leaveTo="invisible opacity-0 translate-y-1"
  >
    <component :is="as" role="alert" :class="computedStyles" v-bind="attrs">
      <template v-if="props.variant === 'primary'">
        <LucideBadgeCheck class="w-6 h-6 mr-4" />
      </template>
      <template v-else-if="props.variant === 'secondary'">
        <LucideBadgeCheck class="w-6 h-6 mr-4" />
      </template>
      <template v-if="props.variant === 'success'">
        <LucideBadgeCheck class="w-6 h-6 mr-4" />
      </template>
      <template v-else-if="props.variant === 'warning'">
        <LucideAlertTriangle class="w-6 h-6 mr-4" />
      </template>
      <template v-else-if="props.variant === 'danger'">
        <LucideAlertCircle class="w-6 h-6 mr-4" />
      </template>
      <template v-else-if="props.variant === 'dark'">
        <LucideAlertCircle class="w-6 h-6 mr-4" />
      </template>
      <slot />
    </component>
  </TransitionRoot>
</template>
