<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { twMerge } from 'tailwind-merge'

const props = defineProps<{ as?: object | string; class?: string }>()

const attrs = useAttrs()

const computedClass = computed(() =>
  twMerge([
    'text-slate-800 py-2 px-3 absolute right-0 my-auto mr-2',
    props.class ?? ''
  ])
)
</script>

<template>
  <component
    :is="as"
    type="button"
    aria-label="Close"
    :class="computedClass"
    v-bind="attrs"
  >
    <slot />
  </component>
</template>
