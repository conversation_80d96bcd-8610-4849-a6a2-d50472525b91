<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-return -->
<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { twMerge } from 'tailwind-merge'

const selectRef = ref<HTMLSelectElement>()

const props = withDefaults(
  defineProps<{
    class?: string
    formSelectSize?: 'lg' | 'sm' | 'xs'
    modelValue?: string
  }>(),
  {
    class: '',
    formSelectSize: 'sm'
  }
)

const attrs = useAttrs()
const formInline = inject<boolean>('formInline', false)

const computedClass = computed(() =>
  twMerge([
    'disabled:bg-slate-100 disabled:cursor-not-allowed disabled:dark:bg-darkmode-800/50',
    '[&[readonly]]:bg-slate-100 [&[readonly]]:cursor-not-allowed [&[readonly]]:dark:bg-darkmode-800/50',
    'transition duration-200 ease-in-out w-full text-sm border-slate-200 shadow-sm rounded-md py-2 px-3 pr-8 focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus:border-primary focus:border-opacity-40 dark:bg-darkmode-800 dark:border-transparent dark:focus:ring-slate-700 dark:focus:ring-opacity-50',
    props.formSelectSize === 'xs' && 'text-xs py-1.5 pl-2 pr-8',
    props.formSelectSize === 'lg' && 'text-sm py-1.5 pl-4 pr-8',
    props.formSelectSize === 'sm' && 'text-sm py-1.5 pl-3 pr-8',
    formInline && 'flex-1',
    props.class
  ])
)
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: Event): void
}>()

const localValue = computed({
  get() {
    if (props.modelValue === undefined) {
      const firstOption = selectRef.value?.querySelector('option')
      if (firstOption !== undefined && firstOption !== null) {
        return firstOption.getAttribute('value') ?? firstOption.text
      }
      return ''
    }

    return props.modelValue
  },
  set(newValue: string) {
    emit('update:modelValue', newValue)
  }
})
</script>

<template>
  <select
    ref="selectRef"
    v-bind="attrs"
    v-model="localValue"
    :class="computedClass"
    @change="e => emit('change', e)"
  >
    <slot />
  </select>
</template>
