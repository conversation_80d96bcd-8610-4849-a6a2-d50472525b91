<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { twMerge } from 'tailwind-merge'

const props = defineProps<{ class?: string }>()

const attrs = useAttrs()
const formInline = inject<boolean>('formInline', false)
const computedClass = computed(() =>
  twMerge([
    'inline-block mb-2',
    formInline && 'mb-2 sm:mb-0 sm:mr-5 sm:text-right',
    props.class ?? ''
  ])
)
</script>

<template>
  <label :class="computedClass" v-bind="attrs">
    <slot />
  </label>
</template>
