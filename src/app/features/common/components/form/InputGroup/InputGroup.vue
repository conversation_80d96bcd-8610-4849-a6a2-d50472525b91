<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { twMerge } from 'tailwind-merge'

const props = defineProps<{ class?: string }>()

const attrs = useAttrs()
const computedClass = computed(() => twMerge(['flex', props.class ?? '']))

provide<boolean>('inputGroup', true)
</script>

<template>
  <div :class="computedClass" v-bind="attrs">
    <slot />
  </div>
</template>
