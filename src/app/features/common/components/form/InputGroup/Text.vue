<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { twMerge } from 'tailwind-merge'

const props = defineProps<{ class?: string }>()

const attrs = useAttrs()
const inputGroup = inject<boolean>('inputGroup')
const computedClass = computed(() =>
  twMerge([
    'py-2 px-3 bg-slate-100 border shadow-sm border-slate-200 text-slate-600 dark:bg-darkmode-900/20 dark:border-darkmode-900/20 dark:text-slate-400',
    (inputGroup ?? false) &&
      'rounded-none [&:not(:first-child)]:border-l-transparent first:rounded-l last:rounded-r',
    props.class ?? ''
  ])
)
</script>

<template>
  <div :class="computedClass" v-bind="attrs">
    <slot />
  </div>
</template>
