<!-- eslint-disable @typescript-eslint/strict-boolean-expressions -->
<!-- eslint-disable @typescript-eslint/no-unsafe-argument -->
<!-- eslint-disable @typescript-eslint/no-unsafe-member-access -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<script setup lang="ts">
import { twMerge } from 'tailwind-merge'

const props = defineProps<{
  class?: string
  formInputSize?: 'lg' | 'sm'
  modelValue?: number | string
  rounded?: boolean
  type?: string
  value?: number | string
}>()
const attrs = useAttrs()
const formInline = inject<boolean>('formInline', false)
const inputGroup = inject<boolean>('inputGroup', false)

const computedClass = computed(() =>
  twMerge([
    'disabled:bg-slate-100 disabled:cursor-not-allowed dark:disabled:bg-darkmode-800/50 dark:disabled:border-transparent',
    '[&[readonly]]:bg-slate-100 [&[readonly]]:cursor-not-allowed [&[readonly]]:dark:bg-darkmode-800/50 [&[readonly]]:dark:border-transparent',
    'transition duration-200 ease-in-out w-full text-sm border-slate-200 shadow-sm rounded-md placeholder:text-slate-400/90 focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus:border-primary focus:border-opacity-40 dark:bg-darkmode-800 dark:border-transparent dark:focus:ring-slate-700 dark:focus:ring-opacity-50 dark:placeholder:text-slate-500/80',
    props.formInputSize === 'sm' && 'text-xs py-1.5 px-2',
    props.formInputSize === 'lg' && 'text-lg py-1.5 px-4',
    props.rounded && 'rounded-full',
    formInline && 'flex-1',
    inputGroup &&
      'rounded-none [&:not(:first-child)]:border-l-transparent first:rounded-l last:rounded-r z-10',
    props.class ?? ''
  ])
)

const emit = defineEmits<(e: 'update:modelValue', value: string) => void>()

const local = ref(String(props.modelValue ?? ''))

watch(
  () => props.modelValue,
  val => {
    const stringVal = String(val ?? '')
    if (stringVal !== local.value) local.value = stringVal
  },
  { immediate: true }
)

onMounted(() => {
  if (props.value !== undefined) local.value = String(props.value)
})

watch(local, val => {
  emit('update:modelValue', val)
})
</script>

<template>
  <input
    v-bind="attrs"
    v-model="local"
    :class="computedClass"
    :type="props.type ?? 'text'"
  />
</template>
