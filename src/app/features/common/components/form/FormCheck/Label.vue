<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { twMerge } from 'tailwind-merge'

const props = defineProps<{ class?: string }>()

const attrs = useAttrs()
const computedClass = computed(() =>
  twMerge(['cursor-pointer ml-2', props.class ?? ''])
)
</script>

<template>
  <label :class="computedClass" v-bind="attrs">
    <slot />
  </label>
</template>
