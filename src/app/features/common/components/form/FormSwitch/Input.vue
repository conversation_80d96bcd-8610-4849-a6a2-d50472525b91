<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { twMerge } from 'tailwind-merge'
import type { InputHTMLAttributes } from 'vue'

import FormCheck from '../FormCheck'

defineOptions({ inheritAttrs: false })

const props = defineProps<
  InputHTMLAttributes & {
    class?: string
    modelValue?: string
    type: 'checkbox'
  }
>()
const emit = defineEmits<(e: 'update:modelValue', value: string) => void>()
const attrs = useAttrs()

const computedClass = computed(() =>
  twMerge([
    'w-[38px] h-[24px] p-px rounded-full relative',
    'before:w-[20px] before:h-[20px] before:shadow-[1px_1px_3px_rgba(0,0,0,0.25)] before:transition-[margin-left] before:duration-200 before:ease-in-out before:absolute before:inset-y-0 before:my-auto before:rounded-full before:dark:bg-darkmode-600',
    'checked:bg-primary checked:border-primary checked:bg-none',
    'before:checked:ml-[14px] before:checked:bg-white',
    props.class ?? ''
  ])
)

const localValue = computed({
  get() {
    return props.modelValue
  },
  set(newValue: string) {
    emit('update:modelValue', newValue)
  }
})
</script>

<template>
  <FormCheck.Input
    v-model="localValue"
    :type="props.type"
    :class="computedClass"
    v-bind="attrs"
  />
</template>
