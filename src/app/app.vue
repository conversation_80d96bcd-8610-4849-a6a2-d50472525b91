<!-- eslint-disable unicorn/no-useless-undefined -->
<!-- eslint-disable @typescript-eslint/no-unsafe-call -->
<!-- eslint-disable @typescript-eslint/no-unsafe-assignment -->
<script setup lang="ts">
import { useRoute } from 'vue-router'

const route = useRoute()

const currentLayout = computed(() => {
  if (route.path.startsWith('/dash')) return 'dashboard'
  return undefined
})
</script>

<template>
  <NuxtLayout :name="currentLayout">
    <NuxtPage />
  </NuxtLayout>
</template>
