/*import { useLoginStore } from '@/common/stores/login-store'
import { jwtDecode } from 'jwt-decode'

export default defineNuxtRouteMiddleware((to, _) => {
  const loginStore = useLoginStore()
  const token = loginStore.user?.token

  if (token !== undefined) {
    try {
      const decodedToken = jwtDecode(token)
      const currentTime = Date.now() / 1000

      if (decodedToken.exp ?? 0 < currentTime) {
        loginStore.logout()
        return navigateTo('/')
      }
    } catch (error) {
      loginStore.logout()
      return navigateTo('/')
    }
  } else if (to.name !== '') {
    return navigateTo('/')
  }
})
*/
