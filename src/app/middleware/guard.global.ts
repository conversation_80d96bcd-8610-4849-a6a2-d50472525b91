import { useLoginStore } from '@/common/stores/login-store'

export default defineNuxtRouteMiddleware((to, _) => {
  const loginStore = useLoginStore()

  if (
    to.path === '/dash/admin/aprob-wm' &&
    (loginStore.user?.role === 'user' || loginStore.user?.role === 'pm')
  )
    return navigateTo('/dash')

  if (
    to.path === '/dash/admin/aprob-pm' &&
    (loginStore.user?.role === 'user' || loginStore.user?.role === 'wm')
  )
    return navigateTo('/dash')

  if (
    to.path === '/dash/admin/tareas' &&
    (loginStore.user?.role === 'user' || loginStore.user?.role === 'pm')
  )
    return navigateTo('/dash')

  if (
    to.path === '/dash/admin/consultor-proyecto' &&
    loginStore.user?.role === 'user'
  )
    return navigateTo('/dash')

  if (
    (to.path === '/dash/admin/consultores' ||
      to.path === '/dash/admin/proyectos' ||
      to.path === '/dash/admin/calendario-festivos' ||
      to.path === '/dash/admin/conf-vacaciones' ||
      to.path === '/dash/admin/limites-horas' ||
      to.path === '/dash/admin/clientes') &&
    loginStore.user?.role !== 'admin'
  )
    return navigateTo('/dash')

  if (
    to.path === '/dash/consultor-tarea' &&
    (loginStore.user?.role === 'user' || loginStore.user?.role === 'pm')
  )
    return navigateTo('/dash')

  if (to.path === '/dash/informes' && loginStore.user?.role === 'user')
    return navigateTo('/dash')

  if (
    to.path === '/dash/consultas' &&
    (loginStore.user?.role === 'user' || loginStore.user?.role === 'wm')
  )
    return navigateTo('/dash')

  if (
    to.path === '/dash/accesos' &&
    (loginStore.user?.role === 'user' || loginStore.user?.role === 'wm')
  )
    return navigateTo('/dash')
})
