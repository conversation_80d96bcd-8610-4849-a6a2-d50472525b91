import { HorasRegistradas } from '../models/horas-registradas'
import { HorasPendientes } from '../models/horas-pendientes'

export const transformHorasRegistradas = (data: HorasRegistradas[]) => {
  const res = [] as Record<string, string>[]

  Object.values(
    data.reduce(
      (acc, rec) => {
        if (!acc[rec.id]) acc[rec.id] = []
        acc[rec.id].push(rec)
        return acc
      },
      {} as Record<number, HorasRegistradas[]>
    )
  ).forEach(grupo => {
    const { id, nombreconsultor } = grupo[0]

    res.push({ id: id.toString(), nombreconsultor, estado: '', horas: '' })

    grupo.forEach(g =>
      res.push({
        id: '',
        nombreconsultor: '',
        estado: g.estado,
        horas: g.horas.toString()
      })
    )

    res.push({
      id: '',
      nombreconsultor: '',
      estado: 'TOTAL',
      horas: grupo.reduce((sum, g) => sum + g.horas, 0).toString()
    })
  })

  return res
}

export const transformHorasPendientes = (data: HorasPendientes[]) => {
  const res = [] as Record<string, string>[]

  Object.values(
    data.reduce(
      (acc, rec) => {
        if (!acc[rec.id]) acc[rec.id] = []
        acc[rec.id].push(rec)
        return acc
      },
      {} as Record<number, HorasPendientes[]>
    )
  ).forEach(grupo => {
    const { id, nombreconsultor } = grupo[0]

    res.push({
      id: id.toString(),
      nombreconsultor,
      estado: '',
      nombreproyecto: '',
      jefedeproyecto: '',
      jefefuncional: '',
      aprobador: '',
      horas: ''
    })

    grupo.forEach(g =>
      res.push({
        id: '',
        nombreconsultor: '',
        estado: g.estado,
        nombreproyecto: g.nombreproyecto,
        jefedeproyecto: g.jefedeproyecto,
        jefefuncional: g.jefefuncional,
        aprobador: g.aprobador,
        horas: g.horas.toString()
      })
    )

    res.push({
      id: '',
      nombreconsultor: '',
      estado: '',
      nombreproyecto: '',
      jefedeproyecto: '',
      jefefuncional: '',
      aprobador: 'TOTAL',
      horas: grupo.reduce((sum, g) => sum + g.horas, 0).toString()
    })
  })

  return res
}
