const keys = <T extends object>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj

  return Object.fromEntries(
    Object.entries(obj).map(([key, value]) => {
      let newKey = key.toLowerCase().replace(/_/g, '')
      if (newKey === 'id') newKey = 'id'
      return [newKey, keys(value)]
    })
  ) as T
}

export const formatDates = <T extends object>(rows: T[]): T[] =>
  rows.map(row => {
    const newRow: any = {}
    for (const [key, value] of Object.entries(row)) {
      if (value instanceof Date) {
        newRow[key] = value.toISOString().slice(0, 10)
      } else if (
        typeof value === 'string' &&
        /^\d{4}-\d{2}-\d{2}T.*Z$/.test(value)
      ) {
        const d = new Date(value)
        if (!isNaN(d.getTime())) {
          newRow[key] = d.toISOString().slice(0, 10)
          continue
        }
        newRow[key] = value
      } else {
        newRow[key] = value
      }
    }
    return newRow
  })

export const arrayKeys = <T extends object>(arr: T[]): T[] => arr.map(keys)
