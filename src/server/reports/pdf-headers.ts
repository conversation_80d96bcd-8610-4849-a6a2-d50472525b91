import PDFDocument from 'pdfkit'
import dayjs from 'dayjs'

export const stdHeader = (
  doc: typeof PDFDocument,
  title: string,
  subtitle: string,
  size = 'A4',
  inLoop = false
) => {
  if (!inLoop) {
    doc.fontSize(10).text(dayjs().format('DD/MM/YYYY HH:mm'), 0, 15, {
      align: 'right'
    })

    doc.moveDown(3.5)
  }
  doc.image(
    process.env.NODE_ENV === 'production'
      ? 'tm.png'
      : './src/server/resources/tm.png',
    25,
    25,
    { width: 150 }
  )

  let sizeAlter = 420

  switch (size) {
    case 'A2':
      sizeAlter = 1010
      break
    case 'A3':
      sizeAlter = 660
      break
    case 'A4':
    default:
      sizeAlter = 420
      break
  }

  doc.image(
    process.env.NODE_ENV === 'production'
      ? 'tm-color.png'
      : './src/server/resources/tm-color.png',
    sizeAlter,
    25,
    { width: 150 }
  )

  if (inLoop) return

  doc.fontSize(20).text(title, { align: 'center' })
  doc.moveDown(0.3)
  if (subtitle !== '') {
    doc.fontSize(12).text(subtitle, { align: 'center' })
    doc.moveDown(0.2)
  }
}
