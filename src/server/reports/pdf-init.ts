import PDFDocument from 'pdfkit'
import { piped, tap } from 'rambdax'

export const pdfInit = ({
  margins = { left: 20, right: 20, top: 80, bottom: 20 },
  size = 'A4',
  headerLength
}: {
  size?: string
  margins?: { left: number; right: number; top: number; bottom: number }
  headerLength: number
}) =>
  piped(new PDFDocument({ size, margins }), doc => ({
    doc,
    colWidth:
      (doc.page.width - doc.page.margins.left - doc.page.margins.right) /
      headerLength
  }))
