import PDFDocument from 'pdfkit'

export const tableHeader = (
  doc: typeof PDFDocument,
  metadata: { label: string; key: string }[],
  colWidth: number,
  rowHeight: number,
  y: number,
  headerFontSize = 12
) => {
  let x = doc.page.margins.left
  doc.fontSize(headerFontSize).fillColor('white')
  metadata.forEach(m => {
    doc.rect(x, y, colWidth, rowHeight).fill('#02D')
    doc
      .fillColor('white')
      .text(
        m.label.charAt(0).toUpperCase() + m.label.slice(1).toLowerCase(),
        x + 5,
        y + 5,
        { width: colWidth - 10, align: 'center' }
      )
    x += colWidth
  })
}
