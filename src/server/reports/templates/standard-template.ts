import { buildTable } from '../build-table'
import { stdHeader } from '../pdf-headers'
import { pdfInit } from '../pdf-init'
import { drawTable, measureRowHeight } from '../pdf-table'
import { tableHeader } from '../pdf-table-head'
import { piped, tap } from 'rambdax'
import { PassThrough } from 'stream'

export const standardTemplate = ({
  title,
  unsortData,
  metadata,
  subtitle = '',
  size = 'A4',
  headerFontSize = 12,
  baseRowHeight = 20
}: {
  fileName: string
  title: string
  unsortData: Record<string, unknown>[]
  metadata: { label: string; key: string }[]
  subtitle?: string
  headerFontSize?: number
  size?: string
  baseRowHeight?: number
}) =>
  piped(
    pdfInit({ headerLength: metadata.length, size }),
    document => ({
      ...document,
      stream: document.doc.pipe(new PassThrough())
    }),
    tap(({ doc }) => stdHeader(doc, title, subtitle, size)),
    tap(({ doc, colWidth }) =>
      tableHeader(doc, metadata, colWidth, baseRowHeight, doc.y, headerFontSize)
    ),
    document => ({
      ...document,
      y: document.doc.y + baseRowHeight,
      bottomMargin: document.doc.page.margins.bottom
    }),
    tap(({ doc, bottomMargin, colWidth }) => {
      let y = doc.y
      buildTable(unsortData, metadata).forEach(record => {
        const rowHeight = measureRowHeight(doc, record, colWidth, baseRowHeight)

        if (y + rowHeight + bottomMargin > doc.page.height) {
          doc.addPage()
          stdHeader(doc, title, subtitle, size, true)
          y = doc.y
          tableHeader(doc, metadata, colWidth, baseRowHeight, y)
          y += baseRowHeight
        }

        y = drawTable(doc, record, colWidth, rowHeight, y)
      })
    }),
    tap(({ doc }) => doc.end()),
    ({ stream }) => stream
  )
