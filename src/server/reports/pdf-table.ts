import PDFDocument from 'pdfkit'

export const measureRowHeight = (
  doc: typeof PDFDocument,
  record: Record<string, unknown>,
  colWidth: number,
  baseRowHeight: number
) => {
  const cellHeights = Object.values(record).map(value => {
    const text = (value ?? '').toString()
    return (
      doc.heightOfString(text, {
        width: colWidth - 10,
        align: 'center'
      }) + 10
    )
  })
  return Math.max(...cellHeights, baseRowHeight)
}

export const drawTable = (
  doc: typeof PDFDocument,
  record: Record<string, unknown>,
  colWidth: number,
  rowHeight: number,
  y: number
) => {
  let x = doc.page.margins.left

  Object.values(record).forEach(value => {
    const text = (value ?? '').toString()

    doc.rect(x, y, colWidth, rowHeight).stroke()
    doc
      .fontSize(9)
      .fillColor('black')
      .text(text, x + 5, y + 5, {
        width: colWidth - 10,
        align: 'center'
      })

    x += colWidth
  })

  return y + rowHeight
}
