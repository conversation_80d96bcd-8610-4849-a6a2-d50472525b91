export const buildTable = <T extends object>(
  data: T[],
  metadata: { label: string; key: keyof T }[],
  isExcel = false
) =>
  data.map(row =>
    Object.fromEntries(
      metadata.map(m => {
        const val = row[m.key]
        let str =
          val === undefined || val === null
            ? ''
            : typeof val === 'object'
              ? JSON.stringify(val)
              : String(val)

        if (str.length > 60 && !isExcel) str = str.slice(0, 60) + '...'

        return [String(m.key), str]
      })
    )
  ) as T[]
