import ExcelJS from 'exceljs'
import { buildTable } from './build-table'
import { piped, tap } from 'rambdax'
import { PassThrough } from 'stream'

const setupHeader = (
  sheet: ExcelJS.Worksheet,
  title: string,
  subtitle: string
) => {
  sheet.mergeCells('A1', 'C1')
  sheet.getCell('A1').value = title
  sheet.getCell('A1').font = { bold: true, size: 16 }
  sheet.getCell('A1').alignment = { horizontal: 'center' }
  sheet.mergeCells('A2', 'C2')
  if (subtitle === '') return
  sheet.getCell('A2').value = subtitle
  sheet.getCell('A2').alignment = { horizontal: 'center' }
}

const setHeaders = (
  sheet: ExcelJS.Worksheet,
  metadata: { label: string; key: string }[]
) =>
  sheet.addRow(metadata.map(m => m.label)).eachCell(cell => {
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '0000FF' }
    }
    cell.font = { color: { argb: 'FFFFFF' }, bold: true }
    cell.alignment = { horizontal: 'center', vertical: 'middle' }
  })

export const generateExcel = (
  unsortData: Record<string, unknown>[],
  metadata: { label: string; key: string }[],
  title: string,
  subtitle = ''
) => {
  const workbook = new ExcelJS.Workbook()
  const stream = new PassThrough()
  piped(
    workbook.addWorksheet('Reporte'),
    tap(sheet => setupHeader(sheet, title, subtitle)),
    tap(sheet => setHeaders(sheet, metadata)),
    tap(sheet =>
      buildTable(unsortData, metadata, true).forEach(record =>
        sheet.addRow(Object.values(record))
      )
    )
  )
  workbook.xlsx.write(stream).then(() => stream.end())
  return stream
}
