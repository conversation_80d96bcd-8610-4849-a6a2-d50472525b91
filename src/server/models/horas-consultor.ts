import { type } from 'arktype'

export const horasConsultor = type({
  id: 'number',
  fecha: 'string',
  idconsultor: 'number',
  nombreconsultor: 'string',
  horas: 'number',
  tarea: 'string',
  descripcion: 'string',
  etapa: 'string',
  nombreproyecto: 'string',
  estado: 'string'
})

export type HorasConsultor = typeof horasConsultor.infer

export const horasConsultorMetadata: {
  label: string
  key: keyof HorasConsultor
}[] = [
  {
    label: 'ID',
    key: 'id'
  },
  {
    label: 'Consultor',
    key: 'nombreconsultor'
  },
  {
    label: 'Horas',
    key: 'horas'
  },
  {
    label: 'Fe<PERSON>',
    key: 'fecha'
  },
  {
    label: 'Tarea',
    key: 'tarea'
  },
  {
    label: 'Descripción',
    key: 'descripcion'
  },
  {
    label: 'Etapa',
    key: 'etapa'
  },
  {
    label: 'Proyecto',
    key: 'nombreproyecto'
  },
  {
    label: 'Estado',
    key: 'estado'
  }
]
