import { type } from 'arktype'

export const listadoConsultor = type({
  id: 'number',
  nombre: 'string',
  apellido: 'string',
  direccion: 'string',
  pais: 'string',
  jefefuncional: 'number',
  usuario: 'string',
  passwd: 'string',
  role: 'string',
  fechaingreso: 'string',
  esjefefunc: 'string',
  esje<PERSON><PERSON>roy: 'string',
  vacdispperantnat: 'number?',
  vacdispperantabil: 'number?',
  vacdispanoantnat: 'number?',
  vacdispanoantabil: 'number?',
  vacdispperactnat: 'number?',
  vacdispperactabil: 'number?',
  vacdispanoactnat: 'number?',
  vacdispanoactabil: 'number?',
  observac: 'string?',
  email: 'string',
  jefefunc: 'string'
})

export type ListadoConsultor = typeof listadoConsultor.infer

export const listadoConsultorMetadata: {
  label: string
  key: keyof ListadoConsultor
}[] = [
  {
    key: 'id',
    label: 'ID'
  },
  {
    key: 'nombre',
    label: 'Nombre'
  },
  {
    key: 'apellido',
    label: 'Apellido'
  },
  {
    key: 'pais',
    label: 'País'
  },
  {
    key: 'direccion',
    label: 'Dirección'
  },
  {
    key: 'fechaingreso',
    label: 'Fecha Ingreso'
  },
  {
    key: 'jefefunc',
    label: 'Jefe Funcional'
  },
  {
    key: 'esjefefunc',
    label: 'Es J. Funcional?'
  },
  {
    key: 'esjefeproy',
    label: 'Es J. Proyecto?'
  },
  {
    key: 'role',
    label: 'Rol'
  }
]
