import { type } from 'arktype'

export const horasRegistradasAlert = type({
  id: 'number',
  nombreconsultor: 'string',
  horas: 'number',
  email: 'string',
  emailjefefuncional: 'string',
  fechaingreso: 'string'
})

export type HorasRegistradasAlert = typeof horasRegistradasAlert.infer

export const horasRegistradasAlertMetadata: {
  label: string
  key: keyof HorasRegistradasAlert
}[] = [
  {
    label: 'ID',
    key: 'id'
  },
  {
    label: 'Nombre Consultor',
    key: 'nombreconsultor'
  },
  {
    label: 'Horas',
    key: 'horas'
  },
  {
    label: 'Fecha Ingreso',
    key: 'fechaingreso'
  },
  {
    label: 'Email',
    key: 'email'
  },
  {
    label: 'Email Jefe Funcional',
    key: 'emailjefefuncional'
  }
]
