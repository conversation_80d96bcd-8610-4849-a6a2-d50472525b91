import { type } from 'arktype'

export const por<PERSON>roy<PERSON>to = type({
  id: 'number',
  fecha: 'string',
  consultor: 'string',
  horastrabajadas: 'number',
  nombretarea: 'string',
  horas: 'number?',
  descripcion: 'string',
  etapa: 'string',
  nombreproyecto: 'string',
  horasproy: 'number?',
  nombrecliente: 'string',
  estado: 'string'
})

export type PorProyecto = typeof porProyecto.infer

export const porProyectoMetadata: {
  label: string
  key: keyof PorProyecto
}[] = [
  {
    label: 'Fecha',
    key: 'fecha'
  },
  {
    label: 'Proyecto',
    key: 'nombreproyecto'
  },
  {
    label: 'Horas',
    key: 'horastrabajadas'
  },
  {
    label: 'Tarea',
    key: 'nombretarea'
  },
  {
    label: 'H. Presup.',
    key: 'horasproy'
  },
  {
    label: 'Horas',
    key: 'horas'
  },
  {
    label: 'Consultor',
    key: 'consultor'
  },
  {
    label: '<PERSON><PERSON><PERSON>',
    key: 'etapa'
  },
  {
    label: 'Descripción',
    key: 'descripcion'
  }
]
