import { type } from 'arktype'

export const porCliente = type({
  id: 'number',
  fecha: 'string',
  consultor: 'string',
  horastrabajadas: 'number',
  nombretarea: 'string',
  horas: 'number?',
  descripcion: 'string',
  etapa: 'string',
  nombreproyecto: 'string',
  horasproy: 'number?',
  nombrecliente: 'string',
  estado: 'string'
})

export type PorCliente = typeof porCliente.infer

export const porClienteMetadata: {
  label: string
  key: keyof PorCliente
}[] = [
  {
    label: 'Fecha',
    key: 'fecha'
  },
  {
    label: 'Cliente',
    key: 'nombrecliente'
  },
  {
    label: 'Proyecto',
    key: 'nombreproyecto'
  },
  {
    label: 'Consultor',
    key: 'consultor'
  },
  {
    label: 'Horas',
    key: 'horastrabajadas'
  },
  {
    label: 'Tarea',
    key: 'nombretarea'
  },
  {
    label: 'Etapa',
    key: 'etapa'
  },
  {
    label: 'Descripción',
    key: 'descripcion'
  }
]
