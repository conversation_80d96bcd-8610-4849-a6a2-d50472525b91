import { type } from 'arktype'

export const consultoresProyecto = type({
  id: 'number',
  nombre: 'string',
  apellido: 'string',
  direccion: 'string',
  pais: 'string[]',
  jefefuncional: 'number',
  usuario: 'string',
  passwd: 'string',
  role: 'string',
  fechaingreso: 'string',
  esje<PERSON><PERSON>nc: 'string',
  esjefe<PERSON>roy: 'string',
  vacdispperantnat: 'number?',
  vacdispperantabil: 'number?',
  vacdispanoantnat: 'number?',
  nombrecliente: 'string',
  vacdispanoantabil: 'number?',
  vacdispperactnat: 'number?',
  nombreproyecto: 'string',
  vacdispperactabil: 'number?',
  vacdispanoactnat: 'number?',
  vacdispanoactabil: 'number?',
  observac: 'string?',
  email: 'string',
  jefefunc: 'string'
})

export type ConsultoresProyecto = typeof consultoresProyecto.infer

export const consultoresProyectoMetadata: {
  label: string
  key: keyof ConsultoresProyecto
}[] = [
  {
    key: 'nombreproyecto',
    label: 'Proyecto'
  },
  {
    key: 'nombre',
    label: 'Nombre'
  },
  {
    key: 'apellido',
    label: 'Apellido'
  },
  {
    key: 'pais',
    label: 'País'
  },
  {
    key: 'direccion',
    label: 'Dirección'
  },
  {
    key: 'nombrecliente',
    label: 'Cliente'
  }
]
