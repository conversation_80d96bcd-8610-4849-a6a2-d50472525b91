import { type } from 'arktype'

export const horasRegistadasConsul = type({
  fecha: 'string',
  horas: 'number',
  descrip: 'string',
  proyecto: 'string',
  tarea: 'string',
  estado: 'string',
  comentario: 'string'
})

export type HorasRegistradasConsul = typeof horasRegistadasConsul.infer

export const horasRegistradasConsulMetadata: {
  label: string
  key: keyof HorasRegistradasConsul
}[] = [
  {
    label: 'Fecha',
    key: 'fecha'
  },
  {
    label: 'Horas',
    key: 'horas'
  },
  {
    label: 'Descripción',
    key: 'descrip'
  },
  {
    label: 'Estado',
    key: 'estado'
  },
  {
    label: 'Proyecto',
    key: 'proyecto'
  },
  {
    label: 'Tarea',
    key: 'tarea'
  },
  {
    label: 'Comentario',
    key: 'comentario'
  }
]
