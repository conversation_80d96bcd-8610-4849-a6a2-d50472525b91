import { type } from 'arktype'

export const vacacionesConsumidas = type({
  id: 'number',
  nombrecompleto: 'string',
  pais: 'string',
  fechaingreso: 'string',
  fechaperantinic: 'string',
  fechaperantfin: 'string',
  fechaperactinic: 'string',
  fechaperactfin: 'string',
  fechaanoantinic: 'string',
  fechaanoantfin: 'string',
  fechaanoactinic: 'string',
  fechaanoactfin: 'string',
  vacconsperantnat: 'number',
  vacconsperanthabil: 'number',
  vacconsanoantnat: 'number',
  vacconsanoanthabil: 'number',
  vacconsperactnat: 'number',
  vacconsperacthabil: 'number',
  vacconsanoactnat: 'number',
  vacconsanoacthabil: 'number'
})

export type VacacionesConsumidas = typeof vacacionesConsumidas.infer

export const vacacionesConsumidasMetadata: {
  label: string
  key: keyof VacacionesConsumidas
}[] = [
  {
    label: 'ID',
    key: 'id'
  },
  {
    label: 'Nombre',
    key: 'nombrecompleto'
  },
  {
    label: 'Fecha Ingreso',
    key: 'fechaingreso'
  },
  {
    label: 'Inic. Per. Ant.',
    key: 'fechaperantinic'
  },
  {
    label: 'Fin Per. Ant.',
    key: 'fechaperantfin'
  },
  {
    label: 'Inic. Per. Act.',
    key: 'fechaperactinic'
  },
  {
    label: 'Fin Per. Act.',
    key: 'fechaperactfin'
  },
  {
    label: 'Inic. Año Ant.',
    key: 'fechaanoantinic'
  },
  {
    label: 'Fin Año Ant.',
    key: 'fechaanoantfin'
  },
  {
    label: 'Inic. Año Act.',
    key: 'fechaanoactinic'
  },
  {
    label: 'Fin Año Act.',
    key: 'fechaanoactfin'
  },
  {
    label: 'Nat. Per. Ant. ',
    key: 'vacconsperantnat'
  },
  {
    label: 'Nat. Per. Act. ',
    key: 'vacconsperactnat'
  },
  {
    label: 'Nat. Año. Ant. ',
    key: 'vacconsanoantnat'
  },
  {
    label: 'Nat. Año. Act. ',
    key: 'vacconsanoactnat'
  },
  {
    label: 'Hab. Per. Ant. ',
    key: 'vacconsperanthabil'
  },
  {
    label: 'Hab. Per. Act. ',
    key: 'vacconsperacthabil'
  },
  {
    label: 'Hab. Año. Ant. ',
    key: 'vacconsanoanthabil'
  },
  {
    label: 'Hab. Año. Act. ',
    key: 'vacconsanoacthabil'
  }
]
