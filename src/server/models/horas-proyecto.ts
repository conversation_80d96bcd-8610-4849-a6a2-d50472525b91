import { type } from 'arktype'

export const horas<PERSON>roy<PERSON><PERSON> = type({
  nombrejefe: 'string',
  apellidojefe: 'string',
  id: 'number',
  nombre: 'string',
  apellido: 'string',
  direccion: 'string',
  pais: 'string',
  jefefuncional: 'number',
  role: 'string',
  proyecto: 'string',
  horastrabajadas: 'number',
  numsemana: 'number'
})

export type HorasProyecto = typeof horasProyecto.infer

export const horasProyectoMetadata: {
  label: string
  key: keyof HorasProyecto
}[] = [
  {
    key: 'nombrejefe',
    label: 'Nombre Jefe'
  },
  {
    key: 'apellidoje<PERSON>',
    label: 'Apellido Jefe'
  },
  {
    key: 'id',
    label: 'ID'
  },
  {
    key: 'nombre',
    label: 'Nombre Consultor'
  },
  {
    key: 'apellido',
    label: 'Apellido Consultor'
  },
  {
    key: 'pais',
    label: 'País'
  },
  {
    key: 'proyecto',
    label: 'Proyecto'
  },
  {
    key: 'horastraba<PERSON><PERSON>',
    label: '<PERSON><PERSON>'
  },
  {
    key: 'numsemana',
    label: '<PERSON><PERSON>'
  }
]
