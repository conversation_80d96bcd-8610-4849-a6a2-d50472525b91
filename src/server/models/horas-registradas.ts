import { type } from 'arktype'

export const horasRegistadas = type({
  id: 'number',
  nombreconsultor: 'string',
  estado: 'string',
  horas: 'number'
})

export type HorasRegistradas = typeof horasRegistadas.infer

export const horasRegistradasMetadata: {
  label: string
  key: keyof HorasRegistradas
}[] = [
  {
    label: 'ID Consultor',
    key: 'id'
  },
  {
    label: 'Nombre Consultor',
    key: 'nombreconsultor'
  },
  {
    label: 'Estado',
    key: 'estado'
  },
  {
    label: 'Horas',
    key: 'horas'
  }
]
