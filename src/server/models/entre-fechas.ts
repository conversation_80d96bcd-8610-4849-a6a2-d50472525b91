import { type } from 'arktype'

export const entreFechas = type({
  id: 'number',
  fecha: 'string',
  consultor: 'string',
  horastrabajadas: 'number',
  nombretarea: 'string',
  horas: 'number?',
  nombre: 'string',
  descripcion: 'string',
  etapa: 'string',
  nombreproyecto: 'string',
  horasproy: 'number?',
  nombrecliente: 'string',
  estado: 'string',
  pais: 'string'
})

export type EntreFechas = typeof entreFechas.infer

export const entreFechasMetadata: {
  label: string
  key: keyof EntreFechas
}[] = [
  {
    label: 'Fecha',
    key: 'fecha'
  },
  {
    label: 'Consultor',
    key: 'nombre'
  },
  {
    label: 'Proyecto',
    key: 'nombreproyecto'
  },
  {
    label: 'Horas',
    key: 'horastrabajadas'
  },
  {
    label: 'Tarea',
    key: 'nombretarea'
  },
  {
    label: 'Descripción',
    key: 'descripcion'
  },
  {
    label: '<PERSON>ta<PERSON>',
    key: 'etapa'
  },
  {
    label: 'Pa<PERSON>',
    key: 'pais'
  }
]
