import { type } from 'arktype'

export const proyectoInterno = type({
  id: 'number',
  nombreconsultor: 'string',
  horas: 'number',
  htotal: 'number',
  tarea: 'string',
  email: 'string',
  emailjefefuncional: 'string',
  fechaingreso: 'string'
})

export type ProyectoInterno = typeof proyectoInterno.infer

export const proyectoInternoMetadata: {
  label: string
  key: keyof ProyectoInterno
}[] = [
  {
    key: 'id',
    label: 'ID'
  },
  {
    key: 'nombreconsultor',
    label: 'Nombre'
  },
  {
    key: 'horas',
    label: 'Horas'
  },
  {
    key: 'htotal',
    label: 'Horas Totales'
  },
  {
    key: 'email',
    label: 'Email'
  },
  {
    key: 'emailjefefuncional',
    label: 'Email Jefe Funcional'
  },
  {
    key: 'fechaingreso',
    label: 'Fecha Ingreso'
  }
]
