import { type } from 'arktype'

export const vacacionesSolicitadas = type({
  id: 'number',
  nombrecompleto: 'string',
  pais: 'string',
  diainicio: 'string',
  diafin: 'string',
  estado: 'string'
})

export type VacacionesSolicitadas = typeof vacacionesSolicitadas.infer

export const vacacionesSolicitadasMetadata: {
  label: string
  key: keyof VacacionesSolicitadas
}[] = [
  {
    label: 'ID',
    key: 'id'
  },
  {
    label: 'Consultor',
    key: 'nombrecompleto'
  },
  {
    label: 'País',
    key: 'pais'
  },
  {
    label: 'Día Inicio',
    key: 'diainicio'
  },
  {
    label: 'Día Final',
    key: 'diafin'
  },
  {
    label: 'Estado',
    key: 'estado'
  }
]
