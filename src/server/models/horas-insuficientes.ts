import { type } from 'arktype'

export const horasInsuficientes = type({
  id: 'number',
  nombreconsultor: 'string',
  horas: 'number',
  jefefuncional: 'string',
  pais: 'string'
})

export type HorasInsuficientes = typeof horasInsuficientes.infer

export const horasInsuficientesMetadata: {
  label: string
  key: keyof HorasInsuficientes
}[] = [
  {
    key: 'id',
    label: 'ID Consultor'
  },
  {
    key: 'nombreconsultor',
    label: 'Nombre Consultor'
  },
  {
    key: 'horas',
    label: 'Horas'
  },
  {
    key: 'jefefuncional',
    label: 'Jefe Funcional'
  },

  {
    key: 'pais',
    label: 'País'
  }
]
