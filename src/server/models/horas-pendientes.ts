import { type } from 'arktype'

export const horasPendientes = type({
  id: 'number',
  nombreconsultor: 'string',
  estado: 'string',
  nombreproyecto: 'string',
  jefedeproyecto: 'string',
  jefefuncional: 'string',
  aprobador: 'string',
  horas: 'number'
})

export type HorasPendientes = typeof horasPendientes.infer

export const horasPendientesMetadata: {
  label: string
  key: keyof HorasPendientes
}[] = [
  {
    label: 'ID Consultor',
    key: 'id'
  },
  {
    label: 'Nombre Consultor',
    key: 'nombreconsultor'
  },
  {
    label: 'Estado',
    key: 'estado'
  },
  {
    label: 'Proyecto',
    key: 'nombreproyecto'
  },
  {
    label: 'Je<PERSON> de Proyecto',
    key: 'jefede<PERSON>roy<PERSON>to'
  },
  {
    label: 'Jefe Funcional',
    key: 'jefefuncional'
  },
  {
    label: 'Aprobador',
    key: 'aprobador'
  },
  {
    label: 'Horas',
    key: 'horas'
  }
]
