import { type } from 'arktype'

export const porConsultor = type({
  id: 'number',
  fecha: 'string',
  nombre: 'string',
  horastrabajadas: 'number',
  nombretarea: 'string',
  descripcion: 'string',
  etapa: 'string',
  nombreproyecto: 'string',
  nombrecliente: 'string',
  consultor: 'number',
  estado: 'string'
})

export type PorConsultor = typeof porConsultor.infer

export const porConsultorMetadata: {
  label: string
  key: keyof PorConsultor
}[] = [
  {
    label: 'Fecha',
    key: 'fecha'
  },
  {
    label: 'Consultor',
    key: 'nombre'
  },
  {
    label: 'Proyecto',
    key: 'nombreproyecto'
  },
  {
    label: 'Horas',
    key: 'horastrabajadas'
  },
  {
    label: 'Tarea',
    key: 'nombretarea'
  },
  {
    label: 'Descripción',
    key: 'descripcion'
  },
  {
    label: 'Etapa',
    key: 'etapa'
  }
]
