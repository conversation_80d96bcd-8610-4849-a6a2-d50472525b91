import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { consultorId, fecha1, fecha2, tarea, jefeId } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    'Exec ConsultaHoras @0, @1, @2, @3, @4',
    [fecha1, fecha2, consultorId, tarea, jefeId]
  )

  return arrayKeys(formatDates(result))
})
