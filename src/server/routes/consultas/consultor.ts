import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { consultorId } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    'select distinct * from [F_ConsultoresJefeProyecto](@0) order by Nombre',
    [consultorId]
  )

  return arrayKeys(formatDates(result))
})
