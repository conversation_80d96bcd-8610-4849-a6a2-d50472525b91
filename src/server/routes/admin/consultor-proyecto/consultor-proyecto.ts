import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { id } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `
      select C.ID, C.Nombre, C.Apellido from Consultores C inner join ConsultorProyecto CP on C.ID = CP.ConsultorID
      where CP.ProyectoID = @0
    `,
    [id]
  )

  return arrayKeys(formatDates(result))
})
