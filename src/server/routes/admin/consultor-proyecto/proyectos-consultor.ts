import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { id } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `
      select P.* from  [dbo].[ConsultorProyecto] CP inner join [dbo].[Proyectos] P on CP.[ProyectoID]=P.IDproyecto
      where CP.ConsultorID = @0
    `,
    [id]
  )

  return arrayKeys(formatDates(result))
})
