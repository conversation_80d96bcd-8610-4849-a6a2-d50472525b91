import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { id } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `
      select V.*, C.<PERSON>+' ' +  C<PERSON>Apellido as NombreCompleto from [dbo].[Vacaciones] V
      left outer join Consultores C on C.ID=V.ConsultorID
      where Estado='A' and C.JefeFuncional = @0
    `,
    [id]
  )

  return arrayKeys(formatDates(result))
})
