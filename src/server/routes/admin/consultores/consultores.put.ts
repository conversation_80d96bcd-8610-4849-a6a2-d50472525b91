import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const {
    id,
    nombre,
    apellido,
    pais,
    jefefuncional,
    usuario,
    passwd,
    role,
    fechaingreso,
    esjefefunc,
    esjefeproy,
    email
  } = await readBody(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    `
      UPDATE Consultores SET
        Nombre = @1,
        Apellido = @2,
        Pais = @3,
        JefeFuncional = @4,
        USUARIO = @5,
        PASSWD = @6,
        [ROLE] = @7,
        FechaIngreso = @8,
        EsJefeFunc = @9,
        EsJefeProy = @10,
        Email = @11
      WHERE ID = @0
    `,
    [
      id,
      nombre,
      apellido,
      pais,
      jefefuncional,
      usuario,
      passwd,
      role,
      fechaingreso,
      esjefefunc,
      esjefeproy,
      email
    ]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
