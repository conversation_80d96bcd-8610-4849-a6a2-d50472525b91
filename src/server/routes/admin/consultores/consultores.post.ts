import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const {
    nombre,
    apellido,
    pais,
    jefefuncional,
    usuario,
    passwd,
    role,
    fechaingreso,
    esjefefunc,
    esje<PERSON><PERSON>roy,
    email
  } = await readBody(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    `
      INSERT INTO Consultores (
        Nombre, Apellido, Pais, JefeFuncional, USUARIO,
        PASSWD, [ROLE], FechaIngreso, EsJefeFunc, EsJefeProy,
        Email
      ) VALUES(@0, @1, @2, @3, @4, @5, @6, @7, @8, @9, @10)
    `,
    [
      nombre,
      apellido,
      pais,
      jefefuncional,
      usuario,
      passwd,
      role,
      fechaingreso,
      esjefefunc,
      esjefeproy,
      email
    ]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
