import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { id } = getQuery(event)

  let out = 0

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    'exec [P_InicializarConsultor] @0, @1',
    [id, out]
  )

  return arrayKeys(formatDates(result))
})
