import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async () => {
  const dataSource = await getDatasource()

  const result = await dataSource.manager.query(
    `
    SELECT c.*, cf.USUARIO as JefeFuncionalUser
    FROM Consultores c
    INNER JOIN Consultores cf ON c.JefeFuncional = cf.ID
    `
  )

  return arrayKeys(formatDates(result))
})
