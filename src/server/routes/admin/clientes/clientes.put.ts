import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { id, nombrecliente, pais } = await readBody(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    'UPDATE Clientes SET NombreCliente = @0, Pais = @1 WHERE IDcliente = @2',
    [nombrecliente, pais, id]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
