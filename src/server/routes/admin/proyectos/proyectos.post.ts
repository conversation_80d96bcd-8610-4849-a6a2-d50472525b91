import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const {
    clavecorta,
    nombreproyecto,
    pais,
    fechacreacion,
    jefedeproyecto,
    cliente,
    horasproy
  } = await readBody(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    `
      INSERT INTO Consultores (
        ClaveCorta,
        NombreProyecto,
        Pais,
        FechaCreacion,
        JefeDeProyecto,
        Cliente,
        HorasProy
      ) VALUES(@0, @1, @2, @3, @4, @5, @6)
    `,
    [
      clavecorta,
      nombreproyecto,
      pais,
      fechacreacion,
      jefedeproyecto,
      cliente,
      horasproy
    ]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
