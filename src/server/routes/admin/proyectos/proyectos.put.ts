import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const {
    id,
    clavecorta,
    nombreproyecto,
    pais,
    fechacreacion,
    jefedeproyecto,
    cliente,
    horasproy
  } = await readBody(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    `
      UPDATE Consultores SET
        ClaveCorta = @1,
        NombreProyecto = @2,
        Pais = @3,
        FechaCreacion = @4,
        JefeDeProyecto = @5,
        Cliente = @6,
        <PERSON><PERSON><PERSON><PERSON> = @7
      WHERE IDProyecto = @0
    `,
    [
      id,
      clavecorta,
      nombreproyecto,
      pais,
      fechacreacion,
      jefedeproyecto,
      cliente,
      horasproy
    ]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
