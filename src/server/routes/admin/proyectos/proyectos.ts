import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async () => {
  const dataSource = await getDatasource()

  const result = await dataSource.manager.query(
    `
      SELECT
        p.*,
        c.NombreCliente,
        co.Nombre as NombreJefe,
        co.Apellido as ApellidoJefe,
        co.USUARIO as UsuarioJefe,
        <PERSON><PERSON> as ClientePais
      FROM Proyectos p
      INNER JOIN Clientes c ON c.IDcliente = p.Cliente
      INNER JOIN Consultores co ON co.ID = p.JefeDe<PERSON>royecto
    `
  )

  return arrayKeys(formatDates(result))
})
