import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { idpais, nombrepaissgt, codpaiscorep, horasmax } =
    await readBody(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    `
      UPDATE SERGIO.dbo.HorasXPaises
      SET NOMBRE_PAIS_SGT=@1, COD_PAIS_COREP=@2, Horas_Max=@3
      WHERE IDpais=@0
    `,
    [idpais, nombrepaissgt, codpaiscorep, horasmax]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
