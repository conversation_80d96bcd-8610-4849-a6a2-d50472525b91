import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { proyectoCorta } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `
    select <PERSON><PERSON>, T.<PERSON>mbre<PERSON>, T.IDtarea from Tareas T
    left join Proyectos P on T.ClaveCortaProyecto = P.ClaveCorta
    where P.ClaveCorta = @0
    `,
    [proyectoCorta]
  )

  return arrayKeys(formatDates(result))
})
