import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { consultor, tarea, proyecto } = await readBody(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    `
      insert into [dbo].[ConsultorTarea] values (@0, @1, @2)
    `,
    [consultor, tarea, proyecto]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
