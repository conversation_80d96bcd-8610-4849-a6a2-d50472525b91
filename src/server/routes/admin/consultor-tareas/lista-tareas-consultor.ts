import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { consultorId, proyectoId } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    'Exec ListaTareasConsultorNEW @ConsultorID = @0, @ProyectoID = @1',
    [consultorId, proyectoId]
  )

  return arrayKeys(formatDates(result))
})
