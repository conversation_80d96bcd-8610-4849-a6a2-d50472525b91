import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { id, consultorId } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    'SELECT * FROM F_ProyectoConsultoryPM(@0, @1)',
    [id, consultorId]
  )

  return arrayKeys(formatDates(result))
})
