import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { proyectoId, tareaId } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    'Exec ListaConsultoresPT @ProyectoID = @0, @TareaID = @1',
    [proyectoId, tareaId]
  )

  return arrayKeys(formatDates(result))
})
