import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { consultor, tarea } = await readBody(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    `
      delete from  [dbo].[ConsultorTarea] where ConsultorID=@0 and TareaID=@1
    `,
    [consultor, tarea]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
