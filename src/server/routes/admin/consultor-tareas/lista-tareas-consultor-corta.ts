import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { proyectoCorta } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    'select * from [dbo].[Tareas] where ClaveCortaProyecto= @0',
    [proyectoCorta]
  )

  return arrayKeys(formatDates(result))
})
