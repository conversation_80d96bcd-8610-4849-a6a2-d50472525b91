import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { legislacion, nathab, diasper } = await readBody(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    `
      UPDATE LegislLab SET NatHab = @1, DiasPer = @2 WHERE Legislacion = @0
    `,
    [legislacion, nathab, diasper]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
