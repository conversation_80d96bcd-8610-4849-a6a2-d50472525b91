import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { legislacion, nathab, diasper } = await readBody(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    `
      INSERT INTO LegislLab (Legislacion, NatHab, DiasPer) VALUES (@0, @1, @2)
    `,
    [legislacion, nathab, diasper]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
