import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { consultorid, vacinic, legislacion, diaspermisoadic, vacnogastadas } =
    await readBody(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    `
      Update ConsultorConfVac set VacInic=@1, Legislacion=@2, DiasPermisoAdic=@3, VacNoGastadas=@4 where ConsultorID=@0
    `,
    [consultorid, vacinic, legislacion, diaspermisoadic, vacnogastadas]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
