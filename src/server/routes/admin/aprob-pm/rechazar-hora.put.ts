import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { id, comentario } = getQuery(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    'update <PERSON>rasTrabajadas set Comentario = @0 where ID = @1',
    [comentario, id]
  )

  await dataSource.manager.query(
    "update HorasTrabajadas set Estado ='R' where ID=(@0)",
    [id]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
