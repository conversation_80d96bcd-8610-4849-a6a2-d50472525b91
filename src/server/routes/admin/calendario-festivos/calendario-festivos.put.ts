import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { id, calendario, fechafestiva } = await readBody(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    `
      UPDATE DiasFestivos SET Calendario = @1, FechaFestiva = @2 WHERE ID = @0
    `,
    [id, calendario, fechafestiva]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
