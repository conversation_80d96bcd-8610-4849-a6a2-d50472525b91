import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { calendario, fechafestiva } = await readBody(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    `
      INSERT INTO DiasFestivos (Calendario, FechaFestiva) VALUES (@0, @1)
    `,
    [calendario, fechafestiva]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
