import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { estatus, horas, fechainicio, fechafin, clavecorta } =
    await readBody(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    'update Tareas set Estatus=@0, Horas=@1, FechaInicio=@2, FechaFin=@3 where ClaveCorta=@4',
    [estatus, horas, fechainicio, fechafin, clavecorta]
  )

  return arrayKeys(formatDates(result))
})
