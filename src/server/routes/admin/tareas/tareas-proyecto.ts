import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { proyecto } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    'SELECT * FROM [dbo].[Tareas] WHERE clavecortaproyecto = @0',
    [proyecto]
  )

  return arrayKeys(formatDates(result))
})
