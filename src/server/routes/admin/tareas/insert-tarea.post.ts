import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { estatus, horas, fechainicio, fechafin, clavecorta } =
    await readBody(event)

  const dataSource = await getDatasource()
  await dataSource.manager.query(
    'update Tareas set Estatus=@0, Horas=@1, FechaInicio=@2, FechaFin=@3 where ClaveCorta=@4',
    [estatus, horas, fechainicio, fechafin, clavecorta]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
