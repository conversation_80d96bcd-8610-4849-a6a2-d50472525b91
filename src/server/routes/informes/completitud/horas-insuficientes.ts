import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'
import { horasInsuficientesMetadata } from '~~/src/server/models/horas-insuficientes'
import { generateExcel } from '~~/src/server/reports/excel'

export default defineEventHandler(async event => {
  const { fechainicio, fechafin, limite } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    'select * from [dbo].[F_JasperAlertaHorasRegistradas](@0, @1, @2) order by JefeFuncional',
    [fechainicio, fechafin, limite]
  )

  event.node.res.setHeader(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  event.node.res.setHeader(
    'Content-Disposition',
    `attachment; filename="horas-insuficientes.xlsx`
  )

  return sendStream(
    event,
    generateExcel(
      arrayKeys(formatDates(result)),
      horasInsuficientesMetadata,
      'Informe de Horas Insuficientes'
    )
  )
})
