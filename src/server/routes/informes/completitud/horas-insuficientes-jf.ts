import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'
import { horasInsuficientesMetadata } from '~~/src/server/models/horas-insuficientes'
import { generateExcel } from '~~/src/server/reports/excel'

export default defineEventHandler(async event => {
  const { fechainicio, fechafin, jf } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    'select * from [dbo].F_JasperAlertaHorasRegistradas_JefeFuncional(@2, @0, @1) order by Horas desc',
    [fechainicio, fechafin, jf]
  )

  event.node.res.setHeader(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  event.node.res.setHeader(
    'Content-Disposition',
    `attachment; filename="horas-insuficientes-jf.xlsx`
  )

  return sendStream(
    event,
    generateExcel(
      arrayKeys(formatDates(result)),
      horasInsuficientesMetadata,
      'Informe de Horas Insuficientes por Jefe Funcional'
    )
  )
})
