import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'
import { horasProyectoMetadata } from '~~/src/server/models/horas-proyecto'
import { generateExcel } from '~~/src/server/reports/excel'

export default defineEventHandler(async event => {
  const { fechainicio, fechafin, consultorid } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    'select * from dbo.[F_Horas_Consultor_Proyecto] (@0, @1, @2)',
    [fechainicio, fechafin, consultorid]
  )

  event.node.res.setHeader(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  event.node.res.setHeader(
    'Content-Disposition',
    `attachment; filename="horas-proyecto-completitud.xlsx`
  )

  return sendStream(
    event,
    generateExcel(
      arrayKeys(formatDates(result)),
      horasProyectoMetadata,
      'Informe de Horas por Proyecto'
    )
  )
})
