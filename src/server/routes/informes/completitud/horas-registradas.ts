import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'
import {
  HorasRegistradas,
  horasRegistradasMetadata
} from '~~/src/server/models/horas-registradas'
import { generateExcel } from '~~/src/server/reports/excel'
import { transformHorasRegistradas } from '~~/src/server/utils/total'

export default defineEventHandler(async event => {
  const { fechainicio, fechafin } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query<HorasRegistradas[]>(
    'SELECT * FROM [dbo].[F_JasperHorasRegistradas](@0, @1) order by ID',
    [fechainicio, fechafin]
  )

  event.node.res.setHeader(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  event.node.res.setHeader(
    'Content-Disposition',
    `attachment; filename="horas-registradas.xlsx`
  )

  return sendStream(
    event,
    generateExcel(
      transformHorasRegistradas(arrayKeys(formatDates(result))),
      horasRegistradasMetadata,
      'Informe de Horas Registradas'
    )
  )
})
