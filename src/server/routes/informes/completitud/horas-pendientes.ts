import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'
import {
  HorasPendientes,
  horasPendientesMetadata
} from '~~/src/server/models/horas-pendientes'
import { generateExcel } from '~~/src/server/reports/excel'
import { transformHorasPendientes } from '~~/src/server/utils/total'

export default defineEventHandler(async event => {
  const { fechainicio, fechafin } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query<HorasPendientes[]>(
    'SELECT * FROM [dbo].[F_JasperHorasPendientesAprobar](@0, @1) order by ID, Estado',
    [fechainicio, fechafin]
  )

  event.node.res.setHeader(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  event.node.res.setHeader(
    'Content-Disposition',
    `attachment; filename="horas-pendientes.xlsx`
  )

  return sendStream(
    event,
    generateExcel(
      transformHorasPendientes(arrayKeys(formatDates(result))),
      horasPendientesMetadata,
      'Informe de Horas Pendiente de Aprobar'
    )
  )
})
