import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'
import { proyectoInternoMetadata } from '~~/src/server/models/proyecto-interno'
import { generateExcel } from '~~/src/server/reports/excel'

export default defineEventHandler(async event => {
  const { fechainicio, fechafin, consultorid, isExcel } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `select * from [F_Control_HorasConsultor](@0, @1, @2) order by <PERSON>cha asc, NombreProyecto`,
    [fechainicio, fechafin, consultorid]
  )

  if (isExcel === 'true') {
    event.node.res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    event.node.res.setHeader(
      'Content-Disposition',
      `attachment; filename="horas-consultor.xlsx`
    )

    return sendStream(
      event,
      generateExcel(
        arrayKeys(formatDates(result)),
        proyectoInternoMetadata,
        'Informe de Horas de Consultor entre dos fechas',
        `Entre ${fechainicio} Y ${fechafin}`
      )
    )
  }

  return arrayKeys(formatDates(result))
})
