import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'
import { proyectoInternoMetadata } from '~~/src/server/models/proyecto-interno'
import { generateExcel } from '~~/src/server/reports/excel'

export default defineEventHandler(async event => {
  const { fechainicio, fechafin, isExcel } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `select * from [F_Control_HorasProyecInterno](@0, @1) order by HTotal desc, ID`,
    [fechainicio, fechafin]
  )

  if (isExcel === 'true') {
    event.node.res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    event.node.res.setHeader(
      'Content-Disposition',
      `attachment; filename="horas-proyecto-interno.xlsx`
    )

    return sendStream(
      event,
      generateExcel(
        arrayKeys(formatDates(result)),
        proyectoInternoMetadata,
        'Informe de Horas Dedicadas a Proyecto Interno',
        `Entre ${fechainicio} Y ${fechafin}`
      )
    )
  }

  return arrayKeys(formatDates(result))
})
