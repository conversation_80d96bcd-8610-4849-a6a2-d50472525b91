import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'
import { porProyectoMetadata } from '~~/src/server/models/por-proyecto'
import { generateExcel } from '~~/src/server/reports/excel'
import { standardTemplate } from '~~/src/server/reports/templates/standard-template'

export default defineEventHandler(async event => {
  const { nombreproyecto, fechainicio, fechafin, isExcel } = getQuery(event)
  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `
      select
        HT.ID
        , HT.Fecha
        , CONCAT(C.Nombre, ' ',C.A<PERSON>lido) as Consultor
        , HT.<PERSON>
        , T.NombreTarea
        , T.<PERSON>
        , HT.Descripcion
        , HT.Etapa
        , P.<PERSON>mbre<PERSON>roy<PERSON>
        , P<PERSON>
        , CL.NombreCliente
        , HT.Estado
        from  [dbo].[<PERSON>ras<PERSON><PERSON>ajadas] HT
        left outer join [dbo].[Proyectos] P on HT.[Proyecto]=P.ClaveCorta
        left outer join [dbo].[Tareas] T on HT.[Tarea]=T.ClaveCorta
        left outer join Consultores C on HT.Consultor = C.ID
        left outer join Clientes CL on CL.IDcliente = P.Cliente
        where HT.Estado = 'A'
        and P.NombreProyecto=@0
        and HT.Fecha >= @1
        and HT.Fecha <= @2
        order by  T.NombreTarea, HT.Fecha asc`,
    [nombreproyecto, fechainicio, fechafin]
  )

  event.node.res.setHeader(
    'Content-Type',
    isExcel === 'true'
      ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      : 'application/pdf'
  )
  event.node.res.setHeader(
    'Content-Disposition',
    `attachment; filename="horas-por-proyecto${isExcel === 'true' ? '.xlsx' : '.pdf'}"`
  )

  return sendStream(
    event,
    isExcel === 'true'
      ? generateExcel(
          arrayKeys(formatDates(result)),
          porProyectoMetadata,
          'Informe de Horas de Proyecto',
          `Entre ${fechainicio} Y ${fechafin}`
        )
      : standardTemplate({
          fileName: 'horas-por-proyecto',
          title: 'Informe de Horas de Proyecto',
          unsortData: arrayKeys(formatDates(result)),
          metadata: porProyectoMetadata,
          subtitle: `Entre ${fechainicio} Y ${fechafin}`,
          size: 'A3'
        })
  )
})
