import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'
import { porClienteMetadata } from '~~/src/server/models/por-cliente'
import { generateExcel } from '~~/src/server/reports/excel'
import { standardTemplate } from '~~/src/server/reports/templates/standard-template'

export default defineEventHandler(async event => {
  const { nombrecliente, fechainicio, fechafin, isExcel } = getQuery(event)
  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `
      select
      HT.ID
      , HT.Fecha
      , CONCAT(C.Nombre, ' ',C.A<PERSON>lido) as Consultor
      , HT.<PERSON>_<PERSON>das
      , T.NombreTarea
      , HT.Descripcion
      , HT.Etapa
      , P.NombreProyecto
      , CL.NombreCliente
      , HT.Estado
      from  [dbo].[HorasTrabajadas] HT
      left outer join [dbo].[Proyectos] P on HT.[Proyecto]=P.<PERSON>lav<PERSON>
      left outer join [dbo].[Tareas] T on HT.[Tarea]=T.<PERSON>lave<PERSON>orta
      left outer join Consultores C on HT.Consultor = C.ID
      left outer join Clientes CL on CL.IDcliente = P.Cliente
      where HT.Estado = 'A'
      and CL.NombreCliente= @0
      and HT.Fecha >= @1
      and HT.Fecha <= @2
      order by P.NombreProyecto, HT.Fecha asc`,
    [nombrecliente, fechainicio, fechafin]
  )

  event.node.res.setHeader(
    'Content-Type',
    isExcel === 'true'
      ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      : 'application/pdf'
  )
  event.node.res.setHeader(
    'Content-Disposition',
    `attachment; filename="horas-por-cliente${isExcel === 'true' ? '.xlsx' : '.pdf'}"`
  )

  return sendStream(
    event,
    isExcel === 'true'
      ? generateExcel(
          arrayKeys(formatDates(result)),
          porClienteMetadata,
          'Informe de Horas de Cliente',
          `Entre ${fechainicio} Y ${fechafin}`
        )
      : standardTemplate({
          fileName: 'horas-por-cliente',
          title: 'Informe de Horas de Cliente',
          unsortData: arrayKeys(formatDates(result)),
          metadata: porClienteMetadata,
          subtitle: `Entre ${fechainicio} Y ${fechafin}`,
          size: 'A3'
        })
  )
})
