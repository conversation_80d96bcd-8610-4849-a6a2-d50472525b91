import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { nombrecliente } = getQuery(event)
  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `
    select P.* from Proyectos P
      left join Clientes C on P.Cliente = C.IDcliente
      where NombreCliente = @0
    `,
    [nombrecliente]
  )

  return arrayKeys(formatDates(result))
})
