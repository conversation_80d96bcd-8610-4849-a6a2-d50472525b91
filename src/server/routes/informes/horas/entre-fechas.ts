import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'
import { entreFechasMetadata } from '~~/src/server/models/entre-fechas'
import { generateExcel } from '~~/src/server/reports/excel'
import { standardTemplate } from '~~/src/server/reports/templates/standard-template'

export default defineEventHandler(async event => {
  const { fechainicio, fechafin, isExcel } = getQuery(event)
  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `
      select
        HT.ID
        , HT.Fecha
        ,CONCAT(C.Nombre, ' ',C.Apellido) as Nombre
        , HT.<PERSON>_<PERSON>
        , T.NombreTarea
        , HT.Descripcion
        , HT.Etapa
        , P.NombreProyecto
        ,CL.NombreCliente
        ,HT.Consultor
        , HT.Estado,
        C.Pais
        from  [dbo].[HorasTrabajadas] HT
        left outer join [dbo].[Proyectos] P on HT.[Proyecto]=P.<PERSON>
        left outer join [dbo].[Tareas] T on HT.[Tarea]=T.ClaveCorta
        left outer join Consultores C on HT.Consultor = C.ID
        left outer join Clientes CL on CL.IDcliente = P.Cliente
        where HT.Estado = 'A'
        and HT.Fecha >= @0
        and HT.Fecha <= @1
        order by  HT.Fecha, HT.Consultor, P.NombreProyecto asc`,
    [fechainicio, fechafin]
  )

  event.node.res.setHeader(
    'Content-Type',
    isExcel === 'true'
      ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      : 'application/pdf'
  )
  event.node.res.setHeader(
    'Content-Disposition',
    `attachment; filename="horas-por-consultor${isExcel === 'true' ? '.xlsx' : '.pdf'}"`
  )

  return sendStream(
    event,
    isExcel === 'true'
      ? generateExcel(
          arrayKeys(formatDates(result)),
          entreFechasMetadata,
          'Informe de Horas Entre Dos Fechas',
          `Entre ${fechainicio} Y ${fechafin}`
        )
      : standardTemplate({
          fileName: 'horas-por-consultor',
          title: 'Informe de Horas Entre Dos Fechas',
          unsortData: arrayKeys(formatDates(result)),
          metadata: entreFechasMetadata,
          size: 'A3',
          subtitle: `Entre ${fechainicio} Y ${fechafin}`
        })
  )
})
