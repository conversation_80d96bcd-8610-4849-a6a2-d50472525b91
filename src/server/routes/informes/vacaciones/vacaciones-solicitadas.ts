import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'
import { vacacionesSolicitadasMetadata } from '~~/src/server/models/vacaciones-solicitadas'
import { generateExcel } from '~~/src/server/reports/excel'

export default defineEventHandler(async event => {
  const { fechainicio, fechafin } = getQuery(event)
  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    'select * from [dbo].[F_VacacionesSolicitadas](@0 ,@1) order by Pais, ID',
    [fechainicio, fechafin]
  )

  event.node.res.setHeader(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  event.node.res.setHeader(
    'Content-Disposition',
    `attachment; filename="vacaciones-solicitadas.xlsx`
  )

  return sendStream(
    event,
    generateExcel(
      arrayKeys(formatDates(result)),
      vacacionesSolicitadasMetadata,
      'Informe de Vacaciones Solicitadas',
      `Entre ${fechainicio} Y ${fechafin}`
    )
  )
})
