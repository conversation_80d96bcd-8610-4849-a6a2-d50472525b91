import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'
import { vacacionesConsumidasMetadata } from '~~/src/server/models/vacaciones-consumidas'
import { generateExcel } from '~~/src/server/reports/excel'

export default defineEventHandler(async event => {
  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    'select * from F_VacacionesConsumidas()'
  )

  event.node.res.setHeader(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  event.node.res.setHeader(
    'Content-Disposition',
    `attachment; filename="vacaciones-consumidas.xlsx`
  )

  return sendStream(
    event,
    generateExcel(
      arrayKeys(formatDates(result)),
      vacacionesConsumidasMetadata,
      'Informe de Vacaciones Consumidas'
    )
  )
})
