import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'
import { consultoresProyectoMetadata } from '~~/src/server/models/consultores-proyecto'
import { generateExcel } from '~~/src/server/reports/excel'
import { standardTemplate } from '~~/src/server/reports/templates/standard-template'

export default defineEventHandler(async event => {
  const { isExcel } = getQuery(event)
  const dataSource = await getDatasource()
  const result = await dataSource.manager
    .query<Record<string, unknown>[]>(
      `
      select C.*, CP.*, P.*, CL.* from Consultores C
      left outer join ConsultorProyecto CP on CP.ConsultorID = C.ID
      left outer join Proyectos P on P.IDproyecto = CP.ProyectoID
      left outer join Clientes CL on CL.IDcliente = P.Cliente
      where P.NombreProyecto is not null
      order by <PERSON><PERSON>roy<PERSON>
    `
    )
    .then(res =>
      res.map(row => ({
        ...row,
        pais: ((row?.['Pais'] as string[]) ?? []).join(',')
      }))
    )

  event.node.res.setHeader(
    'Content-Type',
    isExcel === 'true'
      ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      : 'application/pdf'
  )
  event.node.res.setHeader(
    'Content-Disposition',
    `attachment; filename="listado-consultores${isExcel === 'true' ? '.xlsx' : '.pdf'}"`
  )

  return sendStream(
    event,
    isExcel === 'true'
      ? generateExcel(
          arrayKeys(formatDates(result)),
          consultoresProyectoMetadata,
          'Consultores Por Proyecto'
        )
      : standardTemplate({
          fileName: 'listado-consultores',
          title: 'Consultores Por Proyecto',
          unsortData: arrayKeys(formatDates(result)),
          metadata: consultoresProyectoMetadata
        })
  )
})
