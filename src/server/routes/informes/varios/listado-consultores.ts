import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'
import { listadoConsultorMetadata } from '~~/src/server/models/listado-consultor'
import { generateExcel } from '~~/src/server/reports/excel'
import { standardTemplate } from '~~/src/server/reports/templates/standard-template'

export default defineEventHandler(async event => {
  const { isExcel } = getQuery(event)
  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `
      select C1.*, C2.Nombre +' '+ C2.A<PERSON>lido as Je<PERSON>Func from Consultores C1
      left outer join Consultores C2 on C1.JefeFuncional=C2.ID
      where C1.ROLE<>'cese'
      order by ID`
  )

  event.node.res.setHeader(
    'Content-Type',
    isExcel === 'true'
      ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      : 'application/pdf'
  )
  event.node.res.setHeader(
    'Content-Disposition',
    `attachment; filename="listado-consultores${isExcel === 'true' ? '.xlsx' : '.pdf'}"`
  )

  return sendStream(
    event,
    isExcel === 'true'
      ? generateExcel(
          arrayKeys(formatDates(result)),
          listadoConsultorMetadata,
          'Equipo TCM'
        )
      : standardTemplate({
          fileName: 'listado-consultores',
          title: 'Equipo TCM',
          unsortData: arrayKeys(formatDates(result)),
          metadata: listadoConsultorMetadata,
          size: 'A2'
        })
  )
})
