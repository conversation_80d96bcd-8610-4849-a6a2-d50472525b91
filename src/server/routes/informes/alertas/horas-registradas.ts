import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'
import { horasRegistradasAlertMetadata } from '~~/src/server/models/horas-registradas-alert'
import { generateExcel } from '~~/src/server/reports/excel'

export default defineEventHandler(async event => {
  const { fechainicio, fechafin, horasmin, isExcel } = getQuery(event)
  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    'select * from [dbo].[F_AlertasHorasRegistradas](@0, @1, @2) order by Horas asc',
    [fechainicio, fechafin, horasmin]
  )

  if (isExcel === 'true') {
    event.node.res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    event.node.res.setHeader(
      'Content-Disposition',
      `attachment; filename="horas-registradas-alert.xlsx`
    )
    return sendStream(
      event,
      generateExcel(
        arrayKeys(formatDates(result)),
        horasRegistradasAlertMetadata,
        'Informe de Horas Registradas'
      )
    )
  }

  return arrayKeys(formatDates(result))
})
