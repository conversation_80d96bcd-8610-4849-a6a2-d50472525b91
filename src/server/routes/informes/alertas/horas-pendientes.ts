import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { fechainicio, fechafin, horasmin } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `select * from [dbo].F_AlertasHorasPendientesAprobar(@0, @1, @2) order by <PERSON><PERSON> desc`,
    [fechainicio, fechafin, horasmin]
  )

  return arrayKeys(formatDates(result))
})
