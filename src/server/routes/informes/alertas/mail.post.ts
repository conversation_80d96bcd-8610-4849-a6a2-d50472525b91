import nodemailer from 'nodemailer'

export default defineEventHandler(async event => {
  const { mails, subject, text } = await readBody(event)

  const transporter = nodemailer.createTransport({
    host: 'smtp.office365.com',
    port: 587,
    secure: false,
    auth: {
      user: '<EMAIL>',
      pass: '%Img5380%'
    }
  })

  try {
    await transporter.sendMail({
      from: '<EMAIL>',
      to: mails as string[],
      subject: subject as string,
      text: text as string
    })

    setResponseStatus(event, 200)

    return { 'status': 'ok' }
  } catch (err) {
    console.error('Error enviando correo:', err)

    setResponseStatus(event, 400)
    return { 'status': 'error' }
  }
})
