import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async () => {
  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `SELECT
      a.ID,
      b.<PERSON>mbre,
      b.<PERSON>,
      b.Role
    FROM ConsultoresExcluidos a
    INNER JOIN Consultores b ON a.ConsultorID = b.ID`
  )

  return arrayKeys(formatDates(result))
})
