import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const {
    fecha1,
    fecha2,
    fecha3,
    fecha4,
    fecha5,
    consultor,
    horasTrabajadas1,
    horasTrabajadas2,
    horasTrabajadas3,
    horasTrabajadas4,
    horasTrabajadas5,
    descripcion1,
    descripcion2,
    descripcion3,
    descripcion4,
    descripcion5,
    proyecto,
    tarea,
    etapa
  } = await readBody(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `
      DECLARE @out INT;
      EXEC [dbo].[InsertaHorasXSemana] @0, @1, @2, @3, @4, @5, @6, @7, @8, @9, @10, @11, @12, @13, @14, @15, @16, @17, @18, @19, @out OUTPUT;
      SELECT @out as res;
    `,
    [
      fecha1,
      fecha2,
      fecha3,
      fecha4,
      fecha5,
      consultor.toString(),
      Number(horasTrabajadas1),
      Number(horasTrabajadas2),
      Number(horasTrabajadas3),
      Number(horasTrabajadas4),
      Number(horasTrabajadas5),
      descripcion1,
      descripcion2,
      descripcion3,
      descripcion4,
      descripcion5,
      proyecto,
      tarea,
      'P',
      etapa
    ]
  )

  if (result[0].res !== 1) {
    setResponseStatus(event, 500)
    return { 'status': 'error' }
  }

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
