import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { consultorId, fechaInicio, fechaFin } = await readBody(event)

  const dataSource = await getDatasource()
  await dataSource.manager.query(
    'UPDATE Vacaciones SET DiaInicio = @0, DiaFin = @1 WHERE ConsultorID = @2 AND Estado = @3',
    [fechaInicio, fechaFin, consultorId, 'P']
  )

  setResponseStatus(event, 200)

  return { 'status': 'ok' }
})
