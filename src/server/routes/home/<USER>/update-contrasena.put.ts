//Update Consultores set PASSWD=:passwd where ID=:id
import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { passwd, id } = getQuery(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    'Update Consultores set PASSWD=@0 where ID=@1',
    [passwd, id]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
