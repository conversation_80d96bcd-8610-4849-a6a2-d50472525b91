import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { id, fechaInicio, fechaFin, proyecto, etapa, tarea } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `SELECT * FROM HorasTrabajadas
      WHERE Consultor=@0
      AND Fecha >= @1 AND Fecha <= @2
      AND Proyecto = @3
      AND Etapa = @4
      AND Tarea = @5
      `,
    [id, fechaInicio, fechaFin, proyecto, etapa, tarea]
  )

  return arrayKeys(formatDates(result))
})
