import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { consultorId, fechaInicio, fechaFin } = await readBody(event)

  const dataSource = await getDatasource()
  await dataSource.manager.query(
    'INSERT INTO Vacaciones (ConsultorID, DiaInicio, DiaFin, Estado) VALUES (@0, @1, @2, @3)',
    [consultorId, fechaInicio, fechaFin, 'P']
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
