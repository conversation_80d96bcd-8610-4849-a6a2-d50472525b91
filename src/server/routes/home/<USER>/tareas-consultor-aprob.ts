import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { consultorId, projClaveCorta } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `
    select <PERSON><PERSON>, <PERSON><PERSON> from ConsultorTarea CT
    left outer join Tareas T on T.IDtarea = CT.TareaID
    left outer join Proyectos P on P.IDproyecto=CT.ProyectoID
    where CT.ConsultorID = @0 and P.ClaveCorta = @1
    and Estatus = 'A' and (T.FechaFin >= getdate() or T.FechaFin  is null)
    `,
    [consultorId, projClaveCorta]
  )

  return arrayKeys(formatDates(result))
})
