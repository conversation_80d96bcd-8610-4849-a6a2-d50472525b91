import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { id1, id2, id3, id4, id5 } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `
      DECLARE @out INT;
      EXEC [dbo].[BorraHorasXSemana] @0, @1, @2, @3, @4, @out OUTPUT;
      SELECT @out as res;
    `,
    [id1, id2, id3, id4, id5]
  )

  if (result[0].res !== 1) {
    setResponseStatus(event, 500)
    return { 'status': 'error' }
  }

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
