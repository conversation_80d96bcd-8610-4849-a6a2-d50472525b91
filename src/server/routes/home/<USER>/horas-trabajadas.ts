import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { fecha, id } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `EXEC [dbo].[HorasXSemanaEstado] @0, @1`,
    [fecha, id]
  )

  return arrayKeys(formatDates(result.slice(1)))
})
