import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const {
    id,
    fecha,
    consultor,
    horasTrabajadas,
    descripcion,
    proyecto,
    tarea,
    etapa
  } = await readBody(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `
      DECLARE @out INT;
      EXEC [dbo].[EditaHoras] @0, @1, @2, @3, @4, @5, @6, @7, @8, @out OUTPUT;
      SELECT @out as res;
    `,
    [
      id,
      fecha,
      consultor.toString(),
      Number(horasTrabajadas),
      descripcion,
      proyecto,
      tarea,
      'P',
      etapa
    ]
  )

  if (result[0].res !== 1) {
    setResponseStatus(event, 500)
    return { 'status': 'error' }
  }

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
