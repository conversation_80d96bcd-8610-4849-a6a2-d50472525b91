import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { id } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    `
    select P.<PERSON>, P.NombreProyecto from Proyectos P
    left outer join ConsultorProyecto C on C.ProyectoID = P.IDproyecto
    where C.ConsultorID = @0
    `,
    [id]
  )

  return arrayKeys(formatDates(result))
})
