import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'
import { horasRegistradasConsulMetadata } from '~~/src/server/models/horas-registradas-consul'
import { generateExcel } from '~~/src/server/reports/excel'

export default defineEventHandler(async event => {
  const { id, fechainicio, fechafin } = getQuery(event)
  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    'select * from dbo.[F_JasperHorasRegistConsul] (@0, @1, @2) order by Fe<PERSON>',
    [id, fechainicio, fechafin]
  )

  event.node.res.setHeader(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  event.node.res.setHeader(
    'Content-Disposition',
    `attachment; filename="horas-registradas.xlsx"`
  )

  return sendStream(
    event,
    generateExcel(
      arrayKeys(formatDates(result)),
      horasRegistradasConsulMetadata,
      'Informe de Horas Registradas',
      `Entre ${fechainicio} Y ${fechafin}`
    )
  )
})
