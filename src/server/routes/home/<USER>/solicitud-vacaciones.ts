import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { consultorId } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    "SELECT * FROM Vacaciones WHERE ConsultorID = @0 AND Estado='P'",
    [consultorId]
  )

  return arrayKeys(formatDates(result))
})
