import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { consultorId } = getQuery(event)

  const dataSource = await getDatasource()
  const result = await dataSource.manager.query(
    "select * from Vacaciones where Estado='R' and ConsultorID=@0",
    [consultorId]
  )

  return arrayKeys(formatDates(result))
})
