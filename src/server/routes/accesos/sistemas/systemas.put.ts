import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const {
    sistemanombre,
    idcliente,
    idproveedor,
    idrespseguridad,
    ambiente,
    componente,
    comunicaciones,
    caractpasswd,
    duracpasswd,
    mfa,
    bup,
    logs,
    monitoreo,
    sla,
    cifradocomuc
  } = await readBody(event)

  const { idSistema } = await getQuery(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    `
      UPDATE Sistemas
      SET SistemaNombre = @0, IDCliente = @1, IDProveedor = @2, IDRespSeguridad = @3, Ambiente = @4, Componente = @5, Comunicaciones = @6, CaractPasswd = @7, DuracPasswd = @8, MFA = @9, BUP = @10, Logs = @11, Monitoreo = @12, SLA = @13, CifradoComuc = @14
      WHERE ID = @15;
    `,
    [
      sistemanombre,
      idcliente,
      idproveedor,
      idrespseguridad,
      ambiente,
      componente,
      comunicaciones,
      caractpasswd,
      duracpasswd,
      mfa,
      bup,
      logs,
      monitoreo,
      sla,
      cifradocomuc,
      idSistema
    ]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
