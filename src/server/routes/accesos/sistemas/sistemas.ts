import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async () => {
  const dataSource = await getDatasource()

  const result = await dataSource.manager.query(
    `
    SELECT s.*, c.NombreCliente, p.NombreProveedor
    FROM Sistemas s
    INNER JOIN Clientes c ON c.IDcliente = s.IDCliente
    INNER JOIN Proveedores p ON p.IDProv = p.IDProv
    `
  )

  return arrayKeys(formatDates(result))
})
