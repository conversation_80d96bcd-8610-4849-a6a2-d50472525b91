import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const {
    sistemanombre,
    idcliente,
    idproveedor,
    idrespseguridad,
    ambiente,
    componente,
    comunicaciones,
    caractpasswd,
    duracpasswd,
    mfa,
    bup,
    logs,
    monitoreo,
    sla,
    cifradocomuc
  } = await readBody(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    `
      INSERT INTO Sistemas
      (SistemaNombre, IDCliente, IDProveedor, IDRespSeguridad, Ambiente, Componente, Comunicaciones, CaractPasswd, DuracPasswd, MFA, BUP, Logs, Monitoreo, SLA, CifradoComuc)
      VALUES(@0, @1, @2, @3, @4, @5, @6, @7, @8, @9, @10, @11, @12, @13, @14);
    `,
    [
      sistemanombre,
      idcliente,
      idproveedor,
      idrespseguridad,
      ambiente,
      componente,
      comunicaciones,
      caractpasswd,
      duracpasswd,
      mfa,
      bup,
      logs,
      monitoreo,
      sla,
      cifradocomuc
    ]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
