import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { idsistema, idproyecto, id } = await readBody(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    `
    UPDATE SERGIO.dbo.SistemaProyec SET IDSIstema=@0, IDProyecto=@1 WHERE IDRelacion=@2
    `,
    [idsistema, idproyecto, id]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
