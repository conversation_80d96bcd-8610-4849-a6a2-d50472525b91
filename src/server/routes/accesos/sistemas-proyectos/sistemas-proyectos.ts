import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async () => {
  const dataSource = await getDatasource()

  const result = await dataSource.manager.query(
    `SELECT
      sp.*,
      s.<PERSON>,
      s.<PERSON>mponente,
      s.Am<PERSON>nte,
      p.ClaveCorta,
      c.<PERSON>mbre<PERSON>liente,
      p.NombreProyecto
    FROM SistemaProyec sp
    INNER JOIN Sistemas s ON sp.IDSIstema = s.IDSistema
    INNER JOIN Proyectos p ON p.IDproyecto = sp.IDProyecto
    INNER JOIN Clientes c ON p.Cliente = c.ID<PERSON>liente
    `
  )

  return arrayKeys(formatDates(result))
})
