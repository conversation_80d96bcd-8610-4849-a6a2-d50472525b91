import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { idsistema, idproyecto } = await readBody(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    `
    INSERT INTO SERGIO.dbo.SistemaProyec (IDSIstema, IDProyecto) VALUES(@0, @1)
    `,
    [idsistema, idproyecto]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
