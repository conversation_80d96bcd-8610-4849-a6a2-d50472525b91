import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { idProyecto } = getQuery(event)

  const dataSource = await getDatasource()

  const result = await dataSource.manager.query(
    `
      select S.IDSistema, S.SistemaNombre, S.Ambiente, S.Componente, S.IDCliente, C.NombreCliente, S.IDProveedor,P.NombreProveedor
      from Sistemas S
          left outer join SistemaProyec SP on  S.IDSistema=SP.IDSIstema
          left outer join Clientes C  on  S.IDCliente=C.ID<PERSON>liente
          left outer join Proveedores P on S.IDProveedor=P.IDProv
      where SP.IDProyecto=@0
    `,
    [idProyecto]
  )

  return arrayKeys(formatDates(result))
})
