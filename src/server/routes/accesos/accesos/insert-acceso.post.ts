import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { idConsultor, idSistema, idPermiso } = await readBody(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    'INSERT INTO AccesoConsultorSistema SELECT @0, @1, @2',
    [idConsultor, idSistema, idPermiso]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
