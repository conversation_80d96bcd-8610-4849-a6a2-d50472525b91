import { getDatasource } from '#typeorm'
import { arrayKeys, formatDates } from '~~/src/server/utils/keys'

export default defineEventHandler(async event => {
  const { idSistema } = getQuery(event)

  const dataSource = await getDatasource()

  const result = await dataSource.manager.query(
    `
      SELECT ACS.[IDAcceso]
        ,ACS.[IDConsultor], C.Nombre+' '+C.Apellido as NombreCompleto
        ,ACS.[IDSistema], S.<PERSON>Nombre
        ,ACS.[IDPermiso], P.EtiqPermiso
      FROM [SERGIO].[dbo].[AccesoConsultorSistema] ACS

      left outer join Consultores C on C.ID=ACS.IDConsultor
      left outer join Sistemas S on S.IDSistema=ACS.[IDSistema]
      left outer join Permisos P on P.IDPermiso=ACS.IDPermiso

      where ACS.IDSistema=@0
    `,
    [idSistema]
  )

  return arrayKeys(formatDates(result))
})
