import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { nombreproveedor, pais, idprov } = await readBody(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    `
      UPDATE Proveedores SET NombreProveedor = @0, Pais = @1 WHERE IDProv = @2
    `,
    [nombreproveedor, pais, idprov]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
