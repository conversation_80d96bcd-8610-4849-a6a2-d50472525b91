import { getDatasource } from '#typeorm'

export default defineEventHandler(async event => {
  const { nombreproveedor, pais } = await readBody(event)

  const dataSource = await getDatasource()

  await dataSource.manager.query(
    `
      INSERT INTO Proveedores (NombreProveedor, Pais) VALUES(@0, @1)
    `,
    [nombreproveedor, pais]
  )

  setResponseStatus(event, 202)

  return { 'status': 'ok' }
})
