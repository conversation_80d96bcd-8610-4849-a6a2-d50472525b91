import jwt from 'jsonwebtoken'
import { getDatasource } from '#typeorm'

const JWT_SECRET = 'SSASD@#$@#%$%&$%@!GGOIEORTBNGA'

export default defineEventHandler(async event => {
  const { username, password } = await readBody(event)

  const dataSource = await getDatasource()
  const res = await dataSource.manager.query(
    'select * from Consultores where USUARIO = @0 AND PASSWD = @1',
    [username, password]
  )

  if (!res) {
    return {
      statusCode: 401,
      body: {
        message: 'Usuario o contraseña inválida',
        token: null,
        user: null
      }
    }
  }

  const formatedRes = arrayKeys(formatDates(res)) as unknown as Record<
    string,
    unknown
  >[]

  if (!formatedRes?.[0] || formatedRes?.[0]?.passwd !== password) {
    return {
      statusCode: 401,
      body: {
        message: 'Usuario o contraseña inválida',
        token: null,
        user: {}
      }
    }
  }

  const token = jwt.sign(
    { id: formatedRes?.[0]?.id, username: formatedRes?.[0]?.username },
    JWT_SECRET,
    { expiresIn: '2h' }
  )

  //await setUserSession(event, { user: formatedRes?.[0] })

  return {
    statusCode: 200,
    body: { message: 'Inicio de sesión exitoso', token, user: formatedRes?.[0] }
  }
})
