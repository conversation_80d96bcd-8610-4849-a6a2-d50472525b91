import dayjs from 'dayjs'
import 'dayjs/locale/es'
import updateLocale from 'dayjs/plugin/updateLocale'
import utc from 'dayjs/plugin/utc'
import isoWeek from 'dayjs/plugin/isoWeek'
import timezone from 'dayjs/plugin/timezone'

dayjs.extend(updateLocale)
dayjs.extend(timezone)
dayjs.extend(isoWeek)
dayjs.extend(utc)
dayjs.updateLocale('es', { weekStart: 1 })

export default defineNuxtPlugin(() => ({ provide: { dayjs } }))
