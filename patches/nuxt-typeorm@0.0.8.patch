diff --git a/node_modules/nuxt-typeorm/.bun-tag-53441d2d635e2094 b/.bun-tag-53441d2d635e2094
new file mode 100644
index 0000000000000000000000000000000000000000..e69de29bb2d1d6434b8b29ae775ad8c2e48c5391
diff --git a/dist/module.mjs b/dist/module.mjs
index 5a49c9cec1e686bc201ed622886e1345022b65d2..6f1286fe15fe085d6b0b0d2c60e5205918fea7c8 100644
--- a/dist/module.mjs
+++ b/dist/module.mjs
@@ -21,8 +21,6 @@ const module = defineNuxtModule({
       _config.alias = _config.alias || {};
       _config.alias["#typeorm"] = resolve("./runtime/utilities");
     });
-    addServerImportsDir(nuxt.options.serverDir + "/entities");
-    addServerImportsDir(nuxt.options.serverDir + "/migrations");
     addServerPlugin(resolve("./runtime/plugin"));
   }
 });
diff --git a/dist/runtime/connection.mjs b/dist/runtime/connection.mjs
index a89e9de4f550352875c41b63b01b059aae988dc3..01c809f286252fe1886a1cdd484ee2a72a903c25 100644
--- a/dist/runtime/connection.mjs
+++ b/dist/runtime/connection.mjs
@@ -1,10 +1,16 @@
-import { entities, migrations, useRuntimeConfig } from "#imports";
+import { useRuntimeConfig } from "#imports";
 import { DataSource } from "typeorm";
 let AppDataSource;
 export async function useDatasource() {
   const config = useRuntimeConfig().typeorm;
   if (!AppDataSource) {
-    AppDataSource = new DataSource({ ...config, entities, migrations });
+    AppDataSource = new DataSource({ ...config,  options: {
+        trustServerCertificate: true,
+        encrypt: false,
+        useUTC: true,
+        enableArithAbort: true
+      }
+    });
     await AppDataSource.initialize();
   }
   if (!AppDataSource.isInitialized) {
