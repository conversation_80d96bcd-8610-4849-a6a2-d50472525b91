import { fileURLToPath, URL } from 'node:url'
import { defineNuxtConfig } from 'nuxt/config'

export default defineNuxtConfig({
  compatibilityDate: '2024-10-08',
  app: {
    head: {
      title: 'Timesheets',
      link: [{ rel: 'icon', type: 'image/png', href: '/favicon.png' }]
    }
  },
  plugins: ['@p/dayjs.ts'],
  devtools: { enabled: true },
  typeorm: {
    type: 'mssql',
    host: '************',
    //host: '********',
    username: 'sa',
    database: 'SERGIO',
    port: 1433,
    synchronize: false,
    logging: true,
    password: '&&&JA4182&&&'
  },
  ssr: false,
  modules: [
    '@nuxt/image',
    '@nuxt/scripts',
    '@pinia/nuxt',
    'nuxt-svgo',
    '@vueuse/nuxt',
    'nuxt-headlessui',
    'nuxt-typeorm',
    'nuxt-lucide-icons',
    //'nuxt-auth-utils'
    '@peterbud/nuxt-query'
  ],
  css: ['./src/app/core/resources/app.css'],
  postcss: {
    plugins: {
      'postcss-import': {},
      'postcss-advanced-variables': {},
      'tailwindcss/nesting': {},
      tailwindcss: {},
      autoprefixer: {}
    }
  },
  srcDir: 'src/app',
  build: { transpile: ['@vuepic/vue-datepicker'] },
  serverDir: 'src/server',
  vite: {
    optimizeDeps: { include: ['tailwind-config'] },
    resolve: {
      alias: {
        'tailwind-config': fileURLToPath(
          new URL('./tailwind.config.js', import.meta.url)
        )
      }
    },
    build: {
      commonjsOptions: { include: ['tailwind.config.js', 'node_modules/**'] }
    }
  },
  alias: {
    '~/*': '../src/app/core/*',
    '~': '../src/app/core',
    '@/*': '../src/app/features/*',
    '@': '../src/app/features',
    '@c/*': '../src/app/features/common/*',
    '@c': '../src/app/features/common',
    '@d/*': '../src/app/features/dashboard/*',
    '@d': '../src/app/features/dashboard',
    '@s/*': '../src/server/*',
    '@s': '../src/server',
    '@p/*': '../src/plugins/*',
    '@p': '../src/plugins'
  },
  typescript: {
    strict: true,
    typeCheck: false,
    tsConfig: {
      compilerOptions: {
        allowUnreachableCode: false,
        allowUnusedLabels: false,
        exactOptionalPropertyTypes: false,
        skipLibCheck: true,
        noFallthroughCasesInSwitch: true,
        noImplicitOverride: true,
        noImplicitReturns: true,
        noPropertyAccessFromIndexSignature: false,
        noUncheckedIndexedAccess: true,
        emitDecoratorMetadata: true,
        experimentalDecorators: true,
        strictPropertyInitialization: false,
        noUnusedLocals: true,
        noUnusedParameters: true
      }
    }
  }
})
