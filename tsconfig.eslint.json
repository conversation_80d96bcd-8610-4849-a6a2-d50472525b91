{"compilerOptions": {"allowJs": true, "noEmit": true, "composite": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "allowUnreachableCode": false, "allowUnusedLabels": false, "exactOptionalPropertyTypes": false, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": true, "noUnusedLocals": true, "noUnusedParameters": true, "strict": true}, "exclude": ["node_modules", "template", "dist"], "include": [".eslintrc.cjs", "src/**/*.ts", "src/**/*.vue", "nuxt.config.ts"]}