{"name": "timesheets", "type": "module", "private": true, "scripts": {"build": "bunx --bun nuxt build", "clean": "rimraf .nuxt && rimraf .output && rimraf bun.lock && rimraf node_modules && bun i", "dev": "nuxt dev", "generate": "nuxt generate", "lint": "eslint . --ext .ts,.vue --ignore-path .gitignore", "postinstall": "nuxt prepare", "prettier": "prettier --write .", "preview": "nuxt preview", "typecheck": "tsc", "unused": "knip --max-issues 1"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@left4code/tw-starter": "^5.0.3", "@nuxt/image": "1.11.0", "@nuxt/scripts": "0.13.0", "@peterbud/nuxt-query": "^1.2.0", "@pinia/nuxt": "0.11.2", "@tanstack/vue-query": "^5.90.5", "@vuepic/vue-datepicker": "^11.0.3", "@vueuse/nuxt": "^13.9.0", "arktype": "^2.1.23", "dayjs": "^1.11.18", "exceljs": "^4.4.0", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "mssql": "^12.0.0", "nanoid": "^5.1.6", "nodemailer": "^7.0.9", "nuxt": "4.1.3", "nuxt-auth-utils": "0.5.25", "nuxt-headlessui": "1.2.1", "nuxt-lucide-icons": "2.0.0", "nuxt-svgo": "4.2.6", "nuxt-typeorm": "^0.0.8", "pdfkit": "^0.17.2", "pinia": "^3.0.3", "rambdax": "^11.3.1", "simplebar": "^6.3.2", "tippy.js": "^6.3.7", "typeorm": "^0.3.27", "vue": "3.5.22", "vue-router": "^4.6.3"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@types/eslint": "8.56.12", "@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^7.0.2", "@types/pdfkit": "^0.17.3", "@typescript-eslint/eslint-plugin": "^8.46.1", "@typescript-eslint/parser": "^8.46.1", "autoprefixer": "^10.4.21", "eslint": "8.57.1", "eslint-config-prettier": "^10.1.8", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-functional": "6.6.3", "eslint-plugin-import": "^2.32.0", "eslint-plugin-perfectionist": "^4.15.1", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-security": "^3.0.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-sonarjs": "^3.0.5", "eslint-plugin-tailwindcss": "^3.18.2", "eslint-plugin-toplevel": "^1.2.0", "eslint-plugin-unicorn": "56.0.1", "eslint-plugin-vue": "^10.5.1", "knip": "^5.66.1", "postcss": "^8.5.6", "postcss-advanced-variables": "3.0.1", "postcss-import": "^16.1.1", "prettier": "^3.6.2", "prettier-plugin-pkg": "^0.21.2", "prettier-plugin-sh": "^0.18.0", "rimraf": "^6.0.1", "tailwind-merge": "^3.3.1", "tailwindcss": "3.4.18", "vue-eslint-parser": "^10.2.0"}, "patchedDependencies": {"nuxt-typeorm@0.0.8": "patches/<EMAIL>"}}