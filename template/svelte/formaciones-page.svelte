<script lang="ts">
  import Button from '@c/components/button/Button.svelte'
  import Lucide from '@c/components/Lucide.svelte'
  import Pagination from '@d/components/pagination/Pagination.svelte'
  import Table from '@d/components/table/Table.svelte'
  import { FormacionesRepository } from '@d/repository/formaciones-repository'
  import { mapObjIndexed } from 'rambdax'

  import { FormInput, FormSelect } from '@/common/components/form'
  //import LoadingIcon from '@/common/components/loading/LoadingIcon.svelte'
  import type { FormacEjecUpdateSchema } from '&server/formac-ejec/formac-ejec.schema'
  import type { FormacionesJoinedSchema } from '&server/formaciones/formaciones.schema'
  import { container } from '~/modules/di-module'

  import { defaultFormacEjecs } from '../../repository/formac-ejec-repository'
  import FormacEjecActions from './components/formacejec-actions.svelte'
  import FormacionesActions from './components/formaciones-actions.svelte'
  import Paginator from './components/paginator.svelte'

  const { getFormaciones } = container.get(FormacionesRepository)
  const formacionCols = ['Formación', 'Descripción', 'Categoría', 'Acciones']

  const itemsPerFormacionPage = 15
  const assignNullableFormacEjecs = (): Partial<FormacEjecUpdateSchema> =>
    mapObjIndexed(
      (value, key) => {
        if (key === 'fechaFin') return value
        if (key === 'fechaInicio') return value
        return null
      },
      { ...defaultFormacEjecs }
    )

  let formaciones: FormacionesJoinedSchema[] = []

  // eslint-disable-next-line unicorn/prefer-top-level-await
  let formacionesPromise = getFormaciones().then(data => {
    formaciones = data
    return data
  })

  let showFormacionesSlide = false
  let showFormacEjecSlide = false
  let formacionColSelected = 'Formación'
  let formacionDebouncedQuery = ''
  let formacionSearch = ''
  let formacionSelected: FormacionesJoinedSchema | null = null
  let formacEjecSelected = assignNullableFormacEjecs()
  let currentFormacionPage = 1

  let formacionGetted: {
    formacEjecs: FormacEjecUpdateSchema[]
    formacion: string
    formacionId: number
  } | null = null
  let timer: ReturnType<typeof setTimeout> | null = null

  $: formacionesFiltered = formaciones.filter(formacion => {
    switch (formacionColSelected) {
      case 'Categoría': {
        return formacion.categorias?.includes(formacionDebouncedQuery) ?? false
      }
      case 'Descripción': {
        return formacion.descCorta?.includes(formacionDebouncedQuery) ?? false
      }
      case 'Formación': {
        return formacion.descLarga?.includes(formacionDebouncedQuery) ?? false
      }
      default: {
        return true
      }
    }
  })

  $: formacionPages = Math.ceil(formaciones.length / itemsPerFormacionPage)
  $: paginatedFormaciones = formaciones.slice(
    (currentFormacionPage - 1) * itemsPerFormacionPage,
    currentFormacionPage * itemsPerFormacionPage
  )

  const onFormacionEdit = (formac: FormacionesJoinedSchema) => {
    formacionSelected = formac
    showFormacionesSlide = false
    setTimeout(() => {
      showFormacionesSlide = true
    }, 100)
  }

  const onFormacionEjecEdit = (formacEdit: FormacEjecUpdateSchema) => {
    formacEjecSelected = formacEdit
    showFormacEjecSlide = false
    setTimeout(() => {
      showFormacEjecSlide = true
    }, 100)
  }

  const debounce = (value: string) => {
    if (timer !== null) clearTimeout(timer)
    timer = setTimeout(() => {
      formacionDebouncedQuery = value
    }, 750)
  }

  const nextFormacionPage = () => {
    if (
      currentFormacionPage <
      Math.ceil(formaciones.length / itemsPerFormacionPage)
    )
      currentFormacionPage++
  }

  const prevFormacionPage = () => {
    if (currentFormacionPage > 1) currentFormacionPage--
  }
</script>

<div>
  <div class="flex items-center mt-8 intro-y">
    <h2 class="mr-auto text-2xl font-medium">Eventos de Formación</h2>
  </div>
  <div class="grid grid-cols-12 gap-6 mt-5 intro-y">
    <div class="col-span-full">
      <div class="mt-5 intro-y box">
        <div
          class="flex flex-col items-center p-5 border-b sm:flex-row border-slate-200/60"
        >
          <h2 class="mr-auto text-base font-medium">Formaciones</h2>
          <div class="flex flex-col items-center sm:flex-row gap-5">
            <FormSelect
              class="sm:mr-2"
              aria-label="Default select example"
              bind:value={formacionColSelected}
            >
              {#each formacionCols.filter(col => col !== 'Acciones') as col}
                <option>{col}</option>
              {/each}
            </FormSelect>
            <div class="relative w-56 text-slate-500">
              <FormInput
                type="text"
                class="w-56 pr-10 !box"
                bind:value={formacionSearch}
                on:keyup={() => debounce(formacionSearch)}
                placeholder="Buscar..."
              />
              <Lucide
                icon="Search"
                class="absolute inset-y-0 right-0 w-4 h-4 my-auto mr-3"
              />
            </div>
          </div>
        </div>
        <!-- eslint-disable-next-line unicorn/prefer-top-level-await  -->
        {#await formacionesPromise}
          <div
            class="flex flex-col items-center m-5 justify-end col-span-6 sm:col-span-3 xl:col-span-2"
          >
            <!-- <LoadingIcon icon="three-dots" class="w-8 h-8" /> -->
          </div>
        {:then}
          {#if formacionesFiltered.length > 0}
            <div class="p-5">
              <div class="overflow-auto max-h-[360px]">
                <Table hover sm>
                  <Table.Thead variant="dark">
                    <Table.Tr>
                      {#each formacionCols as col}
                        <Table.Th class="whitespace-nowrap font-bold"
                          >{col}</Table.Th
                        >
                      {/each}
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {#each paginatedFormaciones as formacion}
                      <Table.Tr
                        key={formacion.id}
                        on:click={() => {
                          formacionGetted = {
                            formacEjecs: formacion.formacEjecs,
                            formacion: formacion.descLarga ?? '',
                            formacionId: formacion.id
                          }
                        }}
                      >
                        <Table.Td>{formacion.descLarga ?? '---'}</Table.Td>
                        <Table.Td>{formacion.descCorta ?? '---'}</Table.Td>
                        <Table.Td>{formacion.categorias ?? '---'}</Table.Td>
                        <Table.Td className="flex w-max">
                          <Button
                            variant="primary"
                            className="px-auto"
                            on:click={() => onFormacionEdit(formacion)}
                          >
                            <Lucide icon="Pencil" class="w-5 h-5" />
                          </Button></Table.Td
                        >
                      </Table.Tr>
                    {/each}
                  </Table.Tbody>
                </Table>
              </div>
              <div class="flex flex-row w-full justify-between">
                <Paginator
                  currentPage={currentFormacionPage}
                  ceilPages={formacionPages}
                  onNextPage={nextFormacionPage}
                  onPrevPage={prevFormacionPage}
                  onSelectedPage={page => (currentFormacionPage = page)}
                  onFirstPage={() => (currentFormacionPage = 1)}
                  onLastPage={() => (currentFormacionPage = formacionPages)}
                />
                <div class="mt-6">
                  <Button
                    as="a"
                    href="#top"
                    variant="primary"
                    class="w-24 mr-1"
                    on:click={event => {
                      event.preventDefault()
                      showFormacionesSlide = false
                      setTimeout(() => {
                        showFormacionesSlide = true
                      }, 100)
                    }}
                  >
                    Nuevo
                  </Button>
                </div>
              </div>
            </div>
          {:else}
            <div class="p-10">
              <h3 class="text-center font-bold text-xl">
                No hay formaciones disponibles para mostrar
              </h3>
            </div>
          {/if}
        {/await}
      </div>

      {#if formacionGetted !== null && formacionGetted.formacEjecs.length > 0}
        <div class="mt-5 intro-y box">
          <div
            class="flex flex-col items-center p-5 border-b sm:flex-row border-slate-200/60"
          >
            <h2 class="mr-auto text-base font-medium">Capacitaciones</h2>
          </div>
          <div class="p-5">
            <div class="overflow-x-auto">
              <Table hover sm>
                <Table.Thead variant="dark">
                  <Table.Tr>
                    <Table.Th class="whitespace-nowrap font-bold"
                      >Formación</Table.Th
                    >
                    <Table.Th class="whitespace-nowrap font-bold"
                      >Inicio</Table.Th
                    >
                    <Table.Th class="whitespace-nowrap font-bold">Fin</Table.Th>
                    <Table.Th class="whitespace-nowrap font-bold"
                      >Proveedor</Table.Th
                    >
                    <Table.Th class="whitespace-nowrap font-bold"
                      >Núm. Horas</Table.Th
                    >
                    <Table.Th class="whitespace-nowrap font-bold"
                      >Acciones</Table.Th
                    >
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {#each formacionGetted.formacEjecs as formacionEjec}
                    <Table.Tr>
                      <Table.Td>{formacionGetted.formacion}</Table.Td>
                      <Table.Td>{formacionEjec.fechaInicio}</Table.Td>
                      <Table.Td>{formacionEjec.fechaFin}</Table.Td>
                      <Table.Td>{formacionEjec.proveedor}</Table.Td>
                      <Table.Td>{formacionEjec.horas}</Table.Td>
                      <Table.Td className="flex w-max">
                        <Button
                          variant="primary"
                          className="px-auto"
                          on:click={() =>
                            onFormacionEjecEdit({
                              ...formacionEjec,
                              idFormacion: formacionGetted?.formacionId
                            })}
                        >
                          <Lucide icon="Pencil" class="w-5 h-5" />
                        </Button>
                      </Table.Td>
                    </Table.Tr>
                  {/each}
                </Table.Tbody>
              </Table>
            </div>
            <div class="flex flex-row w-full justify-between">
              <Pagination class="w-max sm:w-auto mt-6">
                <Pagination.Link>
                  <Lucide icon="ChevronsLeft" class="w-4 h-4" />
                </Pagination.Link>
                <Pagination.Link>
                  <Lucide icon="ChevronLeft" class="w-4 h-4" />
                </Pagination.Link>
                <Pagination.Link active>1</Pagination.Link>
                <Pagination.Link>2</Pagination.Link>
                <Pagination.Link>3</Pagination.Link>
                <Pagination.Link>
                  <Lucide icon="ChevronRight" class="w-4 h-4" />
                </Pagination.Link>
                <Pagination.Link>
                  <Lucide icon="ChevronsRight" class="w-4 h-4" />
                </Pagination.Link>
              </Pagination>
              <div class="mt-6">
                <Button
                  as="a"
                  href="#top"
                  variant="primary"
                  class="w-24 mr-1"
                  on:click={event => {
                    event.preventDefault()
                    showFormacEjecSlide = false
                    formacEjecSelected = {
                      ...assignNullableFormacEjecs(),
                      // eslint-disable-next-line no-undefined
                      id: undefined,
                      idFormacion: formacionGetted?.formacionId
                    }
                    setTimeout(() => {
                      showFormacEjecSlide = true
                    }, 100)
                  }}
                >
                  Nuevo
                </Button>
              </div>
            </div>
          </div>
        </div>
      {/if}
      <FormacionesActions
        showSlide={showFormacionesSlide}
        formacion={{
          categorias: formacionSelected?.categorias,
          descCorta: formacionSelected?.descCorta,
          descLarga: formacionSelected?.descLarga,
          id: formacionSelected?.id
        }}
        callbackSuccessClosed={() => {
          showFormacionesSlide = false
          formacionSelected = null
          formacionesPromise = getFormaciones().then(data => {
            formaciones = data
            return data
          })
        }}
        callbackClosed={() => {
          showFormacionesSlide = false
          formacionSelected = null
        }}
      />
      <FormacEjecActions
        showSlide={showFormacEjecSlide}
        formacEjec={formacEjecSelected}
        callbackSuccessClosed={() => {
          showFormacEjecSlide = false
          formacEjecSelected = assignNullableFormacEjecs()
          formacionesPromise = getFormaciones().then(data => {
            formaciones = data
            return data
          })
        }}
        callbackClosed={() => {
          showFormacEjecSlide = false
          formacEjecSelected = assignNullableFormacEjecs()
        }}
      />
    </div>
  </div>
</div>
