<script lang="ts">
  import Lucide from '@c/components/Lucide.svelte'
  import Pagination from '@d/components/pagination/Pagination.svelte'

  export let currentPage = 1
  export let ceilPages = 1
  export let onNextPage: () => void
  export let onPrevPage: () => void
  export let onFirstPage: () => void
  export let onLastPage: () => void
  export let onSelectedPage: (page: number) => void

  console.log(ceilPages)
</script>

<Pagination class="w-max sm:w-auto mt-6">
  <Pagination.Link
    as="a"
    href="#top"
    on:click={_ => {
      onFirstPage()
      console.log('first page')
    }}
  >
    <Lucide icon="ChevronsLeft" class="w-4 h-4" />
  </Pagination.Link>
  <Pagination.Link as="a" href="#top" on:click={_ => onPrevPage()}>
    <Lucide icon="ChevronLeft" class="w-4 h-4" />
  </Pagination.Link>

  {#each Array.from({ length: ceilPages }, (_, i) => i + 1) as _, i}
    <Pagination.Link
      active={currentPage === i + 1}
      on:click={_ => onSelectedPage(i + 1)}
      as="a"
      href="#top"
    >
      {i + 1}
    </Pagination.Link>
  {/each}
  <Pagination.Link as="a" href="#top" on:click={_ => onNextPage()}>
    <Lucide icon="ChevronRight" class="w-4 h-4" />
  </Pagination.Link>
  <Pagination.Link as="a" href="#top" on:click={_ => onLastPage()}>
    <Lucide icon="ChevronsRight" class="w-4 h-4" />
  </Pagination.Link>
</Pagination>
