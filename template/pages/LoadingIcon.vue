<script setup lang="ts">
import LoadingIcon from '@/components/Base/LoadingIcon'
</script>

<template>
  <div class="grid grid-cols-12 px-5 py-8 mt-5 intro-y sm:gap-6 gap-y-6 box">
    <div
      class="flex flex-col items-center justify-end col-span-6 sm:col-span-3 xl:col-span-2"
    >
      <LoadingIcon icon="bars" class="w-8 h-8" />
      <div class="mt-2 text-xs text-center">bars</div>
    </div>
    <div
      class="flex flex-col items-center justify-end col-span-6 sm:col-span-3 xl:col-span-2"
    >
      <LoadingIcon icon="grid" class="w-8 h-8" />
      <div class="mt-2 text-xs text-center">grid</div>
    </div>
    <div
      class="flex flex-col items-center justify-end col-span-6 sm:col-span-3 xl:col-span-2"
    >
      <LoadingIcon icon="oval" class="w-8 h-8" />
      <div class="mt-2 text-xs text-center">oval</div>
    </div>
    <div
      class="flex flex-col items-center justify-end col-span-6 sm:col-span-3 xl:col-span-2"
    >
      <LoadingIcon icon="puff" class="w-8 h-8" />
      <div class="mt-2 text-xs text-center">puff</div>
    </div>
    <div
      class="flex flex-col items-center justify-end col-span-6 sm:col-span-3 xl:col-span-2"
    >
      <LoadingIcon icon="three-dots" class="w-8 h-8" />
      <div class="mt-2 text-xs text-center">three-dots</div>
    </div>
  </div>
</template>
